<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.znhb</groupId>
        <artifactId>znhb-plugin</artifactId>
        <version>2.0.0</version>
    </parent>

    <artifactId>znhb-plugin-wechat</artifactId>
    <packaging>jar</packaging>
    <description>微信公众号插件</description>

    <dependencies>
        <!-- 每个插件都要引入自己的对外接口 -->
        <dependency>
            <groupId>com.znhb</groupId>
            <artifactId>znhb-plugin-wechat-api</artifactId>
        </dependency>

        <!-- 引入登录鉴权接口，用于获取登录用户 -->
        <dependency>
            <groupId>com.znhb</groupId>
            <artifactId>znhb-plugin-auth-api</artifactId>
        </dependency>

        <!-- 引入系统接口，用于授权角色等功能 -->
        <dependency>
            <groupId>com.znhb</groupId>
            <artifactId>znhb-plugin-sys-api</artifactId>
        </dependency>

        <!-- 引入开发工具接口，用于配置信息 -->
        <dependency>
            <groupId>com.znhb</groupId>
            <artifactId>znhb-plugin-dev-api</artifactId>
        </dependency>

        <!-- 动态数据源 -->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
        </dependency>
    </dependencies>
</project>
