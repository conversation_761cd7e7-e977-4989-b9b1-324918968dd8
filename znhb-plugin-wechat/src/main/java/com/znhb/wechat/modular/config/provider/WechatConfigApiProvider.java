/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package com.znhb.wechat.modular.config.provider;

import com.znhb.dev.api.DevConfigApi;
import com.znhb.wechat.api.WechatConfigApi;
import me.chanjar.weixin.mp.api.WxMpService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 微信公众号配置API实现类
 *
 * <AUTHOR>
 * @date 2024/01/01 10:00
 **/
@Service
public class WechatConfigApiProvider implements WechatConfigApi {

    @Resource
    private WxMpService wxMpService;

    @Resource
    private DevConfigApi devConfigApi;

    /** 微信公众号配置键名 */
    private static final String WECHAT_MP_APP_ID = "WECHAT_MP_APP_ID";
    private static final String WECHAT_MP_APP_SECRET = "WECHAT_MP_APP_SECRET";
    private static final String WECHAT_MP_TOKEN = "WECHAT_MP_TOKEN";
    private static final String WECHAT_MP_ENCODING_AES_KEY = "WECHAT_MP_ENCODING_AES_KEY";

    @Override
    public WxMpService getWxMpService() {
        return wxMpService;
    }

    @Override
    public String getAppId() {
        return devConfigApi.getValueByKey(WECHAT_MP_APP_ID);
    }

    @Override
    public String getAppSecret() {
        return devConfigApi.getValueByKey(WECHAT_MP_APP_SECRET);
    }

    @Override
    public String getToken() {
        return devConfigApi.getValueByKey(WECHAT_MP_TOKEN);
    }

    @Override
    public String getEncodingAESKey() {
        return devConfigApi.getValueByKey(WECHAT_MP_ENCODING_AES_KEY);
    }
}
