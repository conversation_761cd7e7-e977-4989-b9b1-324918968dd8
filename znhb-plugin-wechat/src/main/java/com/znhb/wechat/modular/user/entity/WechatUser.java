/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package com.znhb.wechat.modular.user.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.znhb.common.pojo.CommonEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * 微信用户实体
 *
 * <AUTHOR>
 * @date 2024/01/01 10:00
 **/
@Getter
@Setter
@TableName("wx_user")
@ApiModel(value = "微信用户")
public class WechatUser extends CommonEntity {

    /** id */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id", position = 1)
    private String id;

    /** 微信openId */
    @ApiModelProperty(value = "微信openId", position = 2)
    private String openId;

    /** 微信unionId */
    @ApiModelProperty(value = "微信unionId", position = 3)
    private String unionId;

    /** 昵称 */
    @ApiModelProperty(value = "昵称", position = 4)
    private String nickname;

    /** 性别 */
    @ApiModelProperty(value = "性别", position = 5)
    private String sex;

    /** 头像 */
    @ApiModelProperty(value = "头像", position = 6)
    private String headImgUrl;

    /** 国家 */
    @ApiModelProperty(value = "国家", position = 7)
    private String country;

    /** 省份 */
    @ApiModelProperty(value = "省份", position = 8)
    private String province;

    /** 城市 */
    @ApiModelProperty(value = "城市", position = 9)
    private String city;

    /** 语言 */
    @ApiModelProperty(value = "语言", position = 10)
    private String language;

    /** 关注状态 */
    @ApiModelProperty(value = "关注状态", position = 11)
    private String subscribeStatus;

    /** 关注时间 */
    @ApiModelProperty(value = "关注时间", position = 12)
    private Date subscribeTime;

    /** 绑定的系统用户ID */
    @ApiModelProperty(value = "绑定的系统用户ID", position = 13)
    private String bindUserId;

    /** 绑定时间 */
    @ApiModelProperty(value = "绑定时间", position = 14)
    private Date bindTime;

    /** 备注 */
    @ApiModelProperty(value = "备注", position = 15)
    private String remark;

    /** 扩展字段 */
    @TableField(exist = false)
    @ApiModelProperty(value = "扩展字段", position = 16)
    private String extJson;
}
