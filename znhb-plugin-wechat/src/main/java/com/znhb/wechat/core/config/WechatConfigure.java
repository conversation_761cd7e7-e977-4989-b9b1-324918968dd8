/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package com.znhb.wechat.core.config;

import com.znhb.common.pojo.CommonResult;
import com.znhb.dev.api.DevConfigApi;
import io.swagger.annotations.ApiOperation;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.api.impl.WxMpServiceImpl;
import me.chanjar.weixin.mp.config.impl.WxMpDefaultConfigImpl;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.bind.annotation.RequestMethod;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.service.Contact;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;

import javax.annotation.Resource;

/**
 * 微信公众号相关配置
 *
 * <AUTHOR>
 * @date 2024/01/01 10:00
 **/
@Configuration
public class WechatConfigure {

    @Resource
    private DevConfigApi devConfigApi;

    /** 微信公众号配置键名 */
    private static final String WECHAT_MP_APP_ID = "WECHAT_MP_APP_ID";
    private static final String WECHAT_MP_APP_SECRET = "WECHAT_MP_APP_SECRET";
    private static final String WECHAT_MP_TOKEN = "WECHAT_MP_TOKEN";
    private static final String WECHAT_MP_ENCODING_AES_KEY = "WECHAT_MP_ENCODING_AES_KEY";

    /**
     * 微信公众号服务配置
     *
     * @return WxMpService
     * <AUTHOR>
     * @date 2024/01/01 10:00
     **/
    @Bean
    public WxMpService wxMpService() {
        WxMpDefaultConfigImpl config = new WxMpDefaultConfigImpl();
        config.setAppId(devConfigApi.getValueByKey(WECHAT_MP_APP_ID));
        config.setSecret(devConfigApi.getValueByKey(WECHAT_MP_APP_SECRET));
        config.setToken(devConfigApi.getValueByKey(WECHAT_MP_TOKEN));
        config.setAesKey(devConfigApi.getValueByKey(WECHAT_MP_ENCODING_AES_KEY));

        WxMpService wxMpService = new WxMpServiceImpl();
        wxMpService.setWxMpConfigStorage(config);
        return wxMpService;
    }

    /**
     * API文档分组配置
     *
     * <AUTHOR>
     * @date 2024/01/01 10:00
     **/
    @Bean(value = "wechatDocApi")
    public Docket wechatDocApi() {
        return new Docket(DocumentationType.SWAGGER_2)
                .apiInfo(new ApiInfoBuilder()
                        .title("微信公众号WECHAT")
                        .description("微信公众号WECHAT")
                        .termsOfServiceUrl("https://www.xiaonuo.vip")
                        .contact(new Contact("ZNHB_TEAM","https://www.xiaonuo.vip", "<EMAIL>"))
                        .version("2.0.0")
                        .build())
                .globalResponseMessage(RequestMethod.GET, CommonResult.responseList())
                .globalResponseMessage(RequestMethod.POST, CommonResult.responseList())
                .groupName("微信公众号WECHAT")
                .select()
                .apis(RequestHandlerSelectors.withMethodAnnotation(ApiOperation.class))
                .apis(RequestHandlerSelectors.basePackage("com.znhb.wechat"))
                .paths(PathSelectors.any())
                .build();
    }
}
