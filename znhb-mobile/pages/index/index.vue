<template>
	<view class="container">
		<scroll-view scroll-y class="content">
			<view v-for="module in menuList" :key="module.id" class="module-section">
				<uni-card :title="module.title" :extra="module.children.length + '个应用'" class="module-card">
					<template v-slot:header>
						<view class="module-header">
							<uni-icons :type="module.meta.icon || 'folder'" size="22" :color="module.color || '#2979ff'"></uni-icons>
							<text class="module-title">{{ module.title }}</text>
						</view>
					</template>
					
					<view class="menu-grid">
						<view 
							v-for="menu in module.children" 
							:key="menu.id" 
							class="menu-item"
							@tap="handleMenuClick(menu)"
							:style="{ backgroundColor: menu.iconColor ? `${menu.iconColor}10` : '#f5f7fa' }"
						>
							<view class="menu-icon" :style="{ backgroundColor: menu.iconColor }">
								<text class="icon-text">{{ menu.title.charAt(0) }}</text>
							</view>
							<text class="menu-title">{{ menu.title }}</text>
						</view>
					</view>
				</uni-card>
			</view>
		</scroll-view>
	</view>
</template>

<script>
import tool from '@/utils/tool.js'
import config from '@/config/index.js'

export default {
	data() {
		return {
			config,
			menuList: [],
			// 预定义一组好看的颜色
			colorList: [
				'#2979ff', // 蓝色
				'#19be6b', // 绿色
				'#fa436a', // 红色
				'#ff9900', // 橙色
				'#9c27b0', // 紫色
				'#3f51b5', // 靛蓝
				'#009688', // 青色
				'#607d8b', // 蓝灰
				'#e51c23', // 玫红
				'#259b24'  // 深绿
			]
		}
	},
	onShow() {
		this.getMenuData()
	},
	methods: {
		// 获取菜单数据
		getMenuData() {
			// 先尝试从本地存储获取带颜色的菜单数据
			const coloredMenu = tool.data.get('COLORED_MENU')
			if (coloredMenu) {
				this.menuList = coloredMenu
				return
			}

			// 如果本地没有带颜色的菜单数据，则重新生成
			const menuData = tool.data.get('MENU') || []
			// 过滤并为每个菜单项分配随机颜色
			this.menuList = menuData.filter(item => {
				return item.category === 'MODULE' && item.children && item.children.length > 0
			}).map(module => {
				// 为每个子菜单分配随机颜色
				module.children = module.children.map(menu => ({
					...menu,
					iconColor: this.getRandomColor()
				}))
				return module
			})

			// 将带颜色的菜单数据保存到本地存储
			tool.data.set('COLORED_MENU', this.menuList)
		},
		
		// 获取随机颜色
		getRandomColor() {
			return this.colorList[Math.floor(Math.random() * this.colorList.length)]
		},
		
		// 处理菜单点击
		handleMenuClick(menu) {
			if (!menu.path) {
				uni.showToast({
					title: '菜单路径未配置',
					icon: 'none'
				})
				return
			}
			
			uni.navigateTo({
				url: menu.path,
				fail: (err) => {
					console.error('页面跳转失败:', err)
					uni.showToast({
						title: '页面跳转失败',
						icon: 'none'
					})
				}
			})
		}
	}
}
</script>

<style lang="scss">
.container {
	min-height: 100vh;
	background-color: #f5f7fa;
	box-sizing: border-box;
}

.content {
	padding: 20rpx 30rpx;
	box-sizing: border-box;
}

.module-section {
	margin-bottom: 20rpx;
	
	.module-card {
		:deep(.uni-card__header) {
			padding: 24rpx 30rpx;
			border-bottom: 1px solid #ebeef5;
		}
		
		:deep(.uni-card__content) {
			padding: 24rpx 30rpx;
		}
		
		.module-header {
			display: flex;
			align-items: center;
			
			.uni-icons {
				margin-right: 12rpx;
			}
			
			.module-title {
				font-size: 32rpx;
				font-weight: 600;
				color: #2c3e50;
			}
		}
	}
}

.menu-grid {
	display: grid;
	grid-template-columns: repeat(3, 1fr);
	gap: 20rpx;
	width: 100%;
	box-sizing: border-box;
	
	.menu-item {
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 24rpx 16rpx;
		border-radius: 16rpx;
		transition: all 0.3s;
		
		&:active {
			transform: scale(0.95);
			opacity: 0.8;
		}
		
		.menu-icon {
			width: 88rpx;
			height: 88rpx;
			border-radius: 22rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			margin-bottom: 16rpx;
			box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
			transition: transform 0.2s;
			
			.icon-text {
				color: #fff;
				font-size: 32rpx;
				font-weight: bold;
			}
		}
		
		.menu-title {
			font-size: 26rpx;
			color: #2c3e50;
			text-align: center;
		}
	}
}

// 自定义uni-card样式
:deep(.uni-card) {
	margin: 0 !important;
	border-radius: 16rpx;
	box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05) !important;
	border: none !important;
	overflow: hidden;
	width: 100%;
	box-sizing: border-box;
	
	.uni-card__header-extra-text {
		font-size: 24rpx;
		color: #909399;
	}
}
</style>
