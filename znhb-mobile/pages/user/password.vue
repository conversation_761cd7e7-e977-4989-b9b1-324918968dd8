<template>
	<view class="container">
		<uni-card :border="false" class="form-card">
			<uni-forms ref="form" :modelValue="formData" :rules="rules">
				<uni-forms-item label="旧密码" required name="oldPassword">
					<uni-easyinput type="password" v-model="formData.oldPassword" placeholder="请输入旧密码" />
				</uni-forms-item>
				<uni-forms-item label="新密码" required name="newPassword">
					<uni-easyinput type="password" v-model="formData.newPassword" placeholder="请输入新密码" />
				</uni-forms-item>
				<uni-forms-item label="确认密码" required name="confirmPassword">
					<uni-easyinput type="password" v-model="formData.confirmPassword" placeholder="请再次输入新密码" />
				</uni-forms-item>
			</uni-forms>
			
			<view class="btn-group">
				<button class="submit-btn" type="primary" @click="handleSubmit">确认修改</button>
			</view>
		</uni-card>
	</view>
</template>

<script>
import userCenterApi from '../../api/sys/userCenterApi.js';
import tool from '../../utils/tool.js';

export default {
	data() {
		return {
			formData: {
				oldPassword: '',
				newPassword: '',
				confirmPassword: ''
			},
			rules: {
				oldPassword: {
					rules: [{
						required: true,
						errorMessage: '请输入旧密码'
					}]
				},
				newPassword: {
					rules: [{
						required: true,
						errorMessage: '请输入新密码'
					}, {
						minLength: 6,
						errorMessage: '密码长度不能小于6位'
					}]
				},
				confirmPassword: {
					rules: [{
						required: true,
						errorMessage: '请再次输入新密码'
					}, {
						validateFunction: (rule, value, data, callback) => {
							if (value !== this.formData.newPassword) {
								callback('两次输入的密码不一致');
							}
							return true;
						}
					}]
				}
			}
		}
	},
	methods: {
		handleSubmit() {
			this.$refs.form.validate().then(res => {
				const params = {
					oldPassword: this.formData.oldPassword,
					newPassword: this.formData.newPassword
				};
				
				userCenterApi.updateUserPwd(params).then(() => {
					uni.showToast({
						title: '密码修改成功',
						icon: 'success'
					});
					
					// 密码修改成功后退出登录
					setTimeout(() => {
						// 清除登录信息
						tool.data.remove('TOKEN');
						tool.data.remove('USER_INFO');
						tool.data.remove('MENU');
						tool.data.remove('PERMISSIONS');
						tool.data.remove('COLORED_MENU');
						
						// 跳转到登录页
						uni.reLaunch({
							url: '/pages/login/index'
						});
					}, 1500);
				}).catch(err => {
					uni.showToast({
						title: err.message || '修改失败',
						icon: 'none'
					});
				});
			}).catch(err => {
				console.log('表单错误：', err);
			});
		}
	}
}
</script>

<style>
.container {
	min-height: 100vh;
	background-color: #f7f7f7;
	padding: 30rpx;
}

.form-card {
	background: #fff;
	border-radius: 12rpx;
}

.form-card :deep(.uni-forms-item) {
	padding: 20rpx 0;
}

.form-card :deep(.uni-forms-item__label) {
	font-size: 28rpx;
	color: #333;
}

.form-card :deep(.uni-easyinput__content) {
	background-color: #f8f8f8;
	border-radius: 8rpx;
	height: 88rpx;
}

.form-card :deep(.uni-easyinput__content-input) {
	font-size: 28rpx;
}

.btn-group {
	margin-top: 60rpx;
	padding: 0 20rpx;
}

.submit-btn {
	width: 100%;
	height: 88rpx;
	line-height: 88rpx;
	font-size: 32rpx;
	border-radius: 44rpx;
	background: #2979ff;
}

.submit-btn:active {
	opacity: 0.8;
}
</style> 