<template>
	<view class="container">
		<!-- 头部信息 -->
		<uni-card :border="false" class="header-card">
			<view class="header">
				<view class="user-info">
					<image class="avatar" :src="userInfo.avatar || '/static/logo.png'" mode="aspectFill"></image>
					<view class="user-details">
						<text class="username">{{userInfo.name || '微信用户'}}</text>
						<text class="user-role">{{userInfo.orgName || '点击登录'}}</text>
					</view>
				</view>
				<view class="header-actions">
					<view class="action-item" @tap="goToMessage">
						<uni-icons type="notification" size="24" color="#333"></uni-icons>
						<uni-badge :text="unreadCount" type="error" :absolute="true" :offset="[8, -8]" v-if="unreadCount > 0"></uni-badge>
					</view>
				</view>
			</view>
		</uni-card>
		
		<!-- 功能导航 -->
		<view class="nav-section">
			<uni-list>
				<uni-list-item v-for="(item, index) in navItems" 
					:key="index"
					:title="item.name"
					:clickable="true"
					:showArrow="true"
					@click="navigateTo(item.url)"
				>
					<template v-slot:header>
						<view class="nav-icon-wrapper" :style="{ backgroundColor: getIconColor(index) }">
							<uni-icons :type="item.uniIcon" size="20" color="#fff"></uni-icons>
						</view>
					</template>
				</uni-list-item>
			</uni-list>
		</view>

		<!-- 底部版权信息 -->
		<view class="footer">
			<text>{{copyright}}</text>
		</view>
	</view>
</template>

<script>
import tool from '../../utils/tool.js';
import config from '../../config/index.js';
import userCenterApi from '../../api/sys/userCenterApi.js';
import loginApi from '../../api/auth/loginApi.js';

export default {
	data() {
		return {
			userInfo: {},
			unreadCount: 0,
			navItems: [
				{ name: '个人中心', uniIcon: 'person-filled', url: '/pages/user/index' },
				{ name: '消息中心', uniIcon: 'email-filled', url: '/pages/message/index' },
				{ name: '修改密码', uniIcon: 'locked-filled', url: '/pages/user/password' },
				{ name: '退出登录', uniIcon: 'redo-filled', url: '' }
			],
			copyright: config.SYS_BASE_CONFIG.SNOWY_SYS_COPYRIGHT,
			iconColors: [
				'#2979ff',
				'#19be6b',
				'#ff9900',
				'#fa436a'
			]
		}
	},
	onShow() {
		// 获取登录信息
		this.getUserInfo();
		// 获取未读消息数量
		this.getUnreadCount();
	},
	methods: {
		// 获取图标颜色
		getIconColor(index) {
			return this.iconColors[index % this.iconColors.length];
		},
		// 获取用户信息
		getUserInfo() {
			const userInfo = tool.data.get('USER_INFO');
			if (userInfo) {
				this.userInfo = userInfo;
			} else {
				// 如果没有用户信息，跳转到登录页
				uni.redirectTo({
					url: '/pages/login/index'
				});
			}
		},
		
		// 获取未读消息数量
		getUnreadCount() {
			userCenterApi.getUnreadMessageCount().then(count => {
				this.unreadCount = count || 0;
			}).catch(() => {
				this.unreadCount = 0;
			});
		},
		
		// 导航到指定页面
		navigateTo(url) {
			if (!url) {
				// 退出登录
				this.logout();
				return;
			}
			uni.navigateTo({ url });
		},
		
		// 跳转到消息中心
		goToMessage() {
			uni.navigateTo({
				url: '/pages/message/index'
			});
		},
		
		// 退出登录
		logout() {
			uni.showModal({
				title: '提示',
				content: '确定要退出登录吗？',
				success: (res) => {
					if (res.confirm) {
						loginApi.logout().finally(() => {
						  // 清除本地存储
						  tool.data.remove('TOKEN');
						  tool.data.remove('USER_INFO');
						  tool.data.remove('MENU');
						  tool.data.remove('PERMISSIONS');
						  tool.data.remove('COLORED_MENU');
						  
						  // 跳转到登录页
						  uni.reLaunch({
						  	url: '/pages/login/index'
						  });
						});
					}
				}
			});
		}
	}
}
</script>

<style>
.container {
	min-height: 100vh;
	background-color: #f7f7f7;
	padding: 0;
}

.header-card {
	margin: 0;
	border-radius: 0 !important;
	background: linear-gradient(to bottom, #FFD700, #FFB700) !important;
	padding: 80rpx 40rpx 40rpx !important;
}

.header-card :deep(.uni-card__content) {
	padding: 0 !important;
}

.header {
	position: relative;
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 0;
}

.user-info {
	display: flex;
	align-items: center;
	flex: 1;
}

.avatar {
	width: 100rpx;
	height: 100rpx;
	border-radius: 50%;
	margin-right: 24rpx;
	border: 4rpx solid rgba(255, 255, 255, 0.3);
	background-color: #f0f0f0;
}

.user-details {
	display: flex;
	flex-direction: column;
}

.username {
	font-size: 36rpx;
	font-weight: 500;
	color: #333;
	margin-bottom: 8rpx;
}

.user-role {
	font-size: 26rpx;
	color: #666;
}

.header-actions {
	display: flex;
	align-items: center;
}

.action-item {
	width: 60rpx;
	height: 60rpx;
	display: flex;
	justify-content: center;
	align-items: center;
	background: rgba(255, 255, 255, 0.2);
	border-radius: 50%;
}

.nav-section {
	margin-top: 20rpx;
	background: #fff;
}

.nav-section :deep(.uni-list-item) {
	padding: 0 30rpx !important;
	height: 100rpx !important;
}

.nav-section :deep(.uni-list-item__container) {
	padding: 0 !important;
}

.nav-section :deep(.uni-list-item__content-title) {
	font-size: 28rpx !important;
	color: #333 !important;
}

.nav-icon-wrapper {
	width: 40rpx;
	height: 40rpx;
	margin-right: 20rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	color: #fff;
	border-radius: 8rpx;
}

.footer {
	margin-top: 40rpx;
	padding: 30rpx;
	text-align: center;
	color: #999;
	font-size: 24rpx;
}
</style> 