<template>
	<view class="login-container">
		<view class="login-header">
			<image class="logo" :src="config.SYS_BASE_CONFIG.SNOWY_SYS_LOGO" mode="aspectFit"></image>
			<text class="title">{{ config.SYS_BASE_CONFIG.SNOWY_SYS_NAME }}</text>
		</view>
		
		<uni-section title=" " padding class="login-form-section">
			<view class="login-form">
				<uni-easyinput
					class="form-item"
					v-model="loginForm.account"
					placeholder="请输入账号"
					prefixIcon="person"
				/>
				
				<uni-easyinput
					class="form-item"
					v-model="loginForm.password"
					type="password"
					placeholder="请输入密码"
					prefixIcon="locked"
				/>
				
				<view class="form-item captcha-container" v-if="captchaOpen">
					<uni-easyinput
						v-model="loginForm.validCode"
						placeholder="请输入验证码"
						maxlength="4"
						prefixIcon="paperplane"
					/>
					<image 
						class="captcha" 
						:src="validCodeBase64" 
						@tap="refreshCaptcha"
						mode="aspectFit"
					></image>
				</view>
				
				<button 
					class="uni-btn login-btn" 
					:loading="loading" 
					:disabled="loading"
					@tap="handleLogin"
				>
					<uni-icons v-if="!loading" type="login" size="18" color="#fff"></uni-icons>
					<text>{{ loading ? '登录中...' : '登录' }}</text>
				</button>
			</view>
		</uni-section>
		
		<view class="login-footer">
			<text>{{ config.SYS_BASE_CONFIG.SNOWY_SYS_COPYRIGHT }}</text>
		</view>
	</view>
</template>

<script>
import loginApi from '../../api/auth/loginApi.js';
import userCenterApi from '../../api/sys/userCenterApi.js';
import dictApi from '../../api/dev/dictApi.js';
import smCrypto from '../../utils/crypto/smCrypto.js';
import tool from '../../utils/tool.js';
import config from '../../config/index.js';

export default {
	data() {
		return {
			config,
			loginForm: {
				account: 'superAdmin',
				password: '123456',
				validCode: '',
				validCodeReqNo: ''
			},
			captchaOpen: config.SYS_BASE_CONFIG.SNOWY_SYS_DEFAULT_CAPTCHA_OPEN === 'true',
			validCodeBase64: '',
			loading: false
		};
	},
	onLoad() {
		// 检查是否已登录
		const token = tool.data.get('TOKEN');
		if (token) {
			uni.switchTab({
				url: '/pages/index/index'
			});
			return;
		}
		
		// 如果需要验证码，获取验证码
		if (this.captchaOpen) {
			this.refreshCaptcha();
		}
	},
	methods: {
		// 刷新验证码
		refreshCaptcha() {
			loginApi.getPicCaptcha().then(data => {
				this.validCodeBase64 = data.validCodeBase64;
				this.loginForm.validCodeReqNo = data.validCodeReqNo;
			});
		},
		
		// 表单验证
		validateForm() {
			if (!this.loginForm.account) {
				uni.showToast({
					title: '请输入账号',
					icon: 'none'
				});
				return false;
			}
			if (!this.loginForm.password) {
				uni.showToast({
					title: '请输入密码',
					icon: 'none'
				});
				return false;
			}
			if (this.captchaOpen && !this.loginForm.validCode) {
				uni.showToast({
					title: '请输入验证码',
					icon: 'none'
				});
				return false;
			}
			return true;
		},
		
		// 登录成功后的处理
		async afterLogin(loginToken) {
			try {
				// 保存token
				tool.data.set('TOKEN', loginToken);
				
				// 获取登录的用户信息（包含按钮权限）
				const userInfo = await loginApi.getLoginUser();
				// 确保按钮权限数据存在
				if (!userInfo.mobileButtonCodeList) {
					userInfo.mobileButtonCodeList = [];
				}
				tool.data.set('USER_INFO', userInfo);
				tool.data.set('PERMISSIONS', userInfo.mobileButtonCodeList);
				
				// 获取用户菜单
				const menu = await userCenterApi.userLoginMenu();
				tool.data.set('MENU', menu);
				
				// 获取字典
				const dictData = await dictApi.dictTree();
				tool.data.set('DICT_TYPE_TREE_DATA', dictData);
				
				// 所有数据保存成功后，跳转到首页
				uni.switchTab({
					url: '/pages/index/index',
					success: () => {
						console.log('跳转成功');
						this.loading = false;
					},
					fail: (err) => {
						console.error('跳转失败:', err);
						this.loading = false;
					}
				});
			} catch (error) {
				console.error('登录后处理失败:', error);
				this.loading = false;
				uni.showToast({
					title: '登录失败，请重试',
					icon: 'none'
				});
			}
		},
		
		// 登录
		handleLogin() {
			if (this.loading) return;
			
			if (!this.validateForm()) return;
			
			this.loading = true;
			
			// 使用SM2加密密码
			const passwordEncrypt = smCrypto.encrypt(this.loginForm.password);
			
			// 登录请求
			const loginData = {
				account: this.loginForm.account,
				password: passwordEncrypt,
				validCode: this.loginForm.validCode,
				validCodeReqNo: this.loginForm.validCodeReqNo,
				device: 'APP'
			};
			
			loginApi.login(loginData).then(token => {
				// 登录成功后处理
				this.afterLogin(token);
			}).catch((error) => {
				console.error('登录失败:', error);
				this.loading = false;
				if (this.captchaOpen) {
					this.refreshCaptcha();
				}
				uni.showToast({
					title: '登录失败，请重试',
					icon: 'none'
				});
			});
		}
	}
};
</script>

<style lang="scss">
.login-container {
	display: flex;
	flex-direction: column;
	min-height: 100vh;
	background-color: #f8f8f8;
	padding: 40rpx;
}

.login-header {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 60rpx 0;
	
	.logo {
		width: 180rpx;
		height: 180rpx;
		margin-bottom: 30rpx;
	}
	
	.title {
		font-size: 44rpx;
		font-weight: bold;
		color: #333;
	}
}

.login-form-section {
	margin: 20rpx 0;
	
	.login-form {
		background-color: #fff;
		border-radius: 16rpx;
		padding: 40rpx 30rpx;
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
		
		.form-item {
			margin-bottom: 30rpx;
		}
		
		.captcha-container {
			display: flex;
			align-items: center;
			gap: 20rpx;
			
			:deep(.uni-easyinput) {
				flex: 1;
			}
			
			.captcha {
				width: 200rpx;
				height: 80rpx;
				border-radius: 8rpx;
			}
		}
		
		.login-btn {
			width: 100%;
			height: 90rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			gap: 10rpx;
			background-color: #2979ff;
			border: none;
			color: #fff;
			font-size: 32rpx;
			border-radius: 45rpx;
			margin-top: 60rpx;
			
			&:active {
				background-color: #256fe6;
			}
			
			&:disabled {
				background-color: #a0cfff;
			}
		}
	}
}

.login-footer {
	margin-top: auto;
	text-align: center;
	padding: 30rpx 0;
	color: #999;
	font-size: 24rpx;
}

// 自定义uni-easyinput样式
:deep(.uni-easyinput) {
	.uni-easyinput__content {
		min-height: 96rpx;
		border-radius: 12rpx !important;
		background-color: #f5f5f5 !important;
		padding: 0 30rpx;
		
		.uni-easyinput__content-input {
			font-size: 30rpx;
		}
		
		.content-clear-icon {
			margin-right: 10rpx;
		}
	}
}

// 自定义uni-section样式
:deep(.uni-section) {
	.uni-section-header {
		display: none;
	}
}
</style> 