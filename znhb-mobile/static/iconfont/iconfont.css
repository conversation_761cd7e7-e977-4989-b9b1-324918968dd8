@font-face {
  font-family: "iconfont";
  src: url('data:font/woff2;charset=utf-8;base64,BASE64_ENCODED_FONT_CONTENT') format('woff2');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 用户图标 */
.icon-user:before {
  content: "\e7ae";
}

/* 锁/密码图标 */
.icon-lock:before {
  content: "\e7c9";
}

/* 手机图标 */
.icon-mobile:before {
  content: "\e7b2";
}

/* 安全/验证码图标 */
.icon-safetycertificate:before {
  content: "\e7c3";
}

/* 首页图标 */
.icon-home:before {
  content: "\e7c6";
}

/* 设置图标 */
.icon-setting:before {
  content: "\e78e";
}

/* 用户中心图标 */
.icon-user-circle:before {
  content: "\e7af";
}

/* 消息图标 */
.icon-message:before {
  content: "\e7a2";
}

/* 通知图标 */
.icon-notification:before {
  content: "\e7af";
}

/* 退出图标 */
.icon-logout:before {
  content: "\e78d";
}

/* 搜索图标 */
.icon-search:before {
  content: "\e8ef";
}