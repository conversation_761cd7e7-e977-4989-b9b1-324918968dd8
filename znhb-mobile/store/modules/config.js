// store/modules/config.js
import tool from '../../utils/tool';
import config from '../../config/index';

const state = {
  sysBaseConfig: tool.data.get('SNOWY_SYS_BASE_CONFIG') || config.SYS_BASE_CONFIG,
  theme: tool.data.get('APP_THEME') || 'default'
};

const mutations = {
  SET_SYS_BASE_CONFIG(state, sysBaseConfig) {
    state.sysBaseConfig = sysBaseConfig;
    tool.data.set('SNOWY_SYS_BASE_CONFIG', sysBaseConfig);
  },
  SET_THEME(state, theme) {
    state.theme = theme;
    tool.data.set('APP_THEME', theme);
  }
};

const actions = {
  // 设置系统基础配置
  setSysBaseConfig({ commit }, sysBaseConfig) {
    commit('SET_SYS_BASE_CONFIG', sysBaseConfig);
  },
  
  // 设置主题
  setTheme({ commit }, theme) {
    commit('SET_THEME', theme);
  }
};

const getters = {
  sysBaseConfig: state => state.sysBaseConfig,
  theme: state => state.theme
};

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}; 