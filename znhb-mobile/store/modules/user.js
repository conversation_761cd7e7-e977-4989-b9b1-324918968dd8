// store/modules/user.js
import tool from '../../utils/tool';
import loginApi from '../../api/auth/loginApi';

const state = {
  token: tool.data.get('TOKEN') || '',
  userInfo: tool.data.get('USER_INFO') || {},
  menu: tool.data.get('MENU') || []
};

const mutations = {
  SET_TOKEN(state, token) {
    state.token = token;
    tool.data.set('TOKEN', token);
  },
  SET_USER_INFO(state, userInfo) {
    state.userInfo = userInfo;
    tool.data.set('USER_INFO', userInfo);
  },
  SET_MENU(state, menu) {
    state.menu = menu;
    tool.data.set('MENU', menu);
  },
  CLEAR_USER(state) {
    state.token = '';
    state.userInfo = {};
    state.menu = [];
    tool.data.remove('TOKEN');
    tool.data.remove('USER_INFO');
    tool.data.remove('MENU');
  }
};

const actions = {
  // 登录
  login({ commit }, userInfo) {
    return new Promise((resolve, reject) => {
      loginApi.login(userInfo)
        .then(token => {
          commit('SET_TOKEN', token);
          resolve(token);
        })
        .catch(error => {
          reject(error);
        });
    });
  },
  
  // 手机登录
  loginByPhone({ commit }, phoneInfo) {
    return new Promise((resolve, reject) => {
      loginApi.loginByPhone(phoneInfo)
        .then(token => {
          commit('SET_TOKEN', token);
          resolve(token);
        })
        .catch(error => {
          reject(error);
        });
    });
  },
  
  // 获取用户信息
  getUserInfo({ commit }) {
    return new Promise((resolve, reject) => {
      loginApi.getLoginUser()
        .then(userInfo => {
          commit('SET_USER_INFO', userInfo);
          resolve(userInfo);
        })
        .catch(error => {
          reject(error);
        });
    });
  },
  
  // 退出登录
  logout({ commit }) {
    return new Promise((resolve, reject) => {
      loginApi.logout()
        .then(() => {
          commit('CLEAR_USER');
          resolve();
        })
        .catch(error => {
          reject(error);
        });
    });
  }
};

const getters = {
  token: state => state.token,
  userInfo: state => state.userInfo,
  menu: state => state.menu,
  hasToken: state => !!state.token
};

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}; 