import App from './App'
import Vue from 'vue'
import './uni.promisify.adaptor'
import store from './store'
import tool from './utils/tool'

Vue.config.productionTip = false
App.mpType = 'app'

// 全局混入
Vue.mixin({
  beforeCreate() {
    // 将store注入到所有组件
    this.$store = store;
  }
});

// 路由拦截器
const whiteList = ['/pages/login/index']; // 白名单

// 全局导航守卫
function hasPermission(url) {
  const token = tool.data.get('TOKEN');
  // 在白名单中或有token时可以直接访问
  if (whiteList.includes(url) || token) {
    return true;
  }
  return false;
}

// 重写uni-app的导航方法
const originNavigateTo = uni.navigateTo;
uni.navigateTo = function(options) {
  if (hasPermission(options.url)) {
    return originNavigateTo.call(this, options);
  } else {
    uni.reLaunch({
      url: '/pages/login/index'
    });
  }
};

const originRedirectTo = uni.redirectTo;
uni.redirectTo = function(options) {
  if (hasPermission(options.url)) {
    return originRedirectTo.call(this, options);
  } else {
    uni.reLaunch({
      url: '/pages/login/index'
    });
  }
};

const originReLaunch = uni.reLaunch;
uni.reLaunch = function(options) {
  if (hasPermission(options.url)) {
    return originReLaunch.call(this, options);
  } else {
    return originReLaunch.call(this, {
      url: '/pages/login/index'
    });
  }
};

const originSwitchTab = uni.switchTab;
uni.switchTab = function(options) {
  if (hasPermission(options.url)) {
    return originSwitchTab.call(this, options);
  } else {
    uni.reLaunch({
      url: '/pages/login/index'
    });
  }
};

// 添加通用方法到Vue原型
Vue.prototype.$tool = tool;

const app = new Vue({
  store,
  ...App
});
app.$mount();