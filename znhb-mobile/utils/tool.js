// utils/tool.js
const tool = {};

// 持久化存储 (localStorage)
tool.data = {
  set(table, settings) {
    const _set = JSON.stringify(settings);
    return uni.setStorageSync(table, _set);
  },
  get(table) {
    const data = uni.getStorageSync(table);
    try {
      return JSON.parse(data);
    } catch (err) {
      return null;
    }
  },
  remove(table) {
    return uni.removeStorageSync(table);
  },
  clear() {
    return uni.clearStorageSync();
  }
};

// 内存存储 (sessionStorage)
tool.session = {
  data: {}, // 使用对象模拟session存储
  set(table, settings) {
    this.data[table] = settings;
  },
  get(table) {
    return this.data[table] || null;
  },
  remove(table) {
    delete this.data[table];
  },
  clear() {
    this.data = {};
  }
};

// 千分符
tool.groupSeparator = (num) => {
  num = `${num}`;
  if (!num.includes('.')) num += '.';

  return num
    .replace(/(\d)(?=(\d{3})+\.)/g, ($0, $1) => {
      return `${$1},`;
    })
    .replace(/\.$/, '');
};

// 获取所有字典数组
tool.dictDataAll = () => {
  return tool.data.get('DICT_TYPE_TREE_DATA');
};

// 字典翻译方法
tool.dictTypeData = (dictValue, value) => {
  const dictTypeTree = tool.dictDataAll();
  if (!dictTypeTree) {
    return '需重新登录';
  }
  const tree = dictTypeTree.find((item) => item.dictValue === dictValue);
  if (!tree) {
    return '无此字典';
  }
  const children = tree.children;
  const dict = children.find((item) => item.dictValue === value);
  return dict ? dict.dictLabel : null;
};

// 获取某个code下字典的列表，多用于字典下拉框
tool.dictTypeList = (dictValue) => {
  const dictTypeTree = tool.dictDataAll();
  if (!dictTypeTree) {
    return [];
  }
  const tree = dictTypeTree.find((item) => item.dictValue === dictValue);
  if (tree && tree.children) {
    return tree.children;
  }
  return [];
};

// 获取某个code下字典的列表，兼容web端
tool.dictList = (dictValue) => {
  const dictTypeTree = tool.dictDataAll();
  if (!dictTypeTree) {
    return [];
  }
  const tree = dictTypeTree.find((item) => item.dictValue === dictValue);
  if (tree) {
    return tree.children.map((item) => {
      return {
        value: item['dictValue'],
        label: item['name']
      };
    });
  }
  return [];
};

// 树形翻译 需要指定最顶级的 parentValue 和当级的value
tool.translateTree = (parentValue, value) => {
  const tree = tool.dictDataAll().find((item) => item.dictValue === parentValue);
  if (!tree) return '';
  const targetNode = findNodeByValue(tree, value);
  return targetNode ? targetNode.dictLabel : '';
};

const findNodeByValue = (node, value) => {
  if (node.dictValue === value) {
    return node;
  }
  if (node.children) {
    for (let i = 0; i < node.children.length; i++) {
      const result = findNodeByValue(node.children[i], value);
      if (result) {
        return result;
      }
    }
  }
  return null;
};

// 生成UUID
tool.snowyUuid = () => {
  let uuid = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
    let r = (Math.random() * 16) | 0,
        v = c === 'x' ? r : (r & 0x3) | 0x8;
    return v.toString(16);
  });
  // 首字符转换成字母
  return 'xn' + uuid.slice(2);
};

// 日期格式化
tool.dateFormat = (date, fmt = 'yyyy-MM-dd') => {
  date = date === undefined ? new Date() : date;
  date = typeof date === 'number' ? new Date(date) : date;
  fmt = fmt || 'yyyy-MM-dd';
  const obj = {
    'y': date.getFullYear(), // 年份，注意必须用getFullYear
    'M': date.getMonth() + 1, // 月份，注意是从0-11
    'd': date.getDate(), // 日期
    'q': Math.floor((date.getMonth() + 3) / 3), // 季度
    'w': date.getDay(), // 星期，注意是0-6
    'H': date.getHours(), // 24小时制
    'h': date.getHours() % 12 === 0 ? 12 : date.getHours() % 12, // 12小时制
    'm': date.getMinutes(), // 分钟
    's': date.getSeconds(), // 秒
    'S': date.getMilliseconds() // 毫秒
  };
  const week = ['日', '一', '二', '三', '四', '五', '六'];
  for (const i in obj) {
    fmt = fmt.replace(new RegExp('(' + i + '+)', 'g'), function (m) {
      let val = obj[i] + '';
      if (i === 'w') return (m.length > 2 ? '星期' : '周') + week[val];
      for (let j = 0, len = val.length; j < m.length - len; j++) val = '0' + val;
      return m.length === 1 ? val : val.substring(val.length - m.length);
    });
  }
  return fmt;
};

// 深拷贝对象
tool.deepClone = (obj) => {
  if (obj === null || typeof obj !== 'object') return obj;
  const clone = Array.isArray(obj) ? [] : {};
  for (const key in obj) {
    if (Object.prototype.hasOwnProperty.call(obj, key)) {
      clone[key] = tool.deepClone(obj[key]);
    }
  }
  return clone;
};

export default tool; 