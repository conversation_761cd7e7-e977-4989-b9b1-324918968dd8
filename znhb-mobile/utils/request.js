// utils/request.js
import tool from './tool.js';
import config from '../config/index.js';

// 错误码映射
const errorCodeMap = {
  400: '发出的请求有错误，服务器没有进行新建或修改数据的操作。',
  401: '用户没有权限（令牌、用户名、密码错误）。',
  403: '用户得到授权，但是访问是被禁止的。',
  404: '发出的请求针对的是不存在的记录，服务器没有进行操作。',
  406: '请求的格式不可得。',
  410: '请求的资源被永久删除，且不会再得到的。',
  422: '当创建一个对象时，发生一个验证错误。',
  500: '服务器发生错误，请检查服务器。',
  502: '网关错误。',
  503: '服务不可用，服务器暂时过载或维护。',
  504: '网关超时。'
};

// 需要重新登录的状态码
const reloadCodes = [401, 1011007, 1011008];

// 定义一个登录状态变量
let loginBackStatus = false;

// 登录过期处理
const handleLoginExpired = () => {
  if (!loginBackStatus) {
    loginBackStatus = true;
    
    uni.showModal({
      title: '提示',
      content: '登录已失效，请重新登录',
      showCancel: false,
      success: () => {
        loginBackStatus = false;
        tool.data.remove('TOKEN');
        tool.data.remove('USER_INFO');
        tool.data.remove('MENU');
        tool.data.remove('PERMISSIONS');
        
        uni.reLaunch({
          url: '/pages/login/index'
        });
      }
    });
  }
};

// 创建请求对象
const service = {
  // 请求拦截器
  requestInterceptors: (config) => {
    // 添加token到请求头
    const token = tool.data.get('TOKEN');
    if (token) {
      config.header = config.header || {};
      config.header[config.TOKEN_NAME] = config.TOKEN_PREFIX + token;
    }
    
    // 防止GET请求缓存
    if (!config.REQUEST_CACHE && config.method === 'GET') {
      config.data = config.data || {};
      config.data._ = new Date().getTime();
    }
    
    // 合并默认头
    config.header = {
      ...(config.header || {}),
      ...config.HEADERS
    };
    
    return config;
  },
  
  // 响应拦截器
  responseInterceptors: (response, options) => {
    // 配置了blob，不处理直接返回文件流
    if (options.responseType === 'blob') {
      if (response.statusCode === 200) {
        return response;
      } else {
        uni.showToast({
          title: '文件下载失败或此文件不存在',
          icon: 'none'
        });
        return Promise.reject(response);
      }
    }
    
    const data = response.data;
    const code = data.code;
    
    // 需要重新登录的情况
    if (reloadCodes.includes(code)) {
      handleLoginExpired();
      return Promise.reject(data);
    }
    
    // 错误处理
    if (code !== 200) {
      const customErrorMessage = options.customErrorMessage;
      uni.showToast({
        title: customErrorMessage || data.msg || '请求失败',
        icon: 'none'
      });
      return Promise.reject(data);
    } else {
      // 统一成功提示
      const responseUrls = options.url.split('/');
      const apiNameArray = [
        'add', 'edit', 'delete', 'update', 'grant', 'reset', 'stop',
        'pass', 'disable', 'enable', 'revoke', 'suspend', 'active',
        'turn', 'adjust', 'reject', 'saveDraft'
      ];
      
      apiNameArray.forEach((apiName) => {
        if (responseUrls[responseUrls.length - 1].includes(apiName)) {
          uni.showToast({
            title: data.msg || '操作成功',
            icon: 'success'
          });
        }
      });
      
      return Promise.resolve(data.data);
    }
  },
  
  // 请求错误处理
  requestErrorHandler: (error) => {
    if (error) {
      const status = error.statusCode || 503;
      const description = errorCodeMap[status] || errorCodeMap['default'];
      
      uni.showToast({
        title: `请求错误: ${description}`,
        icon: 'none',
        duration: 2000
      });
      
      return Promise.reject(status);
    }
  },
  
  // 实际发起请求的方法
  request: (options) => {
    // 应用请求拦截器
    options = service.requestInterceptors({
      ...options,
      TOKEN_NAME: config.TOKEN_NAME,
      TOKEN_PREFIX: config.TOKEN_PREFIX,
      REQUEST_CACHE: config.REQUEST_CACHE,
      HEADERS: config.HEADERS
    });
    
    return new Promise((resolve, reject) => {
      uni.request({
        url: options.url,
        data: options.data,
        method: options.method,
        header: options.header,
        responseType: options.responseType,
        success: (res) => {
          try {
            const result = service.responseInterceptors(res, options);
            resolve(result);
          } catch (e) {
            reject(e);
          }
        },
        fail: (err) => {
          service.requestErrorHandler(err);
          reject(err);
        }
      });
    });
  }
};

// 基础请求方法
export const baseRequest = (url, value = {}, method = 'POST', options = {}) => {
  url = config.API_URL + url;
  
  if (method === 'POST') {
    return service.request({
      url,
      data: value,
      method: 'POST',
      ...options
    });
  } else if (method === 'GET') {
    return service.request({
      url,
      data: value,
      method: 'GET',
      ...options
    });
  } else if (method === 'formdata') {
    // form-data表单提交的方式
    return service.request({
      url,
      data: value,
      method: 'POST',
      header: {
        'Content-Type': 'multipart/form-data',
        ...(options.header || {})
      },
      ...options
    });
  } else {
    // 其他请求方式，例如：PUT、DELETE
    return service.request({
      method,
      url,
      data: value,
      ...options
    });
  }
};

// 模块内的请求，会自动加上模块的前缀
export const moduleRequest = (moduleUrl) => (url, ...arg) => {
  return baseRequest(moduleUrl + url, ...arg);
};

// 文件上传
export const upload = (url, filePath, formData = {}, options = {}) => {
  url = config.API_URL + url;
  
  const token = tool.data.get('TOKEN');
  const header = {};
  
  if (token) {
    header[config.TOKEN_NAME] = config.TOKEN_PREFIX + token;
  }
  
  return new Promise((resolve, reject) => {
    uni.uploadFile({
      url,
      filePath,
      name: options.name || 'file',
      formData,
      header: {
        ...header,
        ...(options.header || {})
      },
      success: (res) => {
        if (res.statusCode === 200) {
          try {
            const data = JSON.parse(res.data);
            if (data.code === 200) {
              resolve(data.data);
            } else {
              uni.showToast({
                title: data.msg || '上传失败',
                icon: 'none'
              });
              reject(data);
            }
          } catch (e) {
            reject(e);
          }
        } else {
          uni.showToast({
            title: '文件上传失败',
            icon: 'none'
          });
          reject(res);
        }
      },
      fail: (err) => {
        uni.showToast({
          title: '文件上传失败',
          icon: 'none'
        });
        reject(err);
      }
    });
  });
};

// 文件下载
export const download = (url, data = {}) => {
  url = config.API_URL + url;
  
  const token = tool.data.get('TOKEN');
  const header = {};
  
  if (token) {
    header[config.TOKEN_NAME] = config.TOKEN_PREFIX + token;
  }
  
  return new Promise((resolve, reject) => {
    uni.downloadFile({
      url: url + '?' + Object.keys(data).map(key => `${key}=${data[key]}`).join('&'),
      header,
      success: (res) => {
        if (res.statusCode === 200) {
          resolve(res.tempFilePath);
        } else {
          uni.showToast({
            title: '文件下载失败',
            icon: 'none'
          });
          reject(res);
        }
      },
      fail: (err) => {
        uni.showToast({
          title: '文件下载失败',
          icon: 'none'
        });
        reject(err);
      }
    });
  });
};

export default service; 