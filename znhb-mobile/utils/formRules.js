/**
 * 表单验证规则
 */

/**
 * 必填项验证
 * @param {String} message 错误提示
 * @param {String} trigger 触发方式 blur/change
 * @returns {Object} 验证规则
 */
export function required(message = '该项为必填项', trigger = 'blur') {
  return {
    required: true,
    message,
    trigger
  };
}

/**
 * 长度验证
 * @param {Number} min 最小长度
 * @param {Number} max 最大长度
 * @param {String} message 错误提示
 * @param {String} trigger 触发方式 blur/change
 * @returns {Object} 验证规则
 */
export function length(min, max, message, trigger = 'blur') {
  const rule = { min, max, trigger };
  if (message) {
    rule.message = message;
  } else {
    if (min && max) {
      rule.message = `长度应在${min}到${max}个字符之间`;
    } else if (min) {
      rule.message = `长度不能少于${min}个字符`;
    } else if (max) {
      rule.message = `长度不能超过${max}个字符`;
    }
  }
  return rule;
}

/**
 * 手机号验证
 * @param {String} message 错误提示
 * @param {String} trigger 触发方式 blur/change
 * @returns {Object} 验证规则
 */
export function phone(message = '请输入正确的手机号', trigger = 'blur') {
  return {
    pattern: /^1[3-9]\d{9}$/,
    message,
    trigger
  };
}

/**
 * 邮箱验证
 * @param {String} message 错误提示
 * @param {String} trigger 触发方式 blur/change
 * @returns {Object} 验证规则
 */
export function email(message = '请输入正确的邮箱地址', trigger = 'blur') {
  return {
    type: 'email',
    message,
    trigger
  };
}

/**
 * 数字验证
 * @param {String} message 错误提示
 * @param {String} trigger 触发方式 blur/change
 * @returns {Object} 验证规则
 */
export function number(message = '请输入数字', trigger = 'blur') {
  return {
    type: 'number',
    message,
    trigger
  };
}

/**
 * 整数验证
 * @param {String} message 错误提示
 * @param {String} trigger 触发方式 blur/change
 * @returns {Object} 验证规则
 */
export function integer(message = '请输入整数', trigger = 'blur') {
  return {
    pattern: /^-?[1-9]\d*$/,
    message,
    trigger
  };
}

/**
 * URL验证
 * @param {String} message 错误提示
 * @param {String} trigger 触发方式 blur/change
 * @returns {Object} 验证规则
 */
export function url(message = '请输入正确的URL地址', trigger = 'blur') {
  return {
    type: 'url',
    message,
    trigger
  };
}

/**
 * 自定义正则验证
 * @param {RegExp} pattern 正则表达式
 * @param {String} message 错误提示
 * @param {String} trigger 触发方式 blur/change
 * @returns {Object} 验证规则
 */
export function pattern(pattern, message, trigger = 'blur') {
  return {
    pattern,
    message,
    trigger
  };
} 