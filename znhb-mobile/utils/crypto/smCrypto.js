// 引入第三方sm-crypto库，需要先安装：npm install sm-crypto --save
import { sm2 } from 'sm-crypto';

// 公钥 - 从后端获取，这里先使用默认值，后续需要从接口获取
let publicKey = '04298364ec840088475eae92a591e01284d1abefcda348b47eb324bb521bb03b0b2a5bc393f6b71dabb8f15c99a0050818b56b23f31743b93df9cf8948f15ddb54';

/**
 * SM2加密
 * @param {String} msgString 待加密的字符串
 * @returns {String} 加密后的字符串
 */
function encrypt(msgString) {
  if (!publicKey) {
    console.error('SM2公钥未设置，请先调用setPublicKey方法设置');
    return msgString;
  }
  
  try {
    const encryptData = sm2.doEncrypt(msgString, publicKey, 1); // 1表示C1C3C2顺序
    return encryptData;
  } catch (e) {
    console.error('SM2加密失败', e);
    return msgString;
  }
}

/**
 * 设置SM2公钥
 * @param {String} key 公钥
 */
function setPublicKey(key) {
  publicKey = key;
}

export default {
  encrypt,
  setPublicKey
}; 