// api/sys/userCenterApi.js
import { moduleRequest } from '../../utils/request.js';

// 创建用户中心模块的请求
const request = moduleRequest('/sys/userCenter/');

export default {
  // 获取登录用户菜单
  userLoginMenu(data) {
    return request('loginMobileMenu', data, 'GET');
  },
  
  // 获取个人信息
  getUserInfo(data) {
    return request('getUserInfo', data, 'GET');
  },
  
  // 更新个人信息
  updateUserInfo(data) {
    return request('updateUserInfo', data, 'POST');
  },
  
  // 更新用户密码
  updateUserPwd(data) {
    return request('updateUserPwd', data, 'POST');
  },
  
  // 更新用户头像
  updateUserAvatar(data) {
    return request('updateUserAvatar', data, 'POST');
  },
  
  // 获取通知列表
  noticeList(data) {
    return request('noticeList', data, 'GET');
  },
  
  // 获取未读通知数量
  getUnreadNoticeCount(data) {
    return request('getUnreadNoticeCount', data, 'GET');
  },
  
  // 设置通知已读
  setReadNotice(data) {
    return request('setReadNotice', data, 'POST');
  }
}; 