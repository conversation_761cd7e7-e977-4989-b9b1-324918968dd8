// api/dev/dictApi.js
import { moduleRequest } from '../../utils/request.js';

// 创建字典模块的请求
const request = moduleRequest('/dev/dict/');

export default {
  // 获取字典树
  dictTree(data) {
    return request('tree', data, 'GET');
  },
  
  // 获取字典列表
  dictList(data) {
    return request('list', data, 'GET');
  },
  
  // 获取字典分页
  dictPage(data) {
    return request('page', data, 'GET');
  },
  
  // 获取字典详情
  dictDetail(data) {
    return request('detail', data, 'GET');
  },
  
  // 添加字典
  dictAdd(data) {
    return request('add', data, 'POST');
  },
  
  // 编辑字典
  dictEdit(data) {
    return request('edit', data, 'POST');
  },
  
  // 删除字典
  dictDelete(data) {
    return request('delete', data, 'POST');
  }
}; 