// api/auth/loginApi.js
import { moduleRequest } from '../../utils/request.js';

// 创建登录模块的请求
const request = moduleRequest('/auth/b/');

export default {
  // 获取图片验证码
  getPicCaptcha(data) {
    return request('getPicCaptcha', data, 'GET');
  },
  
  // 获取手机验证码
  getPhoneValidCode(data) {
    return request('getPhoneValidCode', data, 'GET');
  },
  
  // 账号密码登录
  login(data) {
    return request('doLogin', data, 'POST');
  },
  
  // 手机验证码登录
  loginByPhone(data) {
    return request('doLoginByPhone', data, 'POST');
  },
  
  // 退出登录
  logout(data) {
    return request('doLogout', data, 'GET');
  },
  
  // 获取用户信息
  getLoginUser(data) {
    return request('getLoginUser', data, 'GET');
  }
}; 