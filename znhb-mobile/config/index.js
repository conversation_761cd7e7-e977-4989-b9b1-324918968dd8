// config/index.js
export default {
  // 首页地址
  DASHBOARD_URL: '/pages/index/index',

  // 接口地址
  API_URL: process.env.NODE_ENV === 'development'
    ? 'http://localhost:82' // 开发环境API地址
    : 'http://localhost:82', // 生产环境API地址
  
  // 请求超时
  TIMEOUT: 60000,
  
  // Token相关配置
  TOKEN_NAME: 'token', // token在header中的名称
  TOKEN_PREFIX: '', // token前缀
  
  // 请求是否开启缓存
  REQUEST_CACHE: false,
  
  // 默认请求头
  HEADERS: {
    'Content-Type': 'application/json;charset=UTF-8'
  },
  
  // 系统基础配置
  SYS_BASE_CONFIG: {
    // 默认logo
    SNOWY_SYS_LOGO: '/static/logo.png',
    // 系统名称
    SNOWY_SYS_NAME: '清洁运输门禁管理平台',
    // 版本
    SNOWY_SYS_VERSION: '2.0',
    // 版权
    SNOWY_SYS_COPYRIGHT: 'Snowy ©2022 Created by xiaonuo.vip',
    // 版权跳转URL
    SNOWY_SYS_COPYRIGHT_URL: 'https://www.xiaonuo.vip',
    // 默认文件存储
    SNOWY_SYS_DEFAULT_FILE_ENGINE: 'LOCAL',
    // 是否开启验证码，默认不启用
    SNOWY_SYS_DEFAULT_CAPTCHA_OPEN: 'false',
    // 默认重置密码
    SNOWY_SYS_DEFAULT_PASSWORD: '123456'
  },
  
  // 主题配置
  THEME: {
    PRIMARY_COLOR: '#1890ff', // 主题色
    SUCCESS_COLOR: '#52c41a', // 成功色
    WARNING_COLOR: '#faad14', // 警告色
    ERROR_COLOR: '#f5222d' // 错误色
  },
  
  // 语言
  LANG: 'zh-cn'
}; 