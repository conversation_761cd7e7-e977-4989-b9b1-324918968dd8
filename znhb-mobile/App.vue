<script>
import tool from './utils/tool.js';

export default {
	onLaunch: function() {
		console.log('App Launch');
		// 初始化字体图标
		// #ifdef APP-PLUS
		plus.navigator.setStatusBarStyle('dark');
		// #endif
	},
	onShow: function() {
		console.log('App Show');
		// 检查登录状态
		this.checkLoginStatus();
	},
	onHide: function() {
		console.log('App Hide');
	},
	methods: {
		// 检查登录状态
		checkLoginStatus() {
			const token = tool.data.get('TOKEN');
			const currentPage = getCurrentPages();
			
			// 如果当前没有页面，说明刚启动应用
			if (currentPage.length === 0) {
				if (!token) {
					uni.redirectTo({
						url: '/pages/login/index'
					});
				}
				return;
			}
			
			const currentRoute = currentPage[currentPage.length - 1].route;
			console.log('当前页面:', currentRoute, '是否有token:', !!token);
			
			// 如果当前不是登录页且没有token，则跳转到登录页
			if (!token && currentRoute !== 'pages/login/index') {
				uni.redirectTo({
					url: '/pages/login/index'
				});
			}
			// 如果当前是登录页且有token，则跳转到首页
			else if (token && currentRoute === 'pages/login/index') {
				uni.switchTab({
					url: '/pages/index/index'
				});
			}
		}
	}
}
</script>

<template>
	<view :class="`font-size-${currentFontSize}`">
		<App />
	</view>
</template>

<style>
	/*每个页面公共css */
	@import './static/iconfont/iconfont.css'; /* 引入字体图标 */

	page {
		font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, Segoe UI, Arial, Roboto, 'PingFang SC', 'miui', 'Hiragino Sans GB', 'Microsoft Yahei', sans-serif;
		font-size: 28rpx;
	}

	/* 全局文本类 */
	.text-content {
		font-size: 28rpx;
	}

	.text-small {
		font-size: 24rpx;
	}

	.text-large {
		font-size: 32rpx;
	}

	.text-title {
		font-size: 36rpx;
		font-weight: bold;
	}

	.text-subtitle {
		font-size: 30rpx;
		font-weight: 500;
	}

	.text-caption {
		font-size: 24rpx;
		color: #999;
	}
</style>
