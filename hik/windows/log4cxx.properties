#È±Ê¡²»Êä³öÈÕÖ¾µ½¿ØÖÆÌ¨
#FATAL¡¢ERROR¡¢WARN¡¢INFO¡¢DEBUG ÓÅÏÈ¼¶Ë³Ðò  Èç¹û×ÓÄ£¿éºÍ¸ùÄ£¿é¶¼Æ¥Åä£¬ÄÇÃ´¶¼»áÊä³ö
log4j.rootLogger=DEBUG, stdout
#log4j.rootLogger=DEBUG
##hlog.async=false
##hlog.secret.show=true
##hlog.secret.encrypt=false
#log4j.loggerÓÃÓÚ¿ØÖÆÈÕÖ¾²É¼¯¼¶±ð¼°²É¼¯ÄÚÈÝ£¬ThresholdÓÃÓÚ¿ØÖÆÈÕÖ¾Êä³ö¼¶±ð

#Ó¦ÓÃÓÚ¿ØÖÆÌ¨
log4j.appender.stdout=org.apache.log4j.ConsoleAppender 
log4j.appender.stdout.layout=org.apache.log4j.PatternLayout 
log4j.appender.stdout.layout.ConversionPattern=[%d][%t][%-5p]- %m%n

log4j.logger.NPQ=TRACE, NPQ
log4j.appender.NPQ=org.apache.log4j.RollingFileAppender
log4j.appender.NPQ.File=./NPQLog/NPQ.log
log4j.appender.NPQ.MaxFileSize=80MB
log4j.appender.NPQ.MaxBackupIndex=12
log4j.appender.NPQ.Append=false
log4j.appender.NPQ.Threshold=TRACE
log4j.appender.NPQ.layout=org.apache.log4j.PatternLayout
log4j.appender.NPQ.layout.ConversionPattern=[%d][%t][%-5p]- %m%n
log4j.additivity.NPQ = false
#×îºóÒ»Î»ÐÞ¸ÄÎªtrue ¼È¿ÉÒÔ¿ØÖÆÌ¨Êä³öÓÖ¿ÉÒÔÎÄ¼þÊä³ö

