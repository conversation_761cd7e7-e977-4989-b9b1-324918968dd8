/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package com.znhb.wechat.api;

import com.znhb.common.pojo.CommonResult;
import me.chanjar.weixin.mp.bean.result.WxMpUser;

/**
 * 微信用户API
 *
 * <AUTHOR>
 * @date 2024/01/01 10:00
 **/
public interface WechatUserApi {

    /**
     * 根据openId获取微信用户信息
     *
     * @param openId 微信用户openId
     * @return 微信用户信息
     * <AUTHOR>
     * @date 2024/01/01 10:00
     **/
    WxMpUser getUserInfo(String openId);

    /**
     * 同步微信用户信息到本地
     *
     * @param openId 微信用户openId
     * @return 同步结果
     * <AUTHOR>
     * @date 2024/01/01 10:00
     **/
    CommonResult<String> syncUserInfo(String openId);

    /**
     * 绑定微信用户与系统用户
     *
     * @param openId 微信用户openId
     * @param userId 系统用户ID
     * @return 绑定结果
     * <AUTHOR>
     * @date 2024/01/01 10:00
     **/
    CommonResult<String> bindUser(String openId, String userId);

    /**
     * 解绑微信用户与系统用户
     *
     * @param openId 微信用户openId
     * @return 解绑结果
     * <AUTHOR>
     * @date 2024/01/01 10:00
     **/
    CommonResult<String> unbindUser(String openId);

    /**
     * 检查用户是否已绑定
     *
     * @param openId 微信用户openId
     * @return 是否已绑定
     * <AUTHOR>
     * @date 2024/01/01 10:00
     **/
    boolean isUserBound(String openId);

    /**
     * 根据系统用户ID获取微信openId
     *
     * @param userId 系统用户ID
     * @return 微信openId
     * <AUTHOR>
     * @date 2024/01/01 10:00
     **/
    String getOpenIdByUserId(String userId);
}
