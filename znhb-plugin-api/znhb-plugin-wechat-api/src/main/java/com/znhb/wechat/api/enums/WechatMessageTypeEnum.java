/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package com.znhb.wechat.api.enums;

import lombok.Getter;

/**
 * 微信消息类型枚举
 *
 * <AUTHOR>
 * @date 2024/01/01 10:00
 **/
@Getter
public enum WechatMessageTypeEnum {

    /** 文本消息 */
    TEXT("text", "文本消息"),

    /** 图片消息 */
    IMAGE("image", "图片消息"),

    /** 语音消息 */
    VOICE("voice", "语音消息"),

    /** 视频消息 */
    VIDEO("video", "视频消息"),

    /** 小视频消息 */
    SHORT_VIDEO("shortvideo", "小视频消息"),

    /** 地理位置消息 */
    LOCATION("location", "地理位置消息"),

    /** 链接消息 */
    LINK("link", "链接消息"),

    /** 事件消息 */
    EVENT("event", "事件消息"),

    /** 音乐消息 */
    MUSIC("music", "音乐消息"),

    /** 图文消息 */
    NEWS("news", "图文消息");

    private final String value;
    private final String description;

    WechatMessageTypeEnum(String value, String description) {
        this.value = value;
        this.description = description;
    }
}
