/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package com.znhb.wechat.api.enums;

import lombok.Getter;

/**
 * 微信事件类型枚举
 *
 * <AUTHOR>
 * @date 2024/01/01 10:00
 **/
@Getter
public enum WechatEventTypeEnum {

    /** 关注事件 */
    SUBSCRIBE("subscribe", "关注事件"),

    /** 取消关注事件 */
    UNSUBSCRIBE("unsubscribe", "取消关注事件"),

    /** 扫描带参数二维码事件 */
    SCAN("SCAN", "扫描带参数二维码事件"),

    /** 上报地理位置事件 */
    LOCATION("LOCATION", "上报地理位置事件"),

    /** 点击菜单拉取消息时的事件推送 */
    CLICK("CLICK", "点击菜单拉取消息时的事件推送"),

    /** 点击菜单跳转链接时的事件推送 */
    VIEW("VIEW", "点击菜单跳转链接时的事件推送"),

    /** 扫码推事件的事件推送 */
    SCANCODE_PUSH("scancode_push", "扫码推事件的事件推送"),

    /** 扫码推事件且弹出"消息接收中"提示框的事件推送 */
    SCANCODE_WAITMSG("scancode_waitmsg", "扫码推事件且弹出消息接收中提示框的事件推送"),

    /** 弹出系统拍照发图的事件推送 */
    PIC_SYSPHOTO("pic_sysphoto", "弹出系统拍照发图的事件推送"),

    /** 弹出拍照或者相册发图的事件推送 */
    PIC_PHOTO_OR_ALBUM("pic_photo_or_album", "弹出拍照或者相册发图的事件推送"),

    /** 弹出微信相册发图器的事件推送 */
    PIC_WEIXIN("pic_weixin", "弹出微信相册发图器的事件推送"),

    /** 弹出地理位置选择器的事件推送 */
    LOCATION_SELECT("location_select", "弹出地理位置选择器的事件推送");

    private final String value;
    private final String description;

    WechatEventTypeEnum(String value, String description) {
        this.value = value;
        this.description = description;
    }
}
