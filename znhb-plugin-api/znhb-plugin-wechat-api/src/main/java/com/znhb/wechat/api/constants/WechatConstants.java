/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package com.znhb.wechat.api.constants;

/**
 * 微信常量
 *
 * <AUTHOR>
 * @date 2024/01/01 10:00
 **/
public class WechatConstants {

    /** 微信公众号配置键名 */
    public static final String WECHAT_MP_APP_ID = "WECHAT_MP_APP_ID";
    public static final String WECHAT_MP_APP_SECRET = "WECHAT_MP_APP_SECRET";
    public static final String WECHAT_MP_TOKEN = "WECHAT_MP_TOKEN";
    public static final String WECHAT_MP_ENCODING_AES_KEY = "WECHAT_MP_ENCODING_AES_KEY";

    /** 用户关注状态 */
    public static final String SUBSCRIBE_STATUS_SUBSCRIBED = "SUBSCRIBED";
    public static final String SUBSCRIBE_STATUS_UNSUBSCRIBED = "UNSUBSCRIBED";

    /** 用户性别 */
    public static final String SEX_MALE = "MALE";
    public static final String SEX_FEMALE = "FEMALE";
    public static final String SEX_UNKNOWN = "UNKNOWN";

    /** 菜单类型 */
    public static final String MENU_TYPE_CLICK = "click";
    public static final String MENU_TYPE_VIEW = "view";
    public static final String MENU_TYPE_SCANCODE_PUSH = "scancode_push";
    public static final String MENU_TYPE_SCANCODE_WAITMSG = "scancode_waitmsg";
    public static final String MENU_TYPE_PIC_SYSPHOTO = "pic_sysphoto";
    public static final String MENU_TYPE_PIC_PHOTO_OR_ALBUM = "pic_photo_or_album";
    public static final String MENU_TYPE_PIC_WEIXIN = "pic_weixin";
    public static final String MENU_TYPE_LOCATION_SELECT = "location_select";
    public static final String MENU_TYPE_MINIPROGRAM = "miniprogram";

    /** 菜单状态 */
    public static final String MENU_STATUS_ENABLE = "ENABLE";
    public static final String MENU_STATUS_DISABLE = "DISABLE";

    /** 自动回复规则类型 */
    public static final String REPLY_RULE_TYPE_SUBSCRIBE = "subscribe";
    public static final String REPLY_RULE_TYPE_KEYWORD = "keyword";
    public static final String REPLY_RULE_TYPE_DEFAULT = "default";

    /** 回复类型 */
    public static final String REPLY_TYPE_TEXT = "text";
    public static final String REPLY_TYPE_IMAGE = "image";
    public static final String REPLY_TYPE_VOICE = "voice";
    public static final String REPLY_TYPE_VIDEO = "video";
    public static final String REPLY_TYPE_MUSIC = "music";
    public static final String REPLY_TYPE_NEWS = "news";

    /** 素材类型 */
    public static final String MEDIA_TYPE_IMAGE = "image";
    public static final String MEDIA_TYPE_VOICE = "voice";
    public static final String MEDIA_TYPE_VIDEO = "video";
    public static final String MEDIA_TYPE_THUMB = "thumb";

    /** 模板消息模板ID */
    public static final String TEMPLATE_VEHICLE_PASS_NOTICE = "VEHICLE_PASS_NOTICE";
    public static final String TEMPLATE_SYSTEM_NOTICE = "SYSTEM_NOTICE";
    public static final String TEMPLATE_BIND_SUCCESS_NOTICE = "BIND_SUCCESS_NOTICE";

    /** 默认菜单KEY */
    public static final String MENU_KEY_HELP = "MENU_HELP";
    public static final String MENU_KEY_BIND = "MENU_BIND";
    public static final String MENU_KEY_STATUS = "MENU_STATUS";
    public static final String MENU_KEY_QUERY = "MENU_QUERY";
    public static final String MENU_KEY_NOTICE = "MENU_NOTICE";

    /** 缓存键前缀 */
    public static final String CACHE_PREFIX_USER = "wechat:user:";
    public static final String CACHE_PREFIX_MESSAGE = "wechat:message:";
    public static final String CACHE_PREFIX_MENU = "wechat:menu:";
    public static final String CACHE_PREFIX_CONFIG = "wechat:config:";

    /** 默认回复消息 */
    public static final String DEFAULT_WELCOME_MESSAGE = "🎉 欢迎关注清洁运输门禁管理平台！\n\n" +
            "我们为您提供：\n" +
            "• 车辆通行记录查询\n" +
            "• 实时通行状态\n" +
            "• 系统通知推送\n" +
            "• 便民服务功能\n\n" +
            "发送"帮助"获取更多功能介绍。";

    public static final String DEFAULT_HELP_MESSAGE = "欢迎使用清洁运输门禁管理平台！\n\n" +
            "您可以发送以下关键词获取帮助：\n" +
            "• 帮助 - 查看帮助信息\n" +
            "• 绑定 - 绑定系统账号\n" +
            "• 状态 - 查看绑定状态\n" +
            "• 通知 - 查看最新通知";

    public static final String DEFAULT_REPLY_MESSAGE = "感谢您的消息！\n发送"帮助"获取更多信息。";
}
