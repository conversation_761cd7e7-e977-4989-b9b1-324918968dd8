/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package com.znhb.wechat.api;

import com.znhb.common.pojo.CommonResult;
import me.chanjar.weixin.mp.bean.template.WxMpTemplateMessage;

/**
 * 微信消息API
 *
 * <AUTHOR>
 * @date 2024/01/01 10:00
 **/
public interface WechatMessageApi {

    /**
     * 发送文本消息
     *
     * @param openId 接收者openId
     * @param content 消息内容
     * @return 发送结果
     * <AUTHOR>
     * @date 2024/01/01 10:00
     **/
    CommonResult<String> sendTextMessage(String openId, String content);

    /**
     * 发送模板消息
     *
     * @param templateMessage 模板消息
     * @return 发送结果
     * <AUTHOR>
     * @date 2024/01/01 10:00
     **/
    CommonResult<String> sendTemplateMessage(WxMpTemplateMessage templateMessage);

    /**
     * 发送图片消息
     *
     * @param openId 接收者openId
     * @param mediaId 图片媒体ID
     * @return 发送结果
     * <AUTHOR>
     * @date 2024/01/01 10:00
     **/
    CommonResult<String> sendImageMessage(String openId, String mediaId);

    /**
     * 发送语音消息
     *
     * @param openId 接收者openId
     * @param mediaId 语音媒体ID
     * @return 发送结果
     * <AUTHOR>
     * @date 2024/01/01 10:00
     **/
    CommonResult<String> sendVoiceMessage(String openId, String mediaId);

    /**
     * 发送视频消息
     *
     * @param openId 接收者openId
     * @param mediaId 视频媒体ID
     * @param title 视频标题
     * @param description 视频描述
     * @return 发送结果
     * <AUTHOR>
     * @date 2024/01/01 10:00
     **/
    CommonResult<String> sendVideoMessage(String openId, String mediaId, String title, String description);

    /**
     * 发送车辆通行通知
     *
     * @param openId 用户openId
     * @param plateNumber 车牌号
     * @param passTime 通行时间
     * @param location 通行地点
     * @param status 通行状态
     * @return 发送结果
     * <AUTHOR>
     * @date 2024/01/01 10:00
     **/
    CommonResult<String> sendVehiclePassNotice(String openId, String plateNumber, String passTime, String location, String status);

    /**
     * 发送系统通知
     *
     * @param openId 用户openId
     * @param title 通知标题
     * @param content 通知内容
     * @param url 跳转链接
     * @return 发送结果
     * <AUTHOR>
     * @date 2024/01/01 10:00
     **/
    CommonResult<String> sendSystemNotice(String openId, String title, String content, String url);

    /**
     * 批量发送消息
     *
     * @param openIds 接收者openId列表
     * @param content 消息内容
     * @return 发送结果
     * <AUTHOR>
     * @date 2024/01/01 10:00
     **/
    CommonResult<String> batchSendMessage(String[] openIds, String content);
}
