/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package com.znhb.wechat.api;

import com.znhb.common.pojo.CommonResult;
import me.chanjar.weixin.mp.bean.material.WxMpMaterial;
import me.chanjar.weixin.mp.bean.material.WxMpMaterialUploadResult;

import java.io.File;
import java.io.InputStream;

/**
 * 微信素材API
 *
 * <AUTHOR>
 * @date 2024/01/01 10:00
 **/
public interface WechatMaterialApi {

    /**
     * 上传临时素材
     *
     * @param type 素材类型
     * @param file 文件
     * @return 上传结果
     * <AUTHOR>
     * @date 2024/01/01 10:00
     **/
    CommonResult<String> uploadTempMedia(String type, File file);

    /**
     * 上传临时素材
     *
     * @param type 素材类型
     * @param fileName 文件名
     * @param inputStream 文件流
     * @return 上传结果
     * <AUTHOR>
     * @date 2024/01/01 10:00
     **/
    CommonResult<String> uploadTempMedia(String type, String fileName, InputStream inputStream);

    /**
     * 上传永久素材
     *
     * @param material 素材对象
     * @return 上传结果
     * <AUTHOR>
     * @date 2024/01/01 10:00
     **/
    CommonResult<WxMpMaterialUploadResult> uploadMaterial(WxMpMaterial material);

    /**
     * 删除永久素材
     *
     * @param mediaId 素材ID
     * @return 删除结果
     * <AUTHOR>
     * @date 2024/01/01 10:00
     **/
    CommonResult<String> deleteMaterial(String mediaId);

    /**
     * 获取素材
     *
     * @param mediaId 素材ID
     * @return 素材内容
     * <AUTHOR>
     * @date 2024/01/01 10:00
     **/
    InputStream downloadMedia(String mediaId);

    /**
     * 获取素材总数
     *
     * @return 素材总数
     * <AUTHOR>
     * @date 2024/01/01 10:00
     **/
    CommonResult<String> getMaterialCount();
}
