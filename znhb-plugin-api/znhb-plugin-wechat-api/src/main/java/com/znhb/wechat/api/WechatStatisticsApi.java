/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package com.znhb.wechat.api;

import java.util.Map;

/**
 * 微信统计API
 *
 * <AUTHOR>
 * @date 2024/01/01 10:00
 **/
public interface WechatStatisticsApi {

    /**
     * 获取用户统计信息
     *
     * @return 用户统计信息
     * <AUTHOR>
     * @date 2024/01/01 10:00
     **/
    Map<String, Object> getUserStatistics();

    /**
     * 获取消息统计信息
     *
     * @return 消息统计信息
     * <AUTHOR>
     * @date 2024/01/01 10:00
     **/
    Map<String, Object> getMessageStatistics();

    /**
     * 获取今日新增关注用户数
     *
     * @return 今日新增关注用户数
     * <AUTHOR>
     * @date 2024/01/01 10:00
     **/
    Long getTodayNewSubscribeCount();

    /**
     * 获取总关注用户数
     *
     * @return 总关注用户数
     * <AUTHOR>
     * @date 2024/01/01 10:00
     **/
    Long getTotalSubscribeCount();

    /**
     * 获取已绑定用户数
     *
     * @return 已绑定用户数
     * <AUTHOR>
     * @date 2024/01/01 10:00
     **/
    Long getBoundUserCount();

    /**
     * 获取今日消息处理量
     *
     * @return 今日消息处理量
     * <AUTHOR>
     * @date 2024/01/01 10:00
     **/
    Long getTodayMessageCount();

    /**
     * 获取用户地区分布统计
     *
     * @return 用户地区分布统计
     * <AUTHOR>
     * @date 2024/01/01 10:00
     **/
    Map<String, Long> getUserRegionStatistics();

    /**
     * 获取用户性别分布统计
     *
     * @return 用户性别分布统计
     * <AUTHOR>
     * @date 2024/01/01 10:00
     **/
    Map<String, Long> getUserGenderStatistics();
}
