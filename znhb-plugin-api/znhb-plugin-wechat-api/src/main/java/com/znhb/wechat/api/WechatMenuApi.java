/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package com.znhb.wechat.api;

import com.znhb.common.pojo.CommonResult;
import me.chanjar.weixin.mp.bean.menu.WxMenu;

/**
 * 微信菜单API
 *
 * <AUTHOR>
 * @date 2024/01/01 10:00
 **/
public interface WechatMenuApi {

    /**
     * 创建微信菜单
     *
     * @param menu 菜单对象
     * @return 创建结果
     * <AUTHOR>
     * @date 2024/01/01 10:00
     **/
    CommonResult<String> createMenu(WxMenu menu);

    /**
     * 删除微信菜单
     *
     * @return 删除结果
     * <AUTHOR>
     * @date 2024/01/01 10:00
     **/
    CommonResult<String> deleteMenu();

    /**
     * 获取微信菜单
     *
     * @return 菜单对象
     * <AUTHOR>
     * @date 2024/01/01 10:00
     **/
    WxMenu getMenu();

    /**
     * 发布本地菜单到微信
     *
     * @return 发布结果
     * <AUTHOR>
     * @date 2024/01/01 10:00
     **/
    CommonResult<String> publishMenu();

    /**
     * 同步微信菜单到本地
     *
     * @return 同步结果
     * <AUTHOR>
     * @date 2024/01/01 10:00
     **/
    CommonResult<String> syncMenu();
}
