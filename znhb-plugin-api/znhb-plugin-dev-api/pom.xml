<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.znhb</groupId>
        <artifactId>znhb-plugin-api</artifactId>
        <version>2.0.0</version>
    </parent>

    <artifactId>znhb-plugin-dev-api</artifactId>
    <packaging>jar</packaging>
    <description>开发工具插件api接口</description>

    <dependencies>
        <!-- 每个插件接口都要引入common -->
        <dependency>
            <groupId>com.znhb</groupId>
            <artifactId>znhb-common</artifactId>
        </dependency>

        <!--腾讯云上传文件客户端-->
        <dependency>
            <groupId>com.qcloud</groupId>
            <artifactId>cos_api</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>tencentcloud-sdk-java-common</artifactId>
                    <groupId>com.tencentcloudapi</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <!--阿里云上传文件客户端-->
        <dependency>
            <groupId>com.aliyun.oss</groupId>
            <artifactId>aliyun-sdk-oss</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.codehaus.jettison</groupId>
                    <artifactId>jettison</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- jettison -->
        <dependency>
            <groupId>org.codehaus.jettison</groupId>
            <artifactId>jettison</artifactId>
        </dependency>

        <!--minio上传文件客户端-->
        <dependency>
            <groupId>io.minio</groupId>
            <artifactId>minio</artifactId>
        </dependency>

        <!--java邮件发送-->
        <dependency>
            <groupId>com.sun.mail</groupId>
            <artifactId>javax.mail</artifactId>
        </dependency>

        <!--阿里云邮件发送-->
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>aliyun-java-sdk-dm</artifactId>
        </dependency>

        <!-- 腾讯云邮件发送 -->
        <dependency>
            <groupId>com.tencentcloudapi</groupId>
            <artifactId>tencentcloud-sdk-java-ses</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>okio-jvm</artifactId>
                    <groupId>com.squareup.okio</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <!--阿里云短信发送-->
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>dysmsapi20170525</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>org.jacoco.agent</artifactId>
                    <groupId>org.jacoco</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <!--腾讯云短信发送-->
        <dependency>
            <groupId>com.tencentcloudapi</groupId>
            <artifactId>tencentcloud-sdk-java-sms</artifactId>
        </dependency>

        <!--系统硬件信息-->
        <dependency>
            <groupId>com.github.oshi</groupId>
            <artifactId>oshi-core</artifactId>
        </dependency>
    </dependencies>
</project>