<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.znhb</groupId>
        <artifactId>znhb-plugin-api</artifactId>
        <version>2.0.0</version>
    </parent>

    <artifactId>znhb-plugin-auth-api</artifactId>
    <packaging>jar</packaging>
    <description>登录鉴权插件api接口</description>

    <dependencies>
        <!-- 每个插件接口都要引入common -->
        <dependency>
            <groupId>com.znhb</groupId>
            <artifactId>znhb-common</artifactId>
        </dependency>

        <!-- sa-token-core -->
        <dependency>
            <groupId>cn.dev33</groupId>
            <artifactId>sa-token-core</artifactId>
        </dependency>
    </dependencies>
</project>