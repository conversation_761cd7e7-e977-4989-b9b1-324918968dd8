/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package com.znhb.dev.core.listener;

import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.event.ApplicationStartedEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.Ordered;
import com.znhb.dev.modular.job.entity.DevJob;
import com.znhb.dev.modular.job.enums.DevJobStatusEnum;
import com.znhb.dev.modular.job.param.DevJobIdParam;
import com.znhb.dev.modular.job.service.DevJobService;

/**
 * 定时任务监听器，系统启动时将定时任务启动
 *
 * <AUTHOR>
 * @date 2022/8/5 16:07
 **/
@Slf4j
@Configuration
public class DevJobListener implements ApplicationListener<ApplicationStartedEvent>, Ordered {

    @SuppressWarnings("ALL")
    @Override
    public void onApplicationEvent(ApplicationStartedEvent applicationStartedEvent) {
        // 获取DevJobService
        DevJobService devJobService = SpringUtil.getBean(DevJobService.class);
        
        log.info("系统启动，开始加载定时任务...");
        
        // 查询所有已启动的定时任务
        devJobService.list(new LambdaQueryWrapper<DevJob>()
                .eq(DevJob::getJobStatus, DevJobStatusEnum.RUNNING.getValue())
                .orderByAsc(DevJob::getSortCode))
                .forEach(devJob -> {
                    // 为每个任务创建一个DevJobIdParam
                    DevJobIdParam devJobIdParam = new DevJobIdParam();
                    devJobIdParam.setId(devJob.getId());
                    
                    // 通过DevJobService加载任务，使用loadJob方法避免状态检查异常
                    try {
                        log.info("正在加载定时任务：{}", devJob.getName());
                        devJobService.loadJob(devJobIdParam);
                        log.info("定时任务[{}]加载成功", devJob.getName());
                    } catch (Exception e) {
                        log.error("定时任务[{}]加载失败，原因：{}", devJob.getName(), e.getMessage());
                    }
                });
        
        log.info("所有定时任务加载完成");
    }

    @Override
    public int getOrder() {
        return LOWEST_PRECEDENCE;
    }
}
