/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package com.znhb.dev.modular.job.util;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.locks.ReentrantLock;

/**
 * 定时任务锁管理器
 * 用于管理定时任务的锁，防止同一任务并发执行
 *
 * @date 2023/06/14
 **/
public class JobLockManager {

    // 存储每个任务的锁，确保同一个任务不会并发执行
    private static final ConcurrentHashMap<String, ReentrantLock> JOB_LOCKS = new ConcurrentHashMap<>();

    /**
     * 获取任务锁
     *
     * @param jobId 任务ID
     * @return 任务锁
     */
    public static ReentrantLock getLock(String jobId) {
        // 如果没有该任务的锁，则创建一个
        JOB_LOCKS.putIfAbsent(jobId, new ReentrantLock());
        return JOB_LOCKS.get(jobId);
    }

    /**
     * 移除任务锁
     *
     * @param jobId 任务ID
     */
    public static void removeLock(String jobId) {
        JOB_LOCKS.remove(jobId);
    }

    /**
     * 尝试获取锁并执行任务
     *
     * @param jobId 任务ID
     * @param runnable 要执行的任务
     * @return 是否执行了任务
     */
    public static boolean tryRunWithLock(String jobId, Runnable runnable) {
        ReentrantLock lock = getLock(jobId);
        if (lock.tryLock()) {
            try {
                runnable.run();
                return true;
            } finally {
                lock.unlock();
            }
        }
        return false;
    }
} 