/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package com.znhb.dev.modular.config.provider;

import com.znhb.common.config.ISCConfig;
import org.springframework.stereotype.Service;
import com.znhb.dev.api.DevConfigApi;
import com.znhb.dev.modular.config.service.DevConfigService;

import javax.annotation.Resource;

/**
 * 配置API接口实现类
 *
 * <AUTHOR>
 **/
@Service
public class DevConfigApiProvider implements DevConfigApi {

    public final static String ENV_REQUEST_BASE_URL = "ENV_REQUEST_BASE_URL";

    public final static String CAPTURE_OSD_INFO = "CAPTURE_OSD_INFO";

    public final static String HIK_ISC_APPKEY = "HIK_ISC_APPKEY";

    public final static String HIK_ISC_SECRET = "HIK_ISC_SECRET";

    public final static String HIK_ISC_IP = "HIK_ISC_IP";

    public final static String HIK_ISC_PORT = "HIK_ISC_PORT";

    private final static String CAMARA_CAPTURE_DELAY_DURATION = "CAMARA_CAPTURE_DELAY_DURATION";


    @Resource
    private DevConfigService devConfigService;

    @Override
    public String getValueByKey(String key) {
        return devConfigService.getValueByKey(key);
    }

    @Override
    public String getEnvBaseUrl() {
        return devConfigService.getValueByKey(ENV_REQUEST_BASE_URL);
    }

    @Override
    public String getOsdInfoConfig() {
        return devConfigService.getValueByKey(CAPTURE_OSD_INFO);
    }

    @Override
    public ISCConfig getIscConfig() {
        ISCConfig iscConfig = new ISCConfig();
        iscConfig.setAppkey(devConfigService.getValueByKey(HIK_ISC_APPKEY));
        iscConfig.setSecret(devConfigService.getValueByKey(HIK_ISC_SECRET));
        iscConfig.setIp(devConfigService.getValueByKey(HIK_ISC_IP));
        iscConfig.setPort(devConfigService.getValueByKey(HIK_ISC_PORT));
        return iscConfig;
    }

    @Override
    public String getCamaraCaptureDelayDuration() {
        return devConfigService.getValueByKey(CAMARA_CAPTURE_DELAY_DURATION);
    }
}
