package com.znhb.dev.modular.monitor.service.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.net.NetUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.system.JvmInfo;
import cn.hutool.system.OsInfo;
import cn.hutool.system.RuntimeInfo;
import cn.hutool.system.SystemUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import com.znhb.common.util.CommonNetWorkInfoUtil;
import com.znhb.dev.modular.monitor.result.DevMonitorServerResult;
import com.znhb.dev.modular.monitor.service.DevMonitorService;

import java.io.File;
import java.lang.management.ManagementFactory;
import java.lang.management.OperatingSystemMXBean;
import java.lang.management.RuntimeMXBean;
import java.math.BigDecimal;
import java.util.Map;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 监控Service接口实现类
 *
 * <AUTHOR>
 * @date 2022/9/1 15:59
 */
@Slf4j
@Service
public class DevMonitorServiceImpl implements DevMonitorService {

    @Override
    public DevMonitorServerResult serverInfo() {
        // 使用不依赖 OSHI 的实现
        return getServerInfoFallback();
    }

    private DevMonitorServerResult getServerInfoFallback() {
        // 使用 Hutool 的 SystemUtil 提供基本信息
        DevMonitorServerResult devMonitorServerResult = new DevMonitorServerResult();

        // CPU 信息（简化版）
        DevMonitorServerResult.DevMonitorCpuInfo devMonitorCpuInfo = getCpuInfo();
        devMonitorServerResult.setDevMonitorCpuInfo(devMonitorCpuInfo);

        // 内存信息（简化版）
        DevMonitorServerResult.DevMonitorMemoryInfo devMonitorMemoryInfo = new DevMonitorServerResult.DevMonitorMemoryInfo();
        long totalMemory = Runtime.getRuntime().totalMemory();
        long freeMemory = Runtime.getRuntime().freeMemory();
        long maxMemory = Runtime.getRuntime().maxMemory();
        long usedMemory = totalMemory - freeMemory;

        devMonitorMemoryInfo.setMemoryTotal(FileUtil.readableFileSize(maxMemory));
        devMonitorMemoryInfo.setMemoryUsed(FileUtil.readableFileSize(usedMemory));
        devMonitorMemoryInfo.setMemoryFree(FileUtil.readableFileSize(freeMemory));
        devMonitorMemoryInfo.setMemoryUseRate(NumberUtil.mul(NumberUtil.div(usedMemory, totalMemory, 4), 100));
        devMonitorServerResult.setDevMonitorMemoryInfo(devMonitorMemoryInfo);

        // 存储信息（简化版）
        DevMonitorServerResult.DevMonitorStorageInfo devMonitorStorageInfo = new DevMonitorServerResult.DevMonitorStorageInfo();
        File[] roots = File.listRoots();
        AtomicLong totalSpace = new AtomicLong(0);
        AtomicLong usableSpace = new AtomicLong(0);
        AtomicLong freeSpace = new AtomicLong(0);

        for (File root : roots) {
            totalSpace.addAndGet(root.getTotalSpace());
            usableSpace.addAndGet(root.getUsableSpace());
            freeSpace.addAndGet(root.getFreeSpace());
        }

        long usedSpace = totalSpace.get() - usableSpace.get();
        devMonitorStorageInfo.setStorageTotal(FileUtil.readableFileSize(totalSpace.get()));
        devMonitorStorageInfo.setStorageUsed(FileUtil.readableFileSize(usedSpace));
        devMonitorStorageInfo.setStorageFree(FileUtil.readableFileSize(freeSpace.get()));

        // 避免除以零错误
        if (totalSpace.get() > 0) {
            devMonitorStorageInfo.setStorageUseRate(NumberUtil.mul(NumberUtil.div(usedSpace, totalSpace.get(), 4), 100));
        } else {
            devMonitorStorageInfo.setStorageUseRate(0.0);
        }

        devMonitorServerResult.setDevMonitorStorageInfo(devMonitorStorageInfo);

        // 服务器信息
        OsInfo osInfo = SystemUtil.getOsInfo();
        DevMonitorServerResult.DevMonitorServerInfo devMonitorServerInfo = new DevMonitorServerResult.DevMonitorServerInfo();
        devMonitorServerInfo.setServerName(NetUtil.getLocalHostName());
        devMonitorServerInfo.setServerOs(osInfo.getName());
        devMonitorServerInfo.setServerIp(NetUtil.getLocalhostStr());
        devMonitorServerInfo.setServerArchitecture(osInfo.getArch());
        devMonitorServerResult.setDevMonitorServerInfo(devMonitorServerInfo);

        // JVM 信息
        DevMonitorServerResult.DevMonitorJvmInfo devMonitorJvmInfo = new DevMonitorServerResult.DevMonitorJvmInfo();
        RuntimeInfo runtimeInfo = SystemUtil.getRuntimeInfo();
        JvmInfo jvmInfo = SystemUtil.getJvmInfo();
        devMonitorJvmInfo.setJvmName(jvmInfo.getName());
        devMonitorJvmInfo.setJvmVersion(jvmInfo.getVersion());
        long jvmTotalMemory = runtimeInfo.getTotalMemory();
        devMonitorJvmInfo.setJvmMemoryTotal(FileUtil.readableFileSize(jvmTotalMemory));
        devMonitorJvmInfo.setJvmMemoryFree(FileUtil.readableFileSize(runtimeInfo.getFreeMemory()));
        long jvmMemoryUsed = NumberUtil.sub(new BigDecimal(runtimeInfo.getTotalMemory()), new BigDecimal(runtimeInfo.getFreeMemory())).longValue();
        devMonitorJvmInfo.setJvmMemoryUsed(FileUtil.readableFileSize(jvmMemoryUsed));
        double jvmUseRate = NumberUtil.mul(NumberUtil.div(jvmMemoryUsed, jvmTotalMemory, 4), 100);
        devMonitorJvmInfo.setJvmUseRate(jvmUseRate);
        RuntimeMXBean runtimeMXBean = ManagementFactory.getRuntimeMXBean();
        DateTime startTime = DateUtil.date(runtimeMXBean.getStartTime());
        devMonitorJvmInfo.setJvmStartTime(DateUtil.formatDateTime(startTime));
        devMonitorJvmInfo.setJvmRunTime(DateUtil.formatBetween(startTime, DateTime.now()));
        devMonitorJvmInfo.setJavaVersion(SystemUtil.get("java.version", false));
        devMonitorJvmInfo.setJavaPath(SystemUtil.get("java.home", false));
        devMonitorServerResult.setDevMonitorJvmInfo(devMonitorJvmInfo);

        return devMonitorServerResult;
    }

    /**
     * 获取CPU信息（不依赖系统命令）
     */
    private DevMonitorServerResult.DevMonitorCpuInfo getCpuInfo() {
        DevMonitorServerResult.DevMonitorCpuInfo cpuInfo = new DevMonitorServerResult.DevMonitorCpuInfo();

        try {
            // 获取操作系统信息
            OperatingSystemMXBean osMXBean = ManagementFactory.getOperatingSystemMXBean();
            int availableProcessors = osMXBean.getAvailableProcessors();

            // 设置CPU逻辑核心数
            cpuInfo.setCpuLogicalCoreNum(availableProcessors + "个逻辑核心");

            // 设置CPU名称（使用系统属性）
            String osArch = System.getProperty("os.arch", "未知");
            String osName = System.getProperty("os.name", "未知");
            String osVersion = System.getProperty("os.version", "未知");
            String cpuName = osName + " " + osArch + " " + osVersion;
            cpuInfo.setCpuName(cpuName);

            // 设置CPU数量和物理核心数（估计值）
            int estimatedPhysicalCores = Math.max(1, availableProcessors / 2); // 假设超线程，物理核心数约为逻辑核心数的一半
            int estimatedCpus = Math.max(1, estimatedPhysicalCores / 4); // 假设每个CPU有4个物理核心

            cpuInfo.setCpuNum(estimatedCpus + "颗物理CPU (估计值)");
            cpuInfo.setCpuPhysicalCoreNum(estimatedPhysicalCores + "个物理核心 (估计值)");

            // 获取CPU使用率
            double cpuLoad = getCpuLoadFromJMX();

            // 计算CPU使用率
            double cpuUsage = cpuLoad * 100;
            if (cpuUsage > 100) {
                cpuUsage = 100;
            }

            // 假设系统和用户使用率各占一半
            double sysRate = cpuUsage / 2;
            double userRate = cpuUsage / 2;

            cpuInfo.setCpuSysUseRate(NumberUtil.roundStr(sysRate, 2) + "%");
            cpuInfo.setCpuUserUseRate(NumberUtil.roundStr(userRate, 2) + "%");
            cpuInfo.setCpuTotalUseRate(NumberUtil.round(cpuUsage, 2).doubleValue());
            cpuInfo.setCpuWaitRate("0%"); // 无法准确获取，设为0
            cpuInfo.setCpuFreeRate(NumberUtil.roundStr(100 - cpuUsage, 2) + "%");
        } catch (Exception e) {
            log.error("获取CPU信息失败", e);
            // 设置默认值
            cpuInfo.setCpuName("未知");
            cpuInfo.setCpuNum("未知");
            cpuInfo.setCpuPhysicalCoreNum("未知");
            cpuInfo.setCpuLogicalCoreNum(Runtime.getRuntime().availableProcessors() + "个逻辑核心");
            cpuInfo.setCpuSysUseRate("0%");
            cpuInfo.setCpuUserUseRate("0%");
            cpuInfo.setCpuTotalUseRate(0.0);
            cpuInfo.setCpuWaitRate("0%");
            cpuInfo.setCpuFreeRate("100%");
        }

        return cpuInfo;
    }

    /**
     * 从JMX获取CPU负载
     */
    private double getCpuLoadFromJMX() {
        try {
            OperatingSystemMXBean osBean = ManagementFactory.getOperatingSystemMXBean();

            // 尝试使用 getSystemLoadAverage 方法
            double systemLoadAverage = osBean.getSystemLoadAverage();
            if (systemLoadAverage >= 0) {
                // 将系统负载平均值转换为CPU使用率
                return systemLoadAverage / osBean.getAvailableProcessors();
            }

            // 如果 getSystemLoadAverage 不可用，尝试使用反射获取 getSystemCpuLoad 方法
            try {
                java.lang.reflect.Method method = osBean.getClass().getMethod("getSystemCpuLoad");
                method.setAccessible(true);
                Double cpuLoad = (Double) method.invoke(osBean);
                if (cpuLoad != null && cpuLoad >= 0) {
                    return cpuLoad;
                }
            } catch (Exception e) {
                log.debug("无法通过反射获取CPU负载", e);
            }

            // 如果上述方法都失败，尝试使用反射获取 getProcessCpuLoad 方法
            try {
                java.lang.reflect.Method method = osBean.getClass().getMethod("getProcessCpuLoad");
                method.setAccessible(true);
                Double cpuLoad = (Double) method.invoke(osBean);
                if (cpuLoad != null && cpuLoad >= 0) {
                    return cpuLoad;
                }
            } catch (Exception e) {
                log.debug("无法通过反射获取进程CPU负载", e);
            }

            // 如果所有方法都失败，返回默认值
            return 0.1; // 默认10%负载
        } catch (Exception e) {
            log.error("获取CPU负载失败", e);
            return 0.0;
        }
    }

    /**
     * 获取服务器网络情况
     *
     * <AUTHOR>
     * @date 2023/7/27
     */
    @Override
    public DevMonitorServerResult networkInfo(){
        DevMonitorServerResult devMonitorServerResult = new DevMonitorServerResult();
        // 网络信息
        DevMonitorServerResult.DevMonitorNetworkInfo devMonitorNetworkInfo = new DevMonitorServerResult.DevMonitorNetworkInfo();
        Map<String, String> networkUpRate = CommonNetWorkInfoUtil.getNetworkUpRate();
        devMonitorNetworkInfo.setUpLinkRate(networkUpRate.get("UP"));
        devMonitorNetworkInfo.setDownLinkRate(networkUpRate.get("DOWN"));
        devMonitorServerResult.setDevMonitorNetworkInfo(devMonitorNetworkInfo);
        return devMonitorServerResult;
    }
}