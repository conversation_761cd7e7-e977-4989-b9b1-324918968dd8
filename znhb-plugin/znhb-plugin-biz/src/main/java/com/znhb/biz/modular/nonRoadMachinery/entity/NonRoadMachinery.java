/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package com.znhb.biz.modular.nonRoadMachinery.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.znhb.common.pojo.CommonEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 非道路移动机械信息表实体
 *
 * <AUTHOR>
 * @date  2025/04/03 10:31
 **/
@Getter
@Setter
@TableName("t_non_road_machinery")
public class NonRoadMachinery extends CommonEntity {

    /** 主键ID */
    @TableId
    @ApiModelProperty(value = "主键ID", position = 1)
    private String id;

    /** 环保编码 */
    @ApiModelProperty(value = "环保编码", position = 2)
    private String envRegCode;

    /** 生产日期 */
    @ApiModelProperty(value = "生产日期", position = 3)
    private String productionDate;

    /** 车牌号码 */
    @ApiModelProperty(value = "车牌号码", position = 4)
    private String plateNumber;

    /** 排放标准 */
    @ApiModelProperty(value = "排放标准", position = 5)
    private String emissionStandard;

    /** 燃料类型 */
    @ApiModelProperty(value = "燃料类型", position = 6)
    private String fuelType;

    /** 机械种类 */
    @ApiModelProperty(value = "机械种类", position = 7)
    private String machineryType;

    /** 环保代码 */
    @ApiModelProperty(value = "环保代码", position = 8)
    private String pinCode;

    /** 机械型号 */
    @ApiModelProperty(value = "机械型号", position = 9)
    private String machineryModel;

    /** 发动机型号 */
    @ApiModelProperty(value = "发动机型号", position = 10)
    private String engineModel;

    /** 发动机生产厂 */
    @ApiModelProperty(value = "发动机生产厂", position = 11)
    private String engineManufacturer;

    /** 发动机编号 */
    @ApiModelProperty(value = "发动机编号", position = 12)
    private String engineSerialNumber;

    /** 整车(机)铭牌照片 */
    @ApiModelProperty(value = "整车(机)铭牌照片", position = 13)
    private String machineryNameplateImage;

    /** 发动机铭牌照片 */
    @ApiModelProperty(value = "发动机铭牌照片", position = 14)
    private String engineNameplateImage;

    /** 机械环保信息标签照片 */
    @ApiModelProperty(value = "机械环保信息标签照片", position = 15)
    private String envInfoLabelImage;

    /** 所属人 */
    @ApiModelProperty(value = "所属人", position = 16)
    private String ownerInfo;

    /** 同步状态 */
    @ApiModelProperty(value = "同步状态", position = 17)
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String syncStatus;

    /** 车辆类别 */
    @ApiModelProperty(value = "车辆类别", position = 23)
    @TableField(exist = false)
    private String vehicleCategory;

    /** 环保二维码图片 */
    @ApiModelProperty(value = "环保二维码图片", position = 24)
    private String envQrCodeImage;

}
