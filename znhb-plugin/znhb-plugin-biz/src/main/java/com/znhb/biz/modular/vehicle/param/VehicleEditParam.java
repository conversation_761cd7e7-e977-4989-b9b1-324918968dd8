/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package com.znhb.biz.modular.vehicle.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 车辆信息编辑参数
 *
 * <AUTHOR>
 * @date  2025/03/13 14:34
 **/
@Getter
@Setter
public class VehicleEditParam {

    /** id */
    @ApiModelProperty(value = "id", required = true, position = 1)
    @NotBlank(message = "id不能为空")
    private String id;

    /** 车牌号码 */
    @ApiModelProperty(value = "车牌号码", position = 2)
    private String plateNumber;

    /** 车牌颜色 */
    @ApiModelProperty(value = "车牌颜色", position = 3)
    private String plateColor;

    /** 车辆类型 */
    @ApiModelProperty(value = "车辆类型", position = 4)
    private String vehicleType;

    /** 车辆类别 */
    @ApiModelProperty(value = "车辆类别", position = 4)
    private String vehicleCategory;

    /** 所有人 */
    @ApiModelProperty(value = "所有人", position = 5)
    private String name;

    /** 住址 */
    @ApiModelProperty(value = "住址", position = 6)
    private String address;

    /** 使用性质 */
    @ApiModelProperty(value = "使用性质", position = 7)
    private String useCharacter;

    /** 品牌型号 */
    @ApiModelProperty(value = "品牌型号", position = 8)
    private String model;

    /** 发动机号码 */
    @ApiModelProperty(value = "发动机号码", position = 9)
    private String engineNo;

    /** 车辆识别代号 */
    @ApiModelProperty(value = "车辆识别代号", position = 10)
    private String vin;

    /** 注册日期 */
    @ApiModelProperty(value = "注册日期", position = 11)
    private String registerDate;

    /** 发证日期 */
    @ApiModelProperty(value = "发证日期", position = 12)
    private String issueDate;

    /** 发证机关 */
    @ApiModelProperty(value = "发证机关", position = 13)
    private String issuingAuthority;

    /** 档案编号 */
    @ApiModelProperty(value = "档案编号", position = 14)
    private String fileNo;

    /** 核定载人数 */
    @ApiModelProperty(value = "核定载人数", position = 15)
    private String approvedPassengers;

    /** 总质量 */
    @ApiModelProperty(value = "总质量", position = 16)
    private String grossMass;

    /** 整备质量 */
    @ApiModelProperty(value = "整备质量", position = 17)
    private String unladenMass;

    /** 核定载质量 */
    @ApiModelProperty(value = "核定载质量", position = 18)
    private String approvedLoad;

    /** 外廓尺寸 */
    @ApiModelProperty(value = "外廓尺寸", position = 19)
    private String dimension;

    /** 准牵引总质量 */
    @ApiModelProperty(value = "准牵引总质量", position = 20)
    private String tractionMass;

    /** 备注 */
    @ApiModelProperty(value = "备注", position = 21)
    private String remarks;

    /** 检验记录 */
    @ApiModelProperty(value = "检验记录", position = 22)
    private String inspectionRecord;

    /** 条码号 */
    @ApiModelProperty(value = "条码号", position = 23)
    private String codeNumber;

    /** 能源类型 */
    @ApiModelProperty(value = "能源类型", position = 24)
    private String energyType;

    /** 联网状态 */
    @ApiModelProperty(value = "联网状态", position = 25)
    private String netStatus;

    /** 审核时间 */
    @ApiModelProperty(value = "审核时间", position = 26)
    private Date verifyTime;

    /** 审核状态 */
    @ApiModelProperty(value = "审核状态", position = 27)
    private String verifyStatus;

    /** 审核人 */
    @ApiModelProperty(value = "审核人", position = 28)
    private String verifyUser;

    /** 审核人名称 */
    @ApiModelProperty(value = "审核人名称", position = 29)
    private String verifyUserName;

    /** 审核备注 */
    @ApiModelProperty(value = "审核备注", position = 30)
    private String verifyRemark;

    /** 排放标准 */
    @ApiModelProperty(value = "排放标准", position = 31)
    private String emissionStage;

    /** 行驶证上传图片（两页） */
    @ApiModelProperty(value = "行驶证上传图片（两页）", position = 32)
    private String drivingLicenseImgUrl;

    /** 行驶证首页上传图片 */
    @ApiModelProperty(value = "行驶证首页上传图片", position = 33)
    private String drivingLicenseFrontImgUrl;

    /** 行驶证副页上传图片 */
    @ApiModelProperty(value = "行驶证副页上传图片", position = 34)
    private String drivingLicenseBackImgUrl;

    /** 环保清单纸质图片上传 */
    @ApiModelProperty(value = "环保清单纸质图片上传", position = 35)
    private String environmentalListImgUrl;

    /** 环保清单电子图片上传 */
    @ApiModelProperty(value = "环保清单电子图片上传", position = 36)
    private String environmentalListElectronicImgUrl;

    /** 重型柴油车排放阶段查询截图 */
    @ApiModelProperty(value = "重型柴油车排放阶段查询截图", position = 37)
    private String heavyVehicleEmissionStageImgUrl;

    /** 机构id */
    @ApiModelProperty(value = "机构id", position = 38)
    private String orgId;

    /** 车牌颜色 */
    @ApiModelProperty(value = "车牌颜色", position = 37)
    private String plateColorName;

    /** 联系人 */
    @ApiModelProperty(value = "联系人", position = 38)
    private String contactName;

    /** 联系人电话 */
    @ApiModelProperty(value = "联系人电话", position = 39)
    private String contactPhone;

    /** 车头照片 */
    @ApiModelProperty(value = "车头照片", position = 40)
    private String carImgUrl;

}
