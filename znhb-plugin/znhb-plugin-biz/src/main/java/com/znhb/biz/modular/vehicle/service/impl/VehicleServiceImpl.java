/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package com.znhb.biz.modular.vehicle.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.znhb.biz.core.enums.SyncStatusEnum;
import com.znhb.biz.core.enums.VehicleCategoryEnum;
import com.znhb.biz.core.enums.VerifyStatusEnum;
import com.znhb.biz.modular.car.entity.CarInfo;
import com.znhb.biz.modular.car.service.CarInfoService;
import com.znhb.biz.modular.internalVehicle.entity.InternalVehicle;
import com.znhb.biz.modular.internalVehicle.service.InternalVehicleService;
import com.znhb.biz.modular.nonRoadMachinery.entity.NonRoadMachinery;
import com.znhb.biz.modular.nonRoadMachinery.service.NonRoadMachineryService;
import com.znhb.biz.modular.vehicle.param.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.znhb.auth.core.pojo.SaBaseLoginUser;
import com.znhb.auth.core.util.StpLoginUserUtil;
import com.znhb.biz.modular.org.entity.BizOrg;
import com.znhb.biz.modular.org.service.BizOrgService;
import com.znhb.biz.modular.vehicle.result.VehicleExportResult;
import com.znhb.common.enums.CommonSortOrderEnum;
import com.znhb.common.exception.CommonException;
import com.znhb.common.page.CommonPageRequest;
import com.znhb.biz.modular.vehicle.entity.Vehicle;
import com.znhb.biz.modular.vehicle.mapper.VehicleMapper;
import com.znhb.biz.modular.vehicle.service.VehicleService;

import com.znhb.common.excel.CommonExcelExportUtil;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * 车辆信息Service接口实现类
 *
 * <AUTHOR>
 * @date 2025/03/13 14:34
 **/
@Service
@Slf4j
public class VehicleServiceImpl extends ServiceImpl<VehicleMapper, Vehicle> implements VehicleService {



    @Resource
    private  InternalVehicleService internalVehicleService;

    @Resource
    private CarInfoService carInfoService;

    @Resource
    private NonRoadMachineryService nonRoadMachineryService;



    @Override
    public Page<Vehicle> page(VehiclePageParam vehiclePageParam) {
        QueryWrapper<Vehicle> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotEmpty(vehiclePageParam.getPlateNumber())) {
            queryWrapper.lambda().like(Vehicle::getPlateNumber, vehiclePageParam.getPlateNumber());
        }
        if (ObjectUtil.isNotEmpty(vehiclePageParam.getNetStatus())) {
            queryWrapper.lambda().eq(Vehicle::getNetStatus, vehiclePageParam.getNetStatus());
        }
        if (ObjectUtil.isNotEmpty(vehiclePageParam.getStartVerifyTime()) && ObjectUtil.isNotEmpty(vehiclePageParam.getEndVerifyTime())) {
            queryWrapper.lambda().between(Vehicle::getVerifyTime, vehiclePageParam.getStartVerifyTime(), vehiclePageParam.getEndVerifyTime());
        }
        if (ObjectUtil.isNotEmpty(vehiclePageParam.getVerifyStatus())) {
            queryWrapper.lambda().eq(Vehicle::getVerifyStatus, vehiclePageParam.getVerifyStatus());
        }
        if (ObjectUtil.isNotEmpty(vehiclePageParam.getEmissionStage())) {
            queryWrapper.lambda().eq(Vehicle::getEmissionStage, vehiclePageParam.getEmissionStage());
        }
        if (ObjectUtil.isAllNotEmpty(vehiclePageParam.getSortField(), vehiclePageParam.getSortOrder())) {
            CommonSortOrderEnum.validate(vehiclePageParam.getSortOrder());
            queryWrapper.orderBy(true, vehiclePageParam.getSortOrder().equals(CommonSortOrderEnum.ASC.getValue()),
                    StrUtil.toUnderlineCase(vehiclePageParam.getSortField()));
        } else {
            queryWrapper.lambda().orderByDesc(Vehicle::getUpdateTime);
        }
        return this.page(CommonPageRequest.defaultPage(), queryWrapper);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void add(VehicleAddParam vehicleAddParam) {

        QueryWrapper<Vehicle> queryWrapper = new QueryWrapper<>();
        //根据车牌查询是否存在未审核的相同车辆  有则抛出异常
        queryWrapper.lambda().eq(Vehicle::getPlateNumber, vehicleAddParam.getPlateNumber());
        queryWrapper.lambda().eq(Vehicle::getVerifyStatus, VerifyStatusEnum.NOT_VERIFY.getCode());
        Vehicle oldVehicle = this.getOne(queryWrapper);
        if (ObjectUtil.isNotEmpty(oldVehicle)) {
            throw new CommonException("车辆已提交审核，请耐心等待。");
        }

        checkVehicle(vehicleAddParam.getPlateNumber());
        Vehicle vehicle = BeanUtil.toBean(vehicleAddParam, Vehicle.class);
        //填充对接部门名称
        BizOrgService bizOrgService = SpringUtil.getBean(BizOrgService.class);
        if (ObjectUtil.isNotEmpty(vehicleAddParam.getOrgId())) {
            BizOrg bizOrg = bizOrgService.queryEntity(vehicleAddParam.getOrgId());
            vehicle.setOrgName(bizOrg.getName());
        }
        //设置车辆类型为外部车辆
        vehicle.setVehicleCategory(VehicleCategoryEnum.EXTERNAL.getCode());
        this.save(vehicle);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(VehicleEditParam vehicleEditParam) {
        Vehicle vehicle = this.queryEntity(vehicleEditParam.getId());
        BeanUtil.copyProperties(vehicleEditParam, vehicle);
        //填充对接部门名称
        BizOrgService bizOrgService = SpringUtil.getBean(BizOrgService.class);
        if (ObjectUtil.isNotEmpty(vehicleEditParam.getOrgId())) {
            BizOrg bizOrg = bizOrgService.queryEntity(vehicleEditParam.getOrgId());
            vehicle.setOrgName(bizOrg.getName());
        }

        this.updateById(vehicle);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<VehicleIdParam> vehicleIdParamList) {
        // 执行删除
        this.removeByIds(CollStreamUtil.toList(vehicleIdParamList, VehicleIdParam::getId));
    }

    @Override
    public Vehicle detail(VehicleIdParam vehicleIdParam) {
        return this.queryEntity(vehicleIdParam.getId());
    }

    @Override
    public Vehicle detail(VehicleNumberParam vehicleNumberParam) {
        return this.queryNumberEntity(vehicleNumberParam.getPlateNumber());
    }

    @Override
    public Vehicle queryEntity(String id) {
        Vehicle vehicle = this.getById(id);
        if (ObjectUtil.isEmpty(vehicle)) {
            throw new CommonException("车辆信息不存在，id值为：{}", id);
        }
        return vehicle;
    }

    @Override
    public Vehicle queryNumberEntity(String plateNumber) {
        QueryWrapper<Vehicle> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(Vehicle::getPlateNumber, plateNumber);
        queryWrapper.lambda().eq(Vehicle::getVerifyStatus, VerifyStatusEnum.VERIFY_SUCCESS.getCode());
        return this.getOne(queryWrapper);
    }

    /**
     * 车辆审核
     *
     * @param vehicleAuditParam 审核参数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void audit(VehicleAuditParam vehicleAuditParam) {
        // 获取当前登录用户信息
        SaBaseLoginUser loginUser = StpLoginUserUtil.getLoginUser();

        // 查询车辆信息
        Vehicle vehicle = this.queryEntity(vehicleAuditParam.getId());
        if (vehicle == null) {
            throw new CommonException("车辆不存在");
        }

        // 更新审核信息
        vehicle.setVerifyStatus(vehicleAuditParam.getVerifyStatus());
        vehicle.setVerifyRemark(vehicleAuditParam.getVerifyRemark());

        // 如果前端未传递审核时间，则使用当前时间
        if (vehicleAuditParam.getVerifyTime() != null) {
            vehicle.setVerifyTime(vehicleAuditParam.getVerifyTime());
        } else {
            vehicle.setVerifyTime(DateUtil.parseDateTime(DateUtil.now()));
        }

        // 设置审核人信息
        vehicle.setVerifyUser(loginUser.getId());
        vehicle.setVerifyUserName(loginUser.getName());

        //AuditState：1：通过，2：未通过
        //TruckLicense：车牌号
        //AuditBy：审核人
        //AuditRemark：审核说明
        Map<String, Object> params = new HashMap<>();
        params.put("AuditState", Integer.parseInt(vehicleAuditParam.getVerifyStatus()));
        params.put("TruckLicense", vehicle.getPlateNumber());
        params.put("AuditBy", loginUser.getName());
        params.put("AuditRemark", vehicleAuditParam.getVerifyRemark());
        log.info("审核接口入参：{}", JSONUtil.toJsonStr(params));
        String content = HttpUtil.createPost("https://smis.pzhgcrg.com/api/v1/erp/partner/license/audit")
                .header("appid", "20")
                .header("Authorization", "rqya9wcgpt8wdnthaxit7e6az5cqb6cd")
                .body(JSONUtil.toJsonStr(params))
                .execute().body();
        log.info("审核接口响应结果{}", content);
        // 处理审核结果
        if (ObjectUtil.isNotEmpty(content)) {
            JSONObject obj = JSONUtil.parseObj(content);
            Integer errcode = obj.getInt("errcode");
            String errmsg = obj.getStr("errmsg");
            if (errcode != 0) {
                throw new CommonException("车辆审核失败：{}", errmsg);
            }
        }

        // 更新车辆信息
        this.updateById(vehicle);
    }

    /**
     * 导出车辆信息
     *
     * @param vehiclePageParam 查询参数
     * @param response         HTTP响应对象
     */
    @Override
    public void export(VehiclePageParam vehiclePageParam, HttpServletResponse response) {
        try {
            // 构建查询条件
            QueryWrapper<Vehicle> queryWrapper = new QueryWrapper<>();

            // 添加查询条件
            if (ObjectUtil.isNotEmpty(vehiclePageParam.getPlateNumber())) {
                queryWrapper.lambda().like(Vehicle::getPlateNumber, vehiclePageParam.getPlateNumber());
            }
            if (ObjectUtil.isNotEmpty(vehiclePageParam.getNetStatus())) {
                queryWrapper.lambda().eq(Vehicle::getNetStatus, vehiclePageParam.getNetStatus());
            }
            if (ObjectUtil.isNotEmpty(vehiclePageParam.getVerifyStatus())) {
                queryWrapper.lambda().eq(Vehicle::getVerifyStatus, vehiclePageParam.getVerifyStatus());
            }
            if (ObjectUtil.isNotEmpty(vehiclePageParam.getEmissionStage())) {
                queryWrapper.lambda().eq(Vehicle::getEmissionStage, vehiclePageParam.getEmissionStage());
            }

            // 处理审核时间范围查询
            if (ObjectUtil.isNotEmpty(vehiclePageParam.getStartVerifyTime()) && ObjectUtil.isNotEmpty(vehiclePageParam.getEndVerifyTime())) {
                queryWrapper.lambda().between(Vehicle::getVerifyTime, vehiclePageParam.getStartVerifyTime(), vehiclePageParam.getEndVerifyTime());
            }

            // 查询数据
            List<Vehicle> vehicleList = this.list(queryWrapper);

            // 转换为导出数据
            List<VehicleExportResult> vehicleExportResultList = new ArrayList<>();

            for (Vehicle vehicle : vehicleList) {
                VehicleExportResult vehicleExportResult = new VehicleExportResult();
                BeanUtil.copyProperties(vehicle, vehicleExportResult);

                // 添加其他转换逻辑，如字典项处理等

                vehicleExportResultList.add(vehicleExportResult);
            }

            // 使用通用工具类导出
            CommonExcelExportUtil.exportExcel(vehicleExportResultList, VehicleExportResult.class, "车辆信息", "车辆信息", response);
        } catch (Exception e) {
            log.error(">>> 车辆信息导出失败：", e);
        }
    }

    @Override
    public Vehicle loadVehicleByPlateNumber(VehicleNumberAndColorParam vehicleNumberAndColorParam) {
        QueryWrapper<Vehicle> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(Vehicle::getPlateNumber, vehicleNumberAndColorParam.getPlateNumber());
        queryWrapper.lambda().eq(Vehicle::getPlateColor, vehicleNumberAndColorParam.getPlateColor());
        Vehicle vehicle = this.getOne(queryWrapper);
        if (ObjectUtil.isEmpty(vehicle)) {
            throw new CommonException("车辆信息不存在，车牌号码为：{}", vehicleNumberAndColorParam.getPlateNumber());
        }
        if (vehicle.getVerifyStatus().equals(VerifyStatusEnum.NOT_VERIFY.getCode())) {
            throw new CommonException("车辆未审核，车牌号码为：{}", vehicleNumberAndColorParam.getPlateNumber());
        }
        if (VerifyStatusEnum.VERIFY_FAIL.getCode().equals(vehicle.getVerifyStatus())) {
            throw new CommonException("车辆审核未通过，车牌号码为：{}", vehicleNumberAndColorParam.getPlateNumber());
        }
        return vehicle;
    }

    @Override
    public Object loadVehicleForOrder(VehicleNumberParam vehicleNumberParam) {
        List<Object> list = new ArrayList<>();
        QueryWrapper<Vehicle> vehicleQueryWrapper = new QueryWrapper<>();
        vehicleQueryWrapper.lambda().like(Vehicle::getPlateNumber, vehicleNumberParam.getPlateNumber());
        List<Vehicle> vehicleList = this.list(vehicleQueryWrapper);
        if (ObjectUtil.isNotEmpty(vehicleList)) {
            for (Vehicle vehicle : vehicleList) {
                vehicle.setVehicleCategory(VehicleCategoryEnum.EXTERNAL.getCode());
            }
            list.addAll(vehicleList);
        }
        QueryWrapper<InternalVehicle> internalQueryWrapper = new QueryWrapper<>();
        internalQueryWrapper.lambda().like(InternalVehicle::getPlateNumber, vehicleNumberParam.getPlateNumber());
        List<InternalVehicle> internalVehicleList = SpringUtil.getBean(InternalVehicleService.class).list(internalQueryWrapper);
        if (ObjectUtil.isNotEmpty(internalVehicleList)) {
            for (InternalVehicle internalVehicle : internalVehicleList) {
                internalVehicle.setVehicleCategory(VehicleCategoryEnum.INTERNAL.getCode());
            }
            list.addAll(internalVehicleList);
        }
        QueryWrapper<NonRoadMachinery> nonRoadMachineryQueryWrapper = new QueryWrapper<>();
        nonRoadMachineryQueryWrapper.lambda().like(NonRoadMachinery::getPlateNumber, vehicleNumberParam.getPlateNumber());
        List<NonRoadMachinery> nonRoadMachineryList = SpringUtil.getBean(NonRoadMachineryService.class).list(nonRoadMachineryQueryWrapper);
        if (ObjectUtil.isNotEmpty(nonRoadMachineryList)) {
            for (NonRoadMachinery nonRoadMachinery : nonRoadMachineryList) {
                nonRoadMachinery.setVehicleCategory(VehicleCategoryEnum.NON_MOVING.getCode());
            }
            list.addAll(nonRoadMachineryList);
        }
        if (!list.isEmpty()) {
            return list;
        }
        throw new CommonException("车辆不存在，车牌号码为：{}", vehicleNumberParam.getPlateNumber());
    }

    @Override
    public List<Vehicle> queryVehicleForSync() {
        QueryWrapper<Vehicle> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(Vehicle::getSyncStatus, SyncStatusEnum.NOT_SYNC.getCode());
        queryWrapper.lambda().eq(Vehicle::getVerifyStatus, VerifyStatusEnum.VERIFY_SUCCESS.getCode());
        //修改时间范围在当天至当天结束
        queryWrapper.lambda().between(Vehicle::getUpdateTime, DateUtil.beginOfDay(DateUtil.parseDateTime(DateUtil.now())), DateUtil.endOfDay(DateUtil.parseDateTime(DateUtil.now())));
        queryWrapper.lambda().orderByAsc(Vehicle::getUpdateTime);
        return this.list(queryWrapper);
    }

    @Override
    public void updateForSync(String id) {
        UpdateWrapper<Vehicle> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda().eq(Vehicle::getId, id);
        updateWrapper.lambda().set(Vehicle::getSyncStatus, SyncStatusEnum.SYNCED.getCode());
        this.update(updateWrapper);
    }


    private void checkVehicle(String plateNumber) {
        InternalVehicle vehicle = internalVehicleService.queryNumberEntity(plateNumber);
        if (ObjectUtil.isNotEmpty(vehicle)) {
            throw new CommonException("该车辆已备案为内部车辆");
        }
        CarInfo carInfo = carInfoService.queryByPlateNumber(plateNumber);
        if (ObjectUtil.isNotEmpty(carInfo)) {
            throw new CommonException("该车辆已备案为小车车辆");
        }
        NonRoadMachinery nonRoadMachinery = nonRoadMachineryService.queryNumberEntity(plateNumber);
        if (ObjectUtil.isNotEmpty(nonRoadMachinery)) {
            throw new CommonException("该车辆已备案为非移机械");
        }
    }
}
