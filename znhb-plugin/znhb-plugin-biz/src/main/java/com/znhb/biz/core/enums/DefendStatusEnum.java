package com.znhb.biz.core.enums;

import lombok.Getter;

@Getter
public enum DefendStatusEnum {

    //未布防
    NOT_DEFEND("NOT_DEFEND"),

    //无需布防
    NOT_NEED_DEFEND("NOT_NEED_DEFEND"),

    //布防成功
    DEFEND_SUCCESS("DEFEND_SUCCESS"),

    //布防失败
    DEFEND_FAIL("DEFEND_FAIL"),

    //未登录
    NOT_LOGIN("NOT_LOGIN"),

    //登录失败
    LOGIN_FAIL("LOGIN_FAIL");



    private final String code;

    DefendStatusEnum(String code) {
        this.code = code;
    }

}
