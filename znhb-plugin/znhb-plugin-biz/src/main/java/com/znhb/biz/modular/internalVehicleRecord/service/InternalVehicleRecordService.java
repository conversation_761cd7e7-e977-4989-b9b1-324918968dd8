/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package com.znhb.biz.modular.internalVehicleRecord.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.znhb.biz.modular.internalVehicle.entity.InternalVehicle;
import com.znhb.biz.modular.internalVehicleRecord.entity.InternalVehicleRecord;
import com.znhb.biz.modular.internalVehicleRecord.param.InternalVehicleRecordAddParam;
import com.znhb.biz.modular.internalVehicleRecord.param.InternalVehicleRecordEditParam;
import com.znhb.biz.modular.internalVehicleRecord.param.InternalVehicleRecordIdParam;
import com.znhb.biz.modular.internalVehicleRecord.param.InternalVehicleRecordPageParam;

import javax.validation.Valid;
import java.util.List;

/**
 * 厂内运输台账Service接口
 *
 * <AUTHOR>
 * @date 2025/04/03 11:37
 **/
public interface InternalVehicleRecordService extends IService<InternalVehicleRecord> {

    /**
     * 获取厂内运输台账分页
     *
     * <AUTHOR>
     * @date 2025/04/03 11:37
     */
    Page<InternalVehicleRecord> page(InternalVehicleRecordPageParam internalVehicleRecordPageParam);

    /**
     * 添加厂内运输台账
     *
     * <AUTHOR>
     * @date 2025/04/03 11:37
     */
    void add(InternalVehicleRecordAddParam internalVehicleRecordAddParam);

    /**
     * 编辑厂内运输台账
     *
     * <AUTHOR>
     * @date 2025/04/03 11:37
     */
    void edit(InternalVehicleRecordEditParam internalVehicleRecordEditParam);

    /**
     * 删除厂内运输台账
     *
     * <AUTHOR>
     * @date 2025/04/03 11:37
     */
    void delete(List<InternalVehicleRecordIdParam> internalVehicleRecordIdParamList);

    /**
     * 获取厂内运输台账详情
     *
     * <AUTHOR>
     * @date 2025/04/03 11:37
     */
    InternalVehicleRecord detail(InternalVehicleRecordIdParam internalVehicleRecordIdParam);

    /**
     * 获取厂内运输台账详情
     *
     * <AUTHOR>
     * @date 2025/04/03 11:37
     **/
    InternalVehicleRecord queryEntity(String id);

    /**
     * 获取厂内运输台账详情
     *
     * <AUTHOR>
     * @date 2025/04/03 11:37
     **/
    InternalVehicleRecord queryLastInRecord(String plateNumber);

    /**
     * 更新出厂信息
     *
     * <AUTHOR>
     */
    void updateForOut(InternalVehicleRecord internalVehicleRecord);

    /**
     * 厂内运输台账同步
     *
     * <AUTHOR>
     */
    List<InternalVehicleRecord> queryInternalVehicleAccessRecord();

    /**
     * 同步状态更新
     */
    void updateForSync(String id);

    /**
     * 更新厂内运输台账的车辆基础信息
     *
     * <AUTHOR>
     */
    void updateInternalVehicleRecord(InternalVehicle internalVehicle);

    /**
     * 确认出厂
     */
    void confirmExit(InternalVehicleRecordIdParam internalVehicleRecordIdParam);
}
