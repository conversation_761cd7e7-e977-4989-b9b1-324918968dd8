package com.znhb.biz.api;

import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.znhb.dev.api.DevConfigApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;

@Component
@Slf4j
public class SuperLowerApi {


    @Resource
    private DevConfigApi devConfigApi;


    /**
     * 外部车辆备案数据推送
     */
    public boolean pushVehicle(Map<String, Object> map) {
        try {
            String url = devConfigApi.getEnvBaseUrl() + "carFiling";
            String res = HttpUtil.post(url, JSONUtil.toJsonStr(map));
            log.info("外部车辆备案数据推送结果：{}", res);
            JSONObject obj = JSONUtil.parseObj(res);
            return obj.getInt("code") == 200;
        } catch (Exception e) {
            e.printStackTrace();
            log.error("外部车辆备案数据推送异常：{}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 内部车辆备案数据推送
     */
    public boolean pushInternalVehicle(Map<String, Object> map) {
        try {
            String url = devConfigApi.getEnvBaseUrl() + "addFieldTransportCar";
            String res = HttpUtil.post(url, JSONUtil.toJsonStr(map));
            log.info("内部车辆备案数据推送结果：{}", res);
            JSONObject obj = JSONUtil.parseObj(res);
            return obj.getInt("code") == 200;
        } catch (Exception e) {
            e.printStackTrace();
            log.error("内部车辆备案数据推送异常：{}", e.getMessage(), e);
            return false;
        }
    }


    /**
     * 外部车辆运输台账推送
     */
    public boolean pushVehicleAccessRecord(Map<String, Object> param) {
        try {
            String url = devConfigApi.getEnvBaseUrl() + "outDoor";
            String res = HttpUtil.post(url, JSONUtil.toJsonStr(param));
            log.info("外部车辆运输台账推送结果：{}", res);
            JSONObject obj = JSONUtil.parseObj(res);
            return obj.getInt("code") == 200;
        } catch (Exception e) {
            e.printStackTrace();
            log.error("外部车辆运输台账推送异常：{}", e.getMessage(), e);
            return false;
        }

    }

    /**
     * 内部车辆运输台账推送
     */
    public boolean pushInternalVehicleAccessRecord(Map<String, Object> param) {
        try {
            String url = devConfigApi.getEnvBaseUrl() + "addFieldTransportCarRecord";
            String res = HttpUtil.post(url, JSONUtil.toJsonStr(param));
            log.info("内部车辆运输台账推送结果：{}", res);
            JSONObject obj = JSONUtil.parseObj(res);
            return obj.getInt("code") == 200;
        } catch (Exception e) {
            e.printStackTrace();
            log.error("内部车辆运输台账推送异常：{}", e.getMessage(), e);
            return false;
        }

    }

    /**
     * 非移机械备案数据推送
     */
    public boolean pushNonRoadMachinery(Map<String, Object> map) {
        try {
            String url = devConfigApi.getEnvBaseUrl() + "addNotMoveCar";
            String res = HttpUtil.post(url, JSONUtil.toJsonStr(map));
            log.info("非移机械备案数据推送结果：{}", res);
            JSONObject obj = JSONUtil.parseObj(res);
            return obj.getInt("code") == 200;
        } catch (Exception e) {
            e.printStackTrace();
            log.error("非移机械备案数据推送异常：{}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 非移机械运输台账数据推送
     * @param map
     */
    public boolean pushNonRoadAccessRecord(Map<String, Object> map) {
        try {
            String url = devConfigApi.getEnvBaseUrl() + "addNotMoveCarRecord";
            String res = HttpUtil.post(url, JSONUtil.toJsonStr(map));
            log.info("非移机械运输台账数据推送结果：{}", res);
            JSONObject obj = JSONUtil.parseObj(res);
            return obj.getInt("code") == 200;
        }catch (Exception e) {
            e.printStackTrace();
            log.error("非移机械运输台账数据推送异常：{}", e.getMessage(), e);
            return false;
        }
    }


    /**
     * 物料数据上报
     */
    public boolean pushMaterial(Map<String, Object> map) {
        try {
            String url = devConfigApi.getEnvBaseUrl() + "addCategory";
            String res = HttpUtil.post(url, JSONUtil.toJsonStr(map));
            log.info("物料数据上报结果：{}", res);
            JSONObject obj = JSONUtil.parseObj(res);
            return obj.getInt("code") == 200;
        }catch (Exception e){
            e.printStackTrace();
            log.error("物料数据上报异常：{}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 小车通行台账
     * @param map
     * @return
     */
    public boolean pushCarAccessRecord(Map<String, Object> map) {
        try {
            String url = devConfigApi.getEnvBaseUrl() + "addExceptionOpen";
            String res = HttpUtil.post(url, JSONUtil.toJsonStr(map));
            log.info("小车通行台账数据上报结果：{}", res);
            JSONObject obj = JSONUtil.parseObj(res);
            return obj.getInt("code") == 200;
        }catch (Exception e){
            e.printStackTrace();
            log.error("小车通行台账数据上报异常：{}", e.getMessage(), e);
            return false;
        }
    }



}
