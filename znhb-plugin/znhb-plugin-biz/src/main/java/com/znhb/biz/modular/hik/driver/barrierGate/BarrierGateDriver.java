package com.znhb.biz.modular.hik.driver.barrierGate;

import cn.hutool.core.util.ObjectUtil;

import cn.hutool.core.util.StrUtil;
import lombok.Getter;
import lombok.Setter;
import com.znhb.biz.modular.barrierGate.entity.BarrierGate;
import com.znhb.biz.modular.hik.driver.camera.HikGateTailCameraDriver;
import com.znhb.biz.modular.hik.driver.camera.HikGateBodyCameraDriver;
import com.znhb.biz.modular.hik.driver.camera.HikGatePlateCameraDriver;
import com.znhb.biz.modular.hik.driver.led.HikLedDriver;
import com.znhb.biz.modular.hik.entity.LedMessage;

import java.util.concurrent.ConcurrentHashMap;

/**
 * 道闸设备驱动
 */
public class BarrierGateDriver {

    @Setter
    @Getter
    private BarrierGate barrierGate;

    private final Long EXPIRED_TIMES = 30000L;

    private final ConcurrentHashMap<String, Long> plateCache = new ConcurrentHashMap<>();

    @Setter
    @Getter
    private String plateNumber = null;

    @Setter
    @Getter
    private String emissionName = null;

    /**
     * 主车牌抓拍机
     */
    @Setter
    private HikGatePlateCameraDriver mainPlateCameraDriver;

    /**
     * 副车牌抓拍机
     */
    @Setter
    private HikGatePlateCameraDriver secondPlateCameraDriver;

    /**
     * 车身抓拍机
     */
    @Setter
    @Getter
    private HikGateBodyCameraDriver bodyCameraDriver;

    /**
     * 全景球机
     */
    @Setter
    @Getter
    private HikGateTailCameraDriver tailCameraDriver;

    @Setter
    @Getter
    private HikLedDriver ledDriver;

    /**
     * 利用ConcurrentHashMap的compute方法原子性地检查并更新时间戳。
     * 如果存在且在30秒内，返回旧时间，方法返回true；否则更新为新时间，返回false
     */
    public boolean plateDuplicateCheck(String plateNumber) {
        if (plateNumber == null || plateNumber.isEmpty()) {
            return false;
        }
        this.plateNumber = plateNumber;

        long currentTime = System.currentTimeMillis();
        return plateCache.compute(plateNumber, (key, oldTime) -> {
            if (oldTime != null && (currentTime - oldTime) <= EXPIRED_TIMES) {
                return oldTime; // 返回旧时间表示重复
            }
            return currentTime; // 返回新时间表示首次出现
        }) != currentTime; // 比较返回时间是否为当前时间
    }


    public boolean openGate() {
        return mainPlateCameraDriver.openGate();
    }

    public void closeGate() {
        mainPlateCameraDriver.closeGate();
    }


    //第一行固定显示车牌号 + 排放阶段
    public void displayLedMessage(String message, String code) {
        LedMessage ledMessage = new LedMessage();
        if (StrUtil.isNotEmpty(emissionName)) {
            ledMessage.setFirstLine(plateNumber + StrUtil.SPACE + emissionName);
        } else {
            ledMessage.setFirstLine(plateNumber);
        }
        ledMessage.setSecondLine(message);
        ledDriver.displayMessage(ledMessage,code);
    }


    public String bodyCapture(String plateNumber) {
        if (ObjectUtil.isNotNull(bodyCameraDriver)) {
            return bodyCameraDriver.capture(plateNumber);
        }
        return null;
    }

    public String tailCapture(String plateNumber) {
        if (ObjectUtil.isNotNull(tailCameraDriver)) {
            return tailCameraDriver.capture(plateNumber);
        }
        return null;
    }


    public void setLedDisplayInfo(String orgName, String ruleName, String ownerName,String vehicleType) {
        ledDriver.setOrgName(orgName);
        ledDriver.setRuleName(ruleName);
        ledDriver.setOwnerName(ownerName);
        ledDriver.setVehicleType(vehicleType);
    }
}
