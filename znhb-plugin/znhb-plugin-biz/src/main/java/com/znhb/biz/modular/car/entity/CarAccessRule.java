/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package com.znhb.biz.modular.car.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import com.znhb.common.pojo.CommonEntity;

import java.sql.Time;

/**
 * 小车进出规则实体
 *
 * <AUTHOR>
 * @date  2025/04/10 15:00
 **/
@Getter
@Setter
@TableName("t_car_access_rule")
public class CarAccessRule extends CommonEntity {

    /** id */
    @TableId
    @ApiModelProperty(value = "id", position = 1)
    private String id;

    /** 规则名称 */
    @ApiModelProperty(value = "规则名称", position = 2)
    private String ruleName;

    /** 允许进入开始时间 */
    @ApiModelProperty(value = "允许进入开始时间", position = 4)
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Time startTime;

    /** 允许进入结束时间 */
    @ApiModelProperty(value = "允许进入结束时间", position = 5)
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Time endTime;

    /** 每日最大进入次数 */
    @ApiModelProperty(value = "每日最大进入次数", position = 6)
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Integer maxCount;

    /** 最大停留时长(分钟) */
    @ApiModelProperty(value = "最大停留时长(分钟)", position = 8)
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Integer maxDuration;

    /** 最小停留时长(分钟) */
    @ApiModelProperty(value = "最小停留时长(分钟)", position = 8)
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Integer minDuration;

    /** 适用日期(1-7表示周一到周日,逗号分隔) */
    @ApiModelProperty(value = "适用日期(1-7表示周一到周日,逗号分隔)", position = 9)
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String applyDays;

    /** 规则优先级(数字越大优先级越高) */
    @ApiModelProperty(value = "规则优先级(数字越大优先级越高)", position = 10)
    private Integer priority;

    /** 状态(0-禁用,1-启用) */
    @ApiModelProperty(value = "状态(0-禁用,1-启用)", position = 11)
    private String status;

    /** 备注 */
    @ApiModelProperty(value = "备注", position = 12)
    private String remark;

    /** 启用时间限制 */
    @ApiModelProperty(value = "启用时间限制", position = 13)
    private boolean enableTimeRule;

    /** 启用次数限制 */
    @ApiModelProperty(value = "启用次数限制", position = 14)
    private boolean enableCountRule;

    /** 启用时长限制 */
    @ApiModelProperty(value = "启用时长限制", position = 16)
    private boolean enableDurationRule;

    /** 入开闸方式 */
    @ApiModelProperty(value = "入开闸方式")
    private boolean entryGateOpenType;

    /** 出开闸方式 */
    @ApiModelProperty(value = "出开闸方式")
    private boolean exitGateOpenType;

    /** 允许入厂道闸(逗号分隔) */
    @ApiModelProperty(value = "允许入道闸(逗号分隔)")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String allowedInGates;

    /** 允许出厂道闸(逗号分隔) */
    @ApiModelProperty(value = "允许出道闸(逗号分隔)")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String allowedOutGates;

    /** 启用区域限制 */
    @ApiModelProperty(value = "启用入厂道闸限制")
    private boolean enableInGateRule;

    /** 启用区域限制 */
    @ApiModelProperty(value = "启用出厂道闸限制")
    private boolean enableOutGateRule;

}
