/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package com.znhb.biz.modular.cyft.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.znhb.biz.modular.cyft.entity.CarRecordReport;
import com.znhb.biz.modular.cyft.param.CarRecordReportAddParam;
import com.znhb.biz.modular.cyft.param.CarRecordReportEditParam;
import com.znhb.biz.modular.cyft.param.CarRecordReportIdParam;
import com.znhb.biz.modular.cyft.param.CarRecordReportPageParam;

import java.util.List;

/**
 * CarRecordReportService接口
 *
 * <AUTHOR>
 * @date  2025/03/27 08:50
 **/
public interface CarRecordReportService extends IService<CarRecordReport> {

    /**
     * 获取CarRecordReport分页
     *
     * <AUTHOR>
     * @date  2025/03/27 08:50
     */
    Page<CarRecordReport> page(CarRecordReportPageParam carRecordReportPageParam);

    /**
     * 添加CarRecordReport
     *
     * <AUTHOR>
     * @date  2025/03/27 08:50
     */
    void add(CarRecordReportAddParam carRecordReportAddParam);

    /**
     * 编辑CarRecordReport
     *
     * <AUTHOR>
     * @date  2025/03/27 08:50
     */
    void edit(CarRecordReportEditParam carRecordReportEditParam);

    /**
     * 删除CarRecordReport
     *
     * <AUTHOR>
     * @date  2025/03/27 08:50
     */
    void delete(List<CarRecordReportIdParam> carRecordReportIdParamList);

    /**
     * 获取CarRecordReport详情
     *
     * <AUTHOR>
     * @date  2025/03/27 08:50
     */
    CarRecordReport detail(CarRecordReportIdParam carRecordReportIdParam);

    /**
     * 获取CarRecordReport详情
     *
     * <AUTHOR>
     * @date  2025/03/27 08:50
     **/
    CarRecordReport queryEntity(String id);

    /**
     * CarRecordReport同步数据
     *
     * <AUTHOR>
     * @date  2025/03/27 08:50
     **/
    List<CarRecordReport> queryListForSync();

    /**
     * CarRecordReport同步数据状态修改
     * @param id
     */
    void updateSyncStatus(String id);

}
