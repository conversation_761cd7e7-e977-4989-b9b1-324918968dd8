package com.znhb.biz.modular.ruigang.tasker;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.znhb.biz.core.enums.PlateColorEnum;
import com.znhb.biz.core.enums.VehicleCategoryEnum;
import com.znhb.biz.core.enums.VerifyStatusEnum;
import com.znhb.biz.modular.hik.constant.CommonConstant;
import com.znhb.biz.modular.vehicle.entity.Vehicle;
import com.znhb.biz.modular.vehicle.param.VehicleAddParam;
import com.znhb.biz.modular.vehicle.param.VehicleEditParam;
import com.znhb.biz.modular.vehicle.service.VehicleService;
import com.znhb.common.timer.CommonTimerTaskRunner;
import com.znhb.common.util.DictCacheUtil;
import com.znhb.dev.api.DevFileApi;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.Nullable;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 获取车辆备案信息从第三方平台
 */
@Slf4j
@Component
public class VehicleInfoLoadFromThirdPlatformTasker implements CommonTimerTaskRunner {

    @Resource
    private DictCacheUtil dictCacheUtil;

    @Resource
    private VehicleService vehicleService;

    @Resource
    private DevFileApi devFileApi;

    @Override
    public void action() {
        String url = "https://smis.pzhgcrg.com/api/v1/erp/partner/license";
        Map<String, Object> param = new HashMap<>();
        param.put("auditState", "0");
        Date date = new Date();
        param.put("startTime", DateUtil.offsetMinute(date, -5));
        param.put("endTime", DateUtil.offsetMinute(date, 5));
        String result = HttpUtil.createGet(url)
                .header("appid", "20")
                .header("Authorization", "rqya9wcgpt8wdnthaxit7e6az5cqb6cd")
                .form(param)
                .execute().body();
        log.info("未审核车辆信息：{}", result);
        JSONObject obj = JSONUtil.parseObj(result);
        Integer errcode = obj.getInt("errcode");
        String errmsg = obj.getStr("errmsg");
        if (errcode == 0) {
            JSONArray data = obj.getJSONArray("data");
            if (CollectionUtil.isNotEmpty(data)) {
                for (Object o : data) {
                    JSONObject row = (JSONObject) o;
                    String plateNumber = row.getStr("TruckLicense");
                    if (StrUtil.isNotBlank(plateNumber)) {
                        Vehicle vehicle = vehicleService.queryNumberEntity(plateNumber);

                        if (ObjectUtil.isEmpty(vehicle)) {
                            VehicleAddParam vehicleAddParam = new VehicleAddParam();
                            Integer plateColor = row.getInt("PlateColor");
                            String emissionStage = row.getStr("EmissionGrade");
                            vehicleAddParam.setPlateNumber(plateNumber);
                            vehicleAddParam.setPlateColor(String.valueOf(plateColor));
                            vehicleAddParam.setPlateColorName(dictCacheUtil.getDictLabel(PlateColorEnum.PLATE_COLOR,String.valueOf(plateColor)));
                            vehicleAddParam.setEmissionStage(emissionStage);
                            vehicleAddParam.setVin(row.getStr("Vin"));
                            vehicleAddParam.setEngineNo(row.getStr("DriverLicense"));
                            vehicleAddParam.setRegisterDate(DateUtil.format(obj.getDate("JoinTime"), "yyyy-MM-dd"));
                            vehicleAddParam.setVehicleType(row.getStr("VehicleType"));
                            vehicleAddParam.setVehicleCategory(VehicleCategoryEnum.EXTERNAL.getCode());
                            vehicleAddParam.setName(row.getStr("Team"));
                            vehicleAddParam.setModel(row.getStr("Brand"));
                            Integer powerType = row.getInt("PowerType");
                            /*
                            燃料类型，整形，1：‌汽油，2：‌柴油，3：天然气，4：乙醇燃料，5：氢燃料，6：生物燃料，7：‌煤油，8：混合燃料，9：其他
                             */
                            String tra_back_energy = getEnergy(powerType);
                            vehicleAddParam.setEnergyType(tra_back_energy);
                            vehicleAddParam.setNetStatus(String.valueOf(row.getInt("NetworkStatus")));
                            Integer vehicleNature = row.getInt("VehicleNature");
                            //使用性质，整形：1:家庭自用汽车，2：非营业客车，3：营业客车，4：非营业货车，5：营业货车，6：特种车，7：摩托车，8：拖拉机，9：挂车
                            String userCharacter = getUserCharacter(vehicleNature);
                            vehicleAddParam.setUseCharacter(userCharacter);
                            vehicleAddParam.setVerifyStatus(VerifyStatusEnum.NOT_VERIFY.getCode());

                            //随车清单
                            JSONArray vehiclePicList = row.getJSONArray("VehiclePicList");
                            if (!JSONUtil.isNull(vehiclePicList) && vehiclePicList.size() > 0) {
                                String environmentalListImgUrl = vehiclePicList.getStr(0);
                                vehicleAddParam.setEnvironmentalListImgUrl(saveImage(environmentalListImgUrl));
                            }

                            //车头照
                            JSONArray carImageUrlList = row.getJSONArray("InitImgList");

                            if (!JSONUtil.isNull(carImageUrlList) && carImageUrlList.size() > 0) {
                                String carImgUrl = carImageUrlList.getStr(0);
                                vehicleAddParam.setCarImgUrl(saveImage(carImgUrl));
                            }

                            //行驶证
                            JSONArray picList = row.getJSONArray("PicList");
                            if (!JSONUtil.isNull(picList) && picList.size() > 0) {
                                String frontUrl = picList.getStr(0);
                                String backUrl = picList.getStr(1);
                                vehicleAddParam.setDrivingLicenseFrontImgUrl(saveImage(frontUrl));
                                vehicleAddParam.setDrivingLicenseBackImgUrl(saveImage(backUrl));
                            }

                            vehicleService.add(vehicleAddParam);
                        }else{
                            //修改车辆信息
                            VehicleEditParam vehicleEditParam = new VehicleEditParam();
                            vehicleEditParam.setPlateNumber(plateNumber);
                            Integer plateColor = row.getInt("PlateColor");
                            String emissionStage = row.getStr("EmissionGrade");
                            vehicleEditParam.setPlateColor(String.valueOf(plateColor));
                            vehicleEditParam.setPlateColorName(dictCacheUtil.getDictLabel(PlateColorEnum.PLATE_COLOR,String.valueOf(plateColor)));
                            vehicleEditParam.setEmissionStage(emissionStage);
                            vehicleEditParam.setVin(row.getStr("Vin"));
                            vehicleEditParam.setEngineNo(row.getStr("DriverLicense"));
                            vehicleEditParam.setRegisterDate(DateUtil.format(obj.getDate("JoinTime"), "yyyy-MM-dd"));
                            vehicleEditParam.setVehicleType(row.getStr("VehicleType"));
                            vehicleEditParam.setVehicleCategory(VehicleCategoryEnum.EXTERNAL.getCode());
                            vehicleEditParam.setName(row.getStr("Team"));
                            vehicleEditParam.setModel(row.getStr("Brand"));
                            Integer powerType = row.getInt("PowerType");
                            /*
                            燃料类型，整形，1：‌汽油，2：‌柴油，3：天然气，4：乙醇燃料，5：氢燃料，6：生物燃料，7：‌煤油，8：混合燃料，9：其他
                             */
                            String tra_back_energy = getEnergy(powerType);
                            vehicleEditParam.setEnergyType(tra_back_energy);
                            vehicleEditParam.setNetStatus(String.valueOf(row.getInt("NetworkStatus")));
                            Integer vehicleNature = row.getInt("VehicleNature");
                            //使用性质，整形：1:家庭自用汽车，2：非营业客车，3：营业客车，4：非营业货车，5：营业货车，6：特种车，7：摩托车，8：拖拉机，9：挂车
                            String userCharacter = getUserCharacter(vehicleNature);
                            vehicleEditParam.setUseCharacter(userCharacter);
                            vehicleEditParam.setVerifyStatus(VerifyStatusEnum.NOT_VERIFY.getCode());

                            //随车清单
                            JSONArray vehiclePicList = row.getJSONArray("VehiclePicList");
                            if (!JSONUtil.isNull(vehiclePicList) && vehiclePicList.size() > 0) {
                                String environmentalListImgUrl = vehiclePicList.getStr(0);
                                vehicleEditParam.setEnvironmentalListImgUrl(saveImage(environmentalListImgUrl));
                            }

                            //车头照
                            JSONArray carImageUrlList = row.getJSONArray("InitImgList");

                            if (!JSONUtil.isNull(carImageUrlList) && carImageUrlList.size() > 0) {
                                String carImgUrl = carImageUrlList.getStr(0);
                                vehicleEditParam.setCarImgUrl(saveImage(carImgUrl));
                            }

                            //行驶证
                            JSONArray picList = row.getJSONArray("PicList");
                            if (!JSONUtil.isNull(picList) && picList.size() > 0) {
                                String frontUrl = picList.getStr(0);
                                String backUrl = picList.getStr(1);
                                vehicleEditParam.setDrivingLicenseFrontImgUrl(saveImage(frontUrl));
                                vehicleEditParam.setDrivingLicenseBackImgUrl(saveImage(backUrl));
                            }
                            vehicleEditParam.setId(vehicle.getId());
                            vehicleService.edit(vehicleEditParam);
                        }

                    }
                }
            }

        }


    }

    @Nullable
    private static String getUserCharacter(Integer vehicleNature) {
        String userCharacter = null;
        if (vehicleNature == 1) {
            userCharacter = "家庭自用汽车";
        }
        if (vehicleNature == 2) {
            userCharacter = "非营业客车";
        }
        if (vehicleNature == 3) {
            userCharacter = "营业客车";
        }
        if (vehicleNature == 4) {
            userCharacter = "非营业货车";
        }
        if (vehicleNature == 5) {
            userCharacter = "营业货车";
        }
        if (vehicleNature == 6) {
            userCharacter = "特种车";
        }
        if (vehicleNature == 7) {
            userCharacter = "摩托车";
        }
        if (vehicleNature == 8) {
            userCharacter = "拖拉机";
        }
        if (vehicleNature == 9) {
            userCharacter = "挂车";
        }
        return userCharacter;
    }

    @Nullable
    private static String getEnergy(Integer powerType) {
        String tra_back_energy = null;
        if (powerType == 1) {
            tra_back_energy = "汽油";
        }
        if (powerType == 2) {
            tra_back_energy = "柴油";
        }
        if (powerType == 3) {
            tra_back_energy = "天然气";
        }
        if (powerType == 4) {
            tra_back_energy = "乙醇燃料";
        }
        if (powerType == 5) {
            tra_back_energy = "氢燃料";
        }
        if (powerType == 6) {
            tra_back_energy = "生物燃料";
        }
        if (powerType == 7) {
            tra_back_energy = "煤油";
        }
        if (powerType == 8) {
            tra_back_energy = "混合燃料";
        }
        if (powerType == 9) {
            tra_back_energy = "其他";
        }
        return tra_back_energy;
    }


    private String saveImage(String urlStr) {
        try {
            if (StrUtil.isNotBlank(urlStr)) {
                String fullUrl = "https://smis.pzhgcrg.com" + urlStr;
                log.info("图片地址：{}", fullUrl);

                // 使用Hutool下载图片字节流
                byte[] bytes = HttpUtil.downloadBytes(fullUrl);

                // 保存图片并返回存储路径
                return devFileApi.storageImageWithReturnUrlLocal(bytes, CommonConstant.CAR_BIZ_IMG_PATH);
            }
        } catch (Exception e) {
            log.error("图片保存失败 - URL: {}", urlStr, e);
        }
        return null;
    }



}
