package com.znhb.biz.modular.tasker;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import com.znhb.biz.modular.cyft.entity.CarRecordReport;
import com.znhb.biz.modular.cyft.service.CarRecordReportService;
import com.znhb.common.timer.CommonTimerTaskRunner;
import com.znhb.dev.api.DevConfigApi;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 运输报表上传超低
 */
@Slf4j
@Component
public class CarRecordReportTasker implements CommonTimerTaskRunner {


    @Resource
    private CarRecordReportService carRecordReportService;


    @Resource
    private DevConfigApi devConfigApi;


    @Override
    public void action() {
        String baseUrl = devConfigApi.getEnvBaseUrl();
        String url = baseUrl + "outDoor";
        log.info("运输报表上传超低url:{}", url);
        List<CarRecordReport> carRecordReports = carRecordReportService.queryListForSync();
        if (ObjectUtil.isNotEmpty(carRecordReports)) {
            for (CarRecordReport row : carRecordReports) {
                Map<String, Object> map = new HashMap<>();
                map.put("accessId", row.getId());
                map.put("inAreaCode", row.getInAreaCode());
                map.put("outAreaCode", row.getOutAreaCode());
                map.put("inGateCode", row.getInGateCode());
                map.put("outGateCode", row.getOutGateCode());
                map.put("inLiftRodType", row.getInLiftRodType());
                map.put("outLiftRodType", row.getOutLiftRodType());
                map.put("inTime", DateUtil.format(row.getInTime(), "yyyy-MM-dd HH:mm:ss"));
                map.put("outTime", DateUtil.format(row.getOutTime(), "yyyy-MM-dd HH:mm:ss"));
                map.put("inCarImgUrl", parseDetailUrl(row.getInCarImgUrl()));
                map.put("outCarImgUrl", parseDetailUrl(row.getOutCarImgUrl()));
                map.put("inLiftRodImgUrl", parseDetailUrl(row.getInLiftRodImgUrl()));
                map.put("outLiftRodImgUrl", parseDetailUrl(row.getOutLiftRodImgUrl()));

                map.put("carNo", row.getCarNumber());
                map.put("teamName", row.getTeamName());
                String in_goods = row.getInGoodsName();
                if (StrUtil.contains(in_goods, ",")) {
                    map.put("inGoodsName", in_goods.split(",")[0]);
                }else{
                    map.put("inGoodsName", in_goods);
                }
                String out_goods = row.getOutGoodsName();
                if (StrUtil.contains(out_goods, ",")) {
                    map.put("outGoodsName", out_goods.split(",")[0]);
                }else{
                    map.put("outGoodsName", out_goods);
                }

                map.put("contractNumber", row.getContractNumber());
                map.put("businessType", row.getBusinessType());
                map.put("unloadingLocation", row.getUnloadingLocation());
                map.put("source", row.getSource());
                map.put("target", row.getTarget());
                map.put("supplier", row.getSupplier());
                map.put("customer", row.getCustomer());
                map.put("receiver", row.getReceiver());
                map.put("receiveTime", DateUtil.format(row.getReceiveTime(), "yyyy-MM-dd HH:mm:ss"));
                map.put("receiveGross", row.getReceiveGross());
                map.put("receiveGrossMeasureScale", row.getReceiveGrossMeasureScale());
                map.put("receiveGrossMeasurePoint", row.getReceiveGrossMeasurePoint());
                map.put("receiveGrossTime", DateUtil.format(row.getReceiveGrossTime(), "yyyy-MM-dd HH:mm:ss"));
                map.put("receiveTare", row.getReceiveTare());
                map.put("receiveTareMeasureScale", row.getReceiveTareMeasureScale());
                map.put("receiveTareMeasurePoint", row.getReceiveTareMeasurePoint());
                map.put("receiveTareTime", DateUtil.format(row.getReceiveTareTime(), "yyyy-MM-dd HH:mm:ss"));
                map.put("receiveSuttle", row.getReceiveSuttle());
                map.put("receiveSuttleMeasureScale", row.getReceiveSuttleMeasureScale());
                map.put("receiveSuttleTime", DateUtil.format(row.getReceiveSuttleTime(), "yyyy-MM-dd HH:mm:ss"));
                map.put("receiveSuttleMeasurePoint", row.getReceiveSuttleMeasurePoint());
                map.put("deliveryGross", row.getDeliveryGross());
                map.put("deliveryGrossMeasureScale", row.getDeliveryGrossMeasureScale());
                map.put("deliveryGrossMeasurePoint", row.getDeliveryGrossMeasurePoint());
                map.put("deliveryGrossTime", DateUtil.format(row.getDeliveryGrossTime(), "yyyy-MM-dd HH:mm:ss"));
                map.put("deliveryTare", row.getDeliveryTare());
                map.put("deliveryTareMeasureScale", row.getDeliveryTareMeasureScale());
                map.put("deliveryTareTime", DateUtil.format(row.getDeliveryTareTime(), "yyyy-MM-dd HH:mm:ss"));
                map.put("deliveryTareMeasurePoint", row.getDeliveryTareMeasurePoint());
                map.put("deliverySuttle", row.getDeliverySuttle());
                map.put("deliverySuttleMeasureScale", row.getDeliverySuttleMeasureScale());
                map.put("deliverySuttleTime", DateUtil.format(row.getDeliverySuttleTime(), "yyyy-MM-dd HH:mm:ss"));
                map.put("deliverySuttleMeasurePoint", row.getDeliverySuttleMeasurePoint());
                map.put("settlementAmount", row.getSettlementAmount());

                //发送 POST 请求
                log.info("运输报表上报环保平台入参:{}", JSONUtil.toJsonStr(map));
                String result = HttpUtil.post(url, JSONUtil.toJsonStr(map));
                log.info("运输报表上报环保平台响应:{}", result);

                JSONObject r = JSONUtil.parseObj(result);
                if (r.get("code").equals(200)) {
                    carRecordReportService.updateSyncStatus(row.getId());
                }

            }
        }
    }


    public static String parseDetailUrl(String path) {
        String prefixUrl = "https://56.cyftcn.cn:449/download/car_detail/";
        if (!StringUtils.isBlank(path)) {
            if (StrUtil.startWithAny(path, "http", "https")) {
                return path;
            }
            return prefixUrl + path;
        }
        return "";
    }
}
