<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.znhb.biz.modular.car.mapper.CarInfoMapper">

    <!-- 获取小车信息分页，并关联规则信息 -->
    <select id="pageWithRule" resultType="com.znhb.biz.modular.car.entity.CarInfo">
        SELECT
            c.*,
            r.rule_name as ruleName
        FROM
            t_car_info c
        LEFT JOIN
            t_car_rule_relation rel ON c.id = rel.car_id AND rel.DELETE_FLAG = 'NOT_DELETE'
        LEFT JOIN
            t_car_access_rule r ON rel.rule_id = r.id AND r.DELETE_FLAG = 'NOT_DELETE'
        ${ew.customSqlSegment}
    </select>

</mapper>
