package com.znhb.biz.modular.ruigang.tasker;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.znhb.biz.api.SuperLowerApi;
import com.znhb.biz.modular.vehicle.entity.Vehicle;
import com.znhb.biz.modular.vehicle.service.VehicleService;
import com.znhb.common.timer.CommonTimerTaskRunner;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * 上报外部车辆备案信息至超低平台
 */
@Slf4j
@Component
public class VehicleTasker implements CommonTimerTaskRunner {

    @Resource
    private VehicleService vehicleService;

    @Resource
    private SuperLowerApi superLowerApi;


    @Override
    public void action() {
        List<Vehicle> vehicleList = vehicleService.queryVehicleForSync();
        for (Vehicle vehicle : vehicleList) {
            // 上报车辆备案信息至超低平台

            Map<String, Object> map = new HashMap<>();
            map.put("carNo", vehicle.getPlateNumber());
            map.put("vin", vehicle.getVin());
            map.put("vehicleType", vehicle.getVehicleType());
            map.put("registerDate", vehicle.getRegisterDate());
            map.put("carBrand", vehicle.getModel());
            map.put("engineType", "");
            map.put("engineBusiness", "");
            map.put("engineNo", vehicle.getEngineNo());
            map.put("carriageUnitName", vehicle.getName());
            map.put("carColor", vehicle.getPlateColor());
            map.put("level", vehicle.getEmissionStage());
            map.put("natureOfUse", vehicle.getUseCharacter());
            map.put("status", Integer.parseInt(vehicle.getNetStatus()));
            map.put("fuelType", vehicle.getEnergyType());
            if (StrUtil.isNotEmpty(vehicle.getDrivingLicenseFrontImgUrl())){
                map.put("travelUrl", vehicle.getDrivingLicenseFrontImgUrl());
            }
            if (StrUtil.isNotEmpty(vehicle.getDrivingLicenseImgUrl())){
                map.put("travelUrl", vehicle.getDrivingLicenseImgUrl());
            }

            map.put("detailedUrl", vehicle.getEnvironmentalListImgUrl());
            map.put("heavyCarResultUrl", vehicle.getHeavyVehicleEmissionStageImgUrl());
            map.put("detailedResultUrl", vehicle.getEnvironmentalListElectronicImgUrl());
            log.info("上报车辆备案信息至超低平台:{}", JSONUtil.toJsonStr(map));
            boolean result = superLowerApi.pushVehicle(map);
            if (result) {
                vehicleService.updateForSync(vehicle.getId());
            }
        }

    }
}
