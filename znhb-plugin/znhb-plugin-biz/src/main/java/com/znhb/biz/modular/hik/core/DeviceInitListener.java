package com.znhb.biz.modular.hik.core;

import cn.hutool.core.util.ClassUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReflectUtil;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;
import com.znhb.biz.modular.barrierGate.entity.BarrierGate;
import com.znhb.biz.modular.barrierGate.service.BarrierGateService;
import com.znhb.biz.modular.device.entity.Device;
import com.znhb.biz.modular.device.service.DeviceService;
import com.znhb.biz.modular.hik.driver.DeviceDriver;
import com.znhb.biz.modular.hik.driver.DriverManager;
import com.znhb.biz.modular.hik.driver.barrierGate.BarrierGateDriver;
import com.znhb.biz.modular.hik.driver.camera.CameraDriver;
import com.znhb.biz.modular.hik.driver.camera.HikGatePlateCameraDriver;
import com.znhb.biz.modular.hik.driver.camera.HikGateTailCameraDriver;
import com.znhb.biz.modular.hik.driver.camera.HikGateBodyCameraDriver;
import com.znhb.biz.modular.hik.driver.led.HikLedDriver;

import javax.annotation.Resource;
import java.util.List;

/**
 * 项目启动 海康设备布防
 */
@Component
@Slf4j
public class DeviceInitListener implements CommandLineRunner {

    @Resource
    private DeviceService deviceService;

    @Resource
    private BarrierGateService barrierGateService;

    @Override
    public void run(String... args) {
        boolean initialized = HikSDKInitializer.isInitialized();
        if (!initialized) {
            log.error("海康SDK未初始化");
            return;
        }
        List<Device> deviceList = deviceService.list();
        for (Device device : deviceList) {
            try {
                //加载驱动
                Class<?> deviceClass = ClassUtil.loadClass(device.getDriverClassPath());
                DeviceDriver deviceDriver = (DeviceDriver) ReflectUtil.newInstance(deviceClass);
                deviceDriver.setDevice(device);
                //驱动缓存
                DriverManager.getInstance().putDriver(deviceDriver);
                if (deviceDriver instanceof CameraDriver) {
                    CameraDriver cameraDriver = (CameraDriver) deviceDriver;
                    //登录
                    boolean login = cameraDriver.login();
                    if (login) {
                        //摄像头布防
                        cameraDriver.defend();
                    }
                }

                log.info("驱动加载成功：{},{}", device.getName(), device.getIp());

            } catch (Exception ex) {
                log.error("驱动加载失败：{},{}", device.getName(), device.getIp(), ex);
            }
        }


        //加载道闸驱动
        List<BarrierGate> barrierGateList = barrierGateService.list();
        for (BarrierGate barrierGate : barrierGateList) {
            BarrierGateDriver gateDriver = new BarrierGateDriver();
            gateDriver.setBarrierGate(barrierGate);
            try {

                if (ObjectUtil.isNotEmpty(DriverManager.getInstance().getDriverById(barrierGate.getPlateCaptureCameraId()))) {
                    //主车牌摄像头驱动
                    HikGatePlateCameraDriver mainPlateCameraDriver = (HikGatePlateCameraDriver) DriverManager.getInstance().getDriverById(barrierGate.getPlateCaptureCameraId());
                    gateDriver.setMainPlateCameraDriver(mainPlateCameraDriver);
                }

                if (ObjectUtil.isNotEmpty(DriverManager.getInstance().getDriverById(barrierGate.getSecondPlateCaptureCameraId()))) {
                    //副车牌摄像头驱动
                    HikGatePlateCameraDriver secondPlateCameraDriver = (HikGatePlateCameraDriver) DriverManager.getInstance().getDriverById(barrierGate.getSecondPlateCaptureCameraId());
                    gateDriver.setSecondPlateCameraDriver(secondPlateCameraDriver);
                }

                if (ObjectUtil.isNotEmpty(DriverManager.getInstance().getDriverById(barrierGate.getBodyCaptureCameraId()))) {
                    //车身摄像头驱动
                    HikGateBodyCameraDriver bodyCameraDriver = (HikGateBodyCameraDriver) DriverManager.getInstance().getDriverById(barrierGate.getBodyCaptureCameraId());
                    gateDriver.setBodyCameraDriver(bodyCameraDriver);
                }

                if (ObjectUtil.isNotEmpty(DriverManager.getInstance().getDriverById(barrierGate.getTailCaptureCameraId()))) {
                    //车尾摄像头驱动
                    HikGateTailCameraDriver tailCameraDriver = (HikGateTailCameraDriver) DriverManager.getInstance().getDriverById(barrierGate.getTailCaptureCameraId());
                    gateDriver.setTailCameraDriver(tailCameraDriver);
                }

                if (ObjectUtil.isNotEmpty(DriverManager.getInstance().getDriverById(barrierGate.getLedId()))){
                    //LED显示屏驱动
                    HikLedDriver ledDriver = (HikLedDriver) DriverManager.getInstance().getDriverById(barrierGate.getLedId());
                    gateDriver.setLedDriver(ledDriver);
                }

                DriverManager.getInstance().putDriver(gateDriver);
                log.info("道闸驱动加载成功：{}", barrierGate.getName());
            } catch (Exception e) {
                log.error("道闸驱动加载失败：{}", barrierGate.getName(), e);
            }
        }


    }
}
