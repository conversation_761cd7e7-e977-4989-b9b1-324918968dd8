/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package com.znhb.biz.modular.device.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.znhb.biz.modular.device.entity.Device;
import com.znhb.biz.modular.device.param.DeviceAddParam;
import com.znhb.biz.modular.device.param.DeviceEditParam;
import com.znhb.biz.modular.device.param.DeviceIdParam;
import com.znhb.biz.modular.device.param.DevicePageParam;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 设备信息Service接口
 *
 * <AUTHOR>
 * @date  2025/03/20 16:10
 **/
public interface DeviceService extends IService<Device> {

    /**
     * 获取设备信息分页
     *
     * <AUTHOR>
     * @date  2025/03/20 16:10
     */
    Page<Device> page(DevicePageParam devicePageParam);

    /**
     * 添加设备信息
     *
     * <AUTHOR>
     * @date  2025/03/20 16:10
     */
    void add(DeviceAddParam deviceAddParam);

    /**
     * 编辑设备信息
     *
     * <AUTHOR>
     * @date  2025/03/20 16:10
     */
    void edit(DeviceEditParam deviceEditParam);

    /**
     * 删除设备信息
     *
     * <AUTHOR>
     * @date  2025/03/20 16:10
     */
    void delete(List<DeviceIdParam> deviceIdParamList);

    /**
     * 获取设备信息详情
     *
     * <AUTHOR>
     * @date  2025/03/20 16:10
     */
    Device detail(DeviceIdParam deviceIdParam);

    /**
     * 获取设备信息详情
     *
     * <AUTHOR>
     * @date  2025/03/20 16:10
     **/
    Device queryEntity(String id);

    /**
     * 设备登录
     *
     * <AUTHOR>
     * @date  2025/03/20 16:10
     */
    boolean login(DeviceIdParam deviceIdParam);

    /**
     * 设备布防
     *
     * <AUTHOR>
     * @date  2025/03/20 16:10
     */
    boolean defend(DeviceIdParam deviceIdParam);

    /**
     * 获取驱动类
     * @return
     */
    List<Map<String, Object>> getDriverClass();

    /**
     * 设备撤防
     * <AUTHOR>
     * @date  2025/03/20 16:10
     */
    boolean closeDefend(DeviceIdParam deviceIdParam);

    /**
     * 获取车牌抓拍机列表
     * @return
     */
    List<Device> getPlateCameraList();

    /**
     * 获取车身抓拍机列表
     * @return
     */
    List<Device> getBodyCameraList();

    /**
     * 获取车尾抓拍机列表
     * @return
     */
    List<Device> getTailCameraList();

    /**
     * 获取led显示屏
     * @return
     */
    List<Device> getLedList();


    /**
     * 获取isc摄像头
     * @return
     */
    List<Device> getIscCameraList();
}
