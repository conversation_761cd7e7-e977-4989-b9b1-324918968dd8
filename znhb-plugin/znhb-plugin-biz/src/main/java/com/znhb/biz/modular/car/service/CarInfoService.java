/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package com.znhb.biz.modular.car.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.znhb.biz.modular.car.entity.CarInfo;
import com.znhb.biz.modular.car.param.CarInfoAddParam;
import com.znhb.biz.modular.car.param.CarInfoEditParam;
import com.znhb.biz.modular.car.param.CarInfoIdParam;
import com.znhb.biz.modular.car.param.CarInfoPageParam;

import javax.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

/**
 * 小车信息Service接口
 *
 * <AUTHOR>
 * @date  2025/04/10 15:00
 **/
public interface CarInfoService extends IService<CarInfo> {

    /**
     * 获取小车信息分页
     *
     * @param param 查询参数
     * @return 分页结果
     */
    Page<CarInfo> page(CarInfoPageParam param);

    /**
     * 添加小车信息
     *
     * @param param 添加参数
     */
    void add(CarInfoAddParam param);

    /**
     * 编辑小车信息
     *
     * @param param 编辑参数
     */
    void edit(CarInfoEditParam param);

    /**
     * 删除小车信息
     *
     * @param param 删除参数
     */
    void delete(List<CarInfoIdParam> param);

    /**
     * 获取小车信息详情
     *
     * @param param 查询参数
     * @return 小车信息详情
     */
    CarInfo detail(CarInfoIdParam param);

    /**
     * 根据车牌号码查询小车信息
     *
     * @param plateNumber 车牌号码
     * @return 小车信息
     */
    CarInfo queryByPlateNumber(String plateNumber);

    /**
     * 下载小车信息导入模板
     *
     * @param response HTTP响应对象
     * @throws IOException IO异常
     */
    void downloadTemplate(HttpServletResponse response) throws IOException;

    /**
     * 导入小车信息
     *
     * @param file 导入文件
     * @throws IOException IO异常
     */
    void importCarInfo(MultipartFile file) throws IOException;

}
