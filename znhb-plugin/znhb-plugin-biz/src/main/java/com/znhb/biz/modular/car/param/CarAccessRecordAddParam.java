/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package com.znhb.biz.modular.car.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import java.util.Date;

/**
 * 小车进厂记录添加参数
 *
 * <AUTHOR>
 * @date  2025/04/10 15:00
 **/
@Getter
@Setter
public class CarAccessRecordAddParam {

    /** id */
    private String id;

    /** 小车ID */
    @ApiModelProperty(value = "小车ID", position = 1)
    private String carId;

    /** 车牌号码 */
    @ApiModelProperty(value = "车牌号码", required = true, position = 2)
    @NotBlank(message = "车牌号码不能为空")
    private String plateNumber;

    /** 进厂时间 */
    @ApiModelProperty(value = "进厂时间", position = 3)
    private Date entryTime;

    /** 进厂道闸 */
    @ApiModelProperty(value = "进厂道闸", position = 4)
    private String entryGate;

    /** 进厂道闸 */
    @ApiModelProperty(value = "进厂道闸id", position = 4)
    private String entryGateId;

    /** 进厂抓拍图片URL */
    @ApiModelProperty(value = "进厂抓拍图片URL", position = 5)
    private String entryImageUrl;

    /** 入厂抬杆类型 */
    private String inOpenGateType;

    /** 入厂数据录入类型 */
    private String inRecordType;

    /** 备注 */
    private String remark;
}
