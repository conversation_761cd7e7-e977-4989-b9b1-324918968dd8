/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package com.znhb.biz.modular.car.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import com.znhb.biz.modular.car.entity.CarRuleRelation;
import com.znhb.biz.modular.car.param.CarRuleRelationAddParam;
import com.znhb.biz.modular.car.param.CarRuleRelationIdParam;
import com.znhb.biz.modular.car.service.CarRuleRelationService;
import com.znhb.common.annotation.CommonLog;
import com.znhb.common.pojo.CommonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 小车规则关联控制器
 *
 * <AUTHOR>
 * @date  2025/04/10 15:00
 **/
@Api(tags = "小车规则关联控制器")
@ApiSupport(author = "ZNHB_TEAM", order = 1)
@RestController
@Validated
public class CarRuleRelationController {

    @Resource
    private CarRuleRelationService carRuleRelationService;

    /**
     * 获取小车规则关联列表
     *
     * @param carId 小车ID
     * @return 查询结果
     */
    @ApiOperationSupport(order = 1)
    @ApiOperation("获取小车规则关联列表")
    @SaCheckPermission("/biz/car/ruleRelation/list")
    @GetMapping("/biz/car/ruleRelation/list")
    public CommonResult<List<CarRuleRelation>> list(@RequestParam(required = true) @NotBlank(message = "小车ID不能为空") String carId) {
        return CommonResult.data(carRuleRelationService.list(carId));
    }

    /**
     * 添加小车规则关联
     *
     * @param carRuleRelationAddParam 添加参数
     * @return 添加结果
     */
    @ApiOperationSupport(order = 2)
    @ApiOperation("添加小车规则关联")
    @CommonLog("添加小车规则关联")
    @SaCheckPermission("/biz/car/ruleRelation/add")
    @PostMapping("/biz/car/ruleRelation/add")
    public CommonResult<String> add(@RequestBody @Valid CarRuleRelationAddParam carRuleRelationAddParam) {
        carRuleRelationService.add(carRuleRelationAddParam);
        return CommonResult.ok();
    }

    /**
     * 批量添加小车规则关联
     *
     * @param carRuleRelationAddParamList 添加参数列表
     * @return 添加结果
     */
    @ApiOperationSupport(order = 3)
    @ApiOperation("批量添加小车规则关联")
    @CommonLog("批量添加小车规则关联")
    @SaCheckPermission("/biz/car/ruleRelation/batchAdd")
    @PostMapping("/biz/car/ruleRelation/batchAdd")
    public CommonResult<String> batchAdd(@RequestBody @Valid List<CarRuleRelationAddParam> carRuleRelationAddParamList) {
        carRuleRelationService.batchAdd(carRuleRelationAddParamList);
        return CommonResult.ok();
    }

    /**
     * 删除小车规则关联
     *
     * @param carRuleRelationIdParam 删除参数
     * @return 删除结果
     */
    @ApiOperationSupport(order = 4)
    @ApiOperation("删除小车规则关联")
    @CommonLog("删除小车规则关联")
    @SaCheckPermission("/biz/car/ruleRelation/delete")
    @PostMapping("/biz/car/ruleRelation/delete")
    public CommonResult<String> delete(@RequestBody @Valid CarRuleRelationIdParam carRuleRelationIdParam) {
        carRuleRelationService.delete(carRuleRelationIdParam);
        return CommonResult.ok();
    }
}
