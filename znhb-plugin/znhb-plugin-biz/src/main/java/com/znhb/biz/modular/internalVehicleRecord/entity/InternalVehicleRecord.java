/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package com.znhb.biz.modular.internalVehicleRecord.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.znhb.common.pojo.CommonEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 厂内运输台账实体
 *
 * <AUTHOR>
 * @date  2025/04/03 11:37
 **/
@Getter
@Setter
@TableName("t_internal_vehicle_record")
public class InternalVehicleRecord extends CommonEntity {

    /** 主键ID */
    @TableId
    @ApiModelProperty(value = "主键ID", position = 1)
    private String id;

    /** 内部车辆备案id */
    @ApiModelProperty(value = "内部车辆备案id", position = 2)
    private String vehicleId;

    /** 环保登记编码 */
    @ApiModelProperty(value = "环保登记编码", position = 3)
    private String envRegCode;

    /** 车辆识别代码(VIN) */
    @ApiModelProperty(value = "车辆识别代码(VIN)", position = 4)
    private String vinCode;

    /** 生产日期 */
    @ApiModelProperty(value = "生产日期", position = 5)
    private String productionDate;

    /** 车牌号码 */
    @ApiModelProperty(value = "车牌号码", position = 6)
    private String plateNumber;

    /** 注册登记日期 */
    @ApiModelProperty(value = "注册登记日期", position = 7)
    private String registrationDate;

    /** 车辆品牌型号 */
    @ApiModelProperty(value = "车辆品牌型号", position = 8)
    private String vehicleModel;

    /** 燃料类型 */
    @ApiModelProperty(value = "燃料类型", position = 9)
    private String fuelType;

    /** 排放标准 */
    @ApiModelProperty(value = "排放标准", position = 10)
    private String emissionStandard;

    /** 联网状态 */
    @ApiModelProperty(value = "联网状态", position = 11)
    private String networkStatus;

    /** 随车清单 */
    @ApiModelProperty(value = "随车清单", position = 12)
    private String vehicleInventory;

    /** 行驶证 */
    @ApiModelProperty(value = "行驶证", position = 13)
    private String drivingLicense;

    /** 车辆所有人(单位) */
    @ApiModelProperty(value = "车辆所有人(单位)", position = 14)
    private String ownerInfo;

    /** 进厂日期 */
    @ApiModelProperty(value = "进厂日期", position = 15)
    private Date inCreateTime;

    /** 出厂日期 */
    @ApiModelProperty(value = "出厂日期", position = 16)
    private Date outCreateTime;

    /** 同步状态 */
    @ApiModelProperty(value = "同步状态", position = 17)
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String syncStatus;


    /** 进厂抓拍 */
    @ApiModelProperty(value = "进厂抓拍", position = 22)
    private String inCarImageUrl;

    /** 出厂抓拍 */
    @ApiModelProperty(value = "出厂抓拍", position = 23)
    private String outCarImageUrl;

    /** 出厂状态 */
    @ApiModelProperty(value = "出厂状态", position = 24)
    private String outStatus;

    /** 电子环保清单照片 */
    @ApiModelProperty(value = "电子环保清单照片", position = 25)
    private String environmentalListElectronicImgUrl;

    /**  电子重型车排放阶段照片 */
    @ApiModelProperty(value = "电子重型车排放阶段照片", position = 26)
    private String heavyVehicleEmissionStageImgUrl;

    /** 备注信息 */
    private String remark;

}
