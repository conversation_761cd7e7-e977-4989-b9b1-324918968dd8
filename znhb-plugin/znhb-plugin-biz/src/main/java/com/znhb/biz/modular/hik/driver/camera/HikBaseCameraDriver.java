package com.znhb.biz.modular.hik.driver.camera;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.sun.jna.ptr.IntByReference;
import com.znhb.common.exception.CommonException;
import com.znhb.dev.api.DevConfigApi;
import lombok.extern.slf4j.Slf4j;
import com.znhb.biz.core.enums.DefendStatusEnum;
import com.znhb.biz.modular.hik.constant.CommonConstant;
import com.znhb.biz.modular.hik.core.HCNetSDK;
import com.znhb.biz.modular.hik.core.HikSDKInitializer;
import com.znhb.dev.api.DevFileApi;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.nio.ByteBuffer;

/**
 * 海康基础摄像头驱动
 */
@Slf4j
public abstract class HikBaseCameraDriver extends CameraDriver {

    public Integer userId;

    public Integer alarmId;

    public Integer previewId;

    private final HCNetSDK.NET_DVR_JPEGPARA jpeg = new HCNetSDK.NET_DVR_JPEGPARA();

    private final HCNetSDK.NET_DVR_SNAPCFG snapConfig = new HCNetSDK.NET_DVR_SNAPCFG();


    @Override
    public String getDefendStatus() {
        if (this.userId == null) {
            return DefendStatusEnum.NOT_LOGIN.getCode();
        }

        if (this.userId == -1) {
            return DefendStatusEnum.LOGIN_FAIL.getCode();
        }

        if (this.defendJudge) {
            //判断是否需要布防
            if (this.alarmId == null) {
                //未布防
                return DefendStatusEnum.NOT_DEFEND.getCode();
            } else {
                if (this.alarmId > -1) {
                    //布防成功
                    return DefendStatusEnum.DEFEND_SUCCESS.getCode();
                } else {
                    //布防失败
                    return DefendStatusEnum.DEFEND_FAIL.getCode();
                }

            }
        } else {
            return DefendStatusEnum.NOT_NEED_DEFEND.getCode();
        }
    }

    /**
     * 登录设备，支持 V40 和 V30 版本，功能一致。
     */
    @Override
    public boolean login() {
        //检查是否登录过
        if (userId != null) {
            log.info(">>> {}:设备已登录", this.getDeviceName());
            return true;
        }

        // 创建设备登录信息和设备信息对象
        HCNetSDK.NET_DVR_USER_LOGIN_INFO loginInfo = new HCNetSDK.NET_DVR_USER_LOGIN_INFO();
        HCNetSDK.NET_DVR_DEVICEINFO_V40 deviceInfo = new HCNetSDK.NET_DVR_DEVICEINFO_V40();

        // 设置设备IP地址
        byte[] deviceAddress = new byte[HCNetSDK.NET_DVR_DEV_ADDRESS_MAX_LEN];
        byte[] ipBytes = this.getDevice().getIp().getBytes();
        System.arraycopy(ipBytes, 0, deviceAddress, 0, Math.min(ipBytes.length, deviceAddress.length));
        loginInfo.sDeviceAddress = deviceAddress;

        // 设置用户名和密码
        byte[] userName = this.getDevice().getUsername().getBytes();
        byte[] password = this.getDevice().getPassword().getBytes();
        System.arraycopy(userName, 0, loginInfo.sUserName, 0, Math.min(userName.length, loginInfo.sUserName.length));
        System.arraycopy(password, 0, loginInfo.sPassword, 0, Math.min(password.length, loginInfo.sPassword.length));

        // 设置端口和登录模式
        loginInfo.wPort = Short.parseShort(this.getDevice().getPort());
        loginInfo.bUseAsynLogin = false; // 同步登录
        loginInfo.byLoginMode = 0; // 使用SDK私有协议

        // 执行登录操作
        userId = HikSDKInitializer.getInstance().NET_DVR_Login_V40(loginInfo, deviceInfo);
        if (userId == -1) {
            //log.info(">>> {}登录失败，错误码为: {}", this.getDeviceName(), HikSDKInitializer.getInstance().NET_DVR_GetLastError());
            throw new CommonException("{}登录失败，错误码为: {}", this.getDeviceName(), HikSDKInitializer.getInstance().NET_DVR_GetLastError());
        } else {
            log.info(">>> {},{}设备登录成功！", this.getDeviceName(), this.getDevice().getIp());
            // 处理通道号逻辑
            int startDChan = deviceInfo.struDeviceV30.byStartDChan;
            log.info(">>> 预览起始通道号: {}", startDChan);
        }
        return userId > -1;
    }

    /**
     * 登出设备
     */
    @Override
    public boolean logout() {
        return HikSDKInitializer.getInstance().NET_DVR_Logout_V30(userId);
    }


    /**
     * 报警布防
     */
    @Override
    public boolean defend() {
        //是否需要布防
        if (defendJudge) {
            if (userId == null) {
                log.info(">>> {}:设备未登录", this.getDeviceName());
                return false;
            }
            if (alarmId == null) {
                //报警布防参数设置
                HCNetSDK.NET_DVR_SETUPALARM_PARAM alarmInfo = new HCNetSDK.NET_DVR_SETUPALARM_PARAM();
                alarmInfo.dwSize = alarmInfo.size();
                alarmInfo.byLevel = 1;  //布防等级 0- 一级 1- 二级 2- 三级
                alarmInfo.byAlarmInfoType = 1;   // 智能交通报警信息上传类型：0- 老报警信息（NET_DVR_PLATE_RESULT），1- 新报警信息(NET_ITS_PLATE_RESULT)
                alarmInfo.byDeployType = 1;   //布防类型：0-客户端布防，1-实时布防，客户端布防仅支持一路
                alarmInfo.write();
                alarmId = HikSDKInitializer.getInstance().NET_DVR_SetupAlarmChan_V41(userId, alarmInfo);
                if (alarmId == -1) {
                    log.info(">>> 布防失败:{}-{}，错误码为{}", this.getDeviceName(), this.getDeviceIp(), HikSDKInitializer.getInstance().NET_DVR_GetLastError());
                } else {
                    log.info(">>> 布防成功:{}-{}", this.getDeviceName(), this.getDeviceIp());

                }
            } else {
                log.info(">>> 设备{}-{}已经布防，请先撤防！", this.getDeviceName(), this.getDeviceIp());
            }
        } else {
            log.info("{}:无需布防", this.getDeviceName());
            return false;
        }

        return alarmId > -1;
    }

    /**
     * 报警撤防
     */
    @Override
    public boolean closeDefend() {
        return HikSDKInitializer.getInstance().NET_DVR_CloseAlarmChan_V30(alarmId);
    }

    @Override
    public boolean onlineInspect() {
        return HikSDKInitializer.getInstance().NET_DVR_RemoteControl(userId, HCNetSDK.NET_DVR_CHECK_USER_STATUS, null, 0);
    }

    @Override
    public boolean previewVideo() {
        try {

            HCNetSDK.NET_DVR_PREVIEWINFO viewInfo = new HCNetSDK.NET_DVR_PREVIEWINFO();
            viewInfo.lChannel = 1;
            viewInfo.hPlayWnd = null;
            viewInfo.dwStreamType = 0;
            viewInfo.byPreviewMode = 0;
            viewInfo.dwLinkMode = 4;

            previewId = HikSDKInitializer.getInstance().NET_DVR_RealPlay_V40(this.userId, viewInfo, null, null);
            return true;
        } catch (Exception e) {
            log.info(">>> 预览失败，错误码为:{}", HikSDKInitializer.getInstance().NET_DVR_GetLastError());
            return false;
        }
    }

    public String capture() {
        return capture(null);
    }

    /**
     * 主抓拍图片返回图片地址（不触发告警回调函数）  图片数据先存在内存 再保存文件
     */
    public String capture(String plateNumber) {
        DevConfigApi configApi = SpringUtil.getBean(DevConfigApi.class);

        jpeg.wPicSize = 2;
        jpeg.wPicQuality = 2;
        IntByReference jpegSize = new IntByReference();
        int bufferSize = 1024 * 1024 * 2; // 初始缓冲区大小为 2MB
        ByteBuffer jpegBuffer = ByteBuffer.allocate(bufferSize);
        //保存在内存中
        boolean captureResult = HikSDKInitializer.getInstance().NET_DVR_CaptureJPEGPicture_NEW(userId, 1, jpeg, jpegBuffer, bufferSize, jpegSize);
        String imgUrl = null;
        if (captureResult) {
            byte[] bytes = jpegBuffer.array();
            if (!StrUtil.isEmpty(plateNumber)) {
                try {
                    //车牌号：{} 抓拍时间：{}
                    String OSD = configApi.getOsdInfoConfig();
                    OSD = StrUtil.format(OSD, plateNumber, DateUtil.now());
                    bytes = addTextToImage(bytes, OSD);
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }
            }
            // 存储到本地
            try {
                DevFileApi devFileApi = SpringUtil.getBean(DevFileApi.class);
                imgUrl = devFileApi.storageImageWithReturnUrlLocal(bytes, CommonConstant.CAPTURE_IMG_BUCKET);

                log.info(">>> 抓拍成功：{}-{}", this.getDeviceName(), imgUrl);
            } catch (Exception e) {
                log.info(">>> 保存图片异常：{}", this.getDeviceName());
            }
        } else {
            log.info(">>> 抓拍失败，错误码为:{}", HikSDKInitializer.getInstance().NET_DVR_GetLastError());
        }
        return captureResult ? imgUrl : null;
    }


    /**
     * 主动抓拍触发回调函数
     */
    public void captureCallback() {
        snapConfig.dwSize = snapConfig.size();
        snapConfig.bySnapTimes = 1;
        snapConfig.wSnapWaitTime = 100;
        snapConfig.write();
        if (!HikSDKInitializer.getInstance().NET_DVR_ContinuousShoot(userId, snapConfig)) {
            log.info("主动抓拍回调失败{}", HikSDKInitializer.getInstance().NET_DVR_GetLastError());
        }
        log.info("主动抓拍回调成功");

    }

    private static byte[] addTextToImage(byte[] bytes, String text) throws IOException {
        BufferedImage image = ImageIO.read(new ByteArrayInputStream(bytes));
        Graphics2D graphics = image.createGraphics();
        try {
            // 设置通用绘制参数
            graphics.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);

            // 字体配置
            Font font = new Font("微软雅黑", Font.PLAIN, 40);
            graphics.setFont(font);
            FontMetrics metrics = graphics.getFontMetrics();

            // 计算绘制位置
            final int TEXT_X_MARGIN = 50;
            final int TEXT_Y_OFFSET = 100;
            int yPos = image.getHeight() - metrics.getHeight() - TEXT_Y_OFFSET + metrics.getAscent();

            // 绘制阴影（使用ARGB十六进制颜色更直观）
            graphics.setColor(new Color(0x26000000, true));
            graphics.drawString(text, TEXT_X_MARGIN + 2, yPos + 2);

            // 绘制主文本
            graphics.setColor(Color.WHITE);
            graphics.drawString(text, TEXT_X_MARGIN, yPos);
        } finally {
            graphics.dispose();
        }

        // 输出图片字节
        try (ByteArrayOutputStream bs = new ByteArrayOutputStream()) {
            ImageIO.write(image, "jpg", bs);
            return bs.toByteArray();
        }
    }
}
