package com.znhb.biz.modular.hik.driver.camera;

import lombok.extern.slf4j.Slf4j;
import com.znhb.biz.core.enums.DeviceTypeEnum;

/**
 * 海康门禁车身抓拍机
 */
@Slf4j
public class HikGateBodyCameraDriver extends HikBaseCameraDriver{

    private final static String NAME = "海康车身抓拍机驱动";

    public HikGateBodyCameraDriver() {
        super();
        defendJudge = true;
    }

    @Override
    public String type() {
        return DeviceTypeEnum.HIK_BODY_CAMERA.getCode();
    }
}
