/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package com.znhb.biz.modular.internalVehicleRecord.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.znhb.biz.core.enums.OutStatusEnum;
import com.znhb.biz.core.enums.SyncStatusEnum;
import com.znhb.biz.modular.internalVehicle.entity.InternalVehicle;
import com.znhb.biz.modular.vehicleRecord.entity.CarRecord;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.znhb.common.enums.CommonSortOrderEnum;
import com.znhb.common.exception.CommonException;
import com.znhb.common.page.CommonPageRequest;
import com.znhb.biz.modular.internalVehicleRecord.entity.InternalVehicleRecord;
import com.znhb.biz.modular.internalVehicleRecord.mapper.InternalVehicleRecordMapper;
import com.znhb.biz.modular.internalVehicleRecord.param.InternalVehicleRecordAddParam;
import com.znhb.biz.modular.internalVehicleRecord.param.InternalVehicleRecordEditParam;
import com.znhb.biz.modular.internalVehicleRecord.param.InternalVehicleRecordIdParam;
import com.znhb.biz.modular.internalVehicleRecord.param.InternalVehicleRecordPageParam;
import com.znhb.biz.modular.internalVehicleRecord.service.InternalVehicleRecordService;

import java.util.Collections;
import java.util.List;

/**
 * 厂内运输台账Service接口实现类
 *
 * <AUTHOR>
 * @date  2025/04/03 11:37
 **/
@Service
public class InternalVehicleRecordServiceImpl extends ServiceImpl<InternalVehicleRecordMapper, InternalVehicleRecord> implements InternalVehicleRecordService {

    @Override
    public Page<InternalVehicleRecord> page(InternalVehicleRecordPageParam internalVehicleRecordPageParam) {
        QueryWrapper<InternalVehicleRecord> queryWrapper = new QueryWrapper<>();
        if(ObjectUtil.isNotEmpty(internalVehicleRecordPageParam.getEnvRegCode())) {
            queryWrapper.lambda().like(InternalVehicleRecord::getEnvRegCode, internalVehicleRecordPageParam.getEnvRegCode());
        }
        if(ObjectUtil.isNotEmpty(internalVehicleRecordPageParam.getVinCode())) {
            queryWrapper.lambda().like(InternalVehicleRecord::getVinCode, internalVehicleRecordPageParam.getVinCode());
        }
        if(ObjectUtil.isNotEmpty(internalVehicleRecordPageParam.getPlateNumber())) {
            queryWrapper.lambda().like(InternalVehicleRecord::getPlateNumber, internalVehicleRecordPageParam.getPlateNumber());
        }
        if(ObjectUtil.isNotEmpty(internalVehicleRecordPageParam.getFuelType())) {
            queryWrapper.lambda().eq(InternalVehicleRecord::getFuelType, internalVehicleRecordPageParam.getFuelType());
        }
        if(ObjectUtil.isNotEmpty(internalVehicleRecordPageParam.getEmissionStandard())) {
            queryWrapper.lambda().eq(InternalVehicleRecord::getEmissionStandard, internalVehicleRecordPageParam.getEmissionStandard());
        }
        if(ObjectUtil.isAllNotEmpty(internalVehicleRecordPageParam.getSortField(), internalVehicleRecordPageParam.getSortOrder())) {
            CommonSortOrderEnum.validate(internalVehicleRecordPageParam.getSortOrder());
            queryWrapper.orderBy(true, internalVehicleRecordPageParam.getSortOrder().equals(CommonSortOrderEnum.ASC.getValue()),
                    StrUtil.toUnderlineCase(internalVehicleRecordPageParam.getSortField()));
        } else {
            queryWrapper.lambda().orderByAsc(InternalVehicleRecord::getId);
        }
        return this.page(CommonPageRequest.defaultPage(), queryWrapper);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void add(InternalVehicleRecordAddParam internalVehicleRecordAddParam) {
        InternalVehicleRecord internalVehicleRecord = BeanUtil.toBean(internalVehicleRecordAddParam, InternalVehicleRecord.class);
        internalVehicleRecord.setOutStatus(OutStatusEnum.NOT_OUT.getCode());
        this.save(internalVehicleRecord);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(InternalVehicleRecordEditParam internalVehicleRecordEditParam) {
        InternalVehicleRecord internalVehicleRecord = this.queryEntity(internalVehicleRecordEditParam.getId());
        BeanUtil.copyProperties(internalVehicleRecordEditParam, internalVehicleRecord);
        this.updateById(internalVehicleRecord);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<InternalVehicleRecordIdParam> internalVehicleRecordIdParamList) {
        // 执行删除
        this.removeByIds(CollStreamUtil.toList(internalVehicleRecordIdParamList, InternalVehicleRecordIdParam::getId));
    }

    @Override
    public InternalVehicleRecord detail(InternalVehicleRecordIdParam internalVehicleRecordIdParam) {
        return this.queryEntity(internalVehicleRecordIdParam.getId());
    }

    @Override
    public InternalVehicleRecord queryEntity(String id) {
        InternalVehicleRecord internalVehicleRecord = this.getById(id);
        if(ObjectUtil.isEmpty(internalVehicleRecord)) {
            throw new CommonException("厂内运输台账不存在，id值为：{}", id);
        }
        return internalVehicleRecord;
    }

    @Override
    public InternalVehicleRecord queryLastInRecord(String plateNumber) {
        QueryWrapper<InternalVehicleRecord> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(InternalVehicleRecord::getPlateNumber, plateNumber);
        queryWrapper.lambda().eq(InternalVehicleRecord::getOutStatus, OutStatusEnum.NOT_OUT.getCode());
        queryWrapper.lambda().orderByDesc(InternalVehicleRecord::getInCreateTime);
        queryWrapper.last("LIMIT 1");
        return this.getOne(queryWrapper);
    }

    @Override
    public void updateForOut(InternalVehicleRecord internalVehicleRecord) {
        UpdateWrapper<InternalVehicleRecord> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda().eq(InternalVehicleRecord::getId, internalVehicleRecord.getId());
        updateWrapper.lambda().set(InternalVehicleRecord::getOutStatus, OutStatusEnum.OUTED.getCode());
        updateWrapper.lambda().set(InternalVehicleRecord::getOutCreateTime, DateUtil.parseDateTime(DateUtil.now()));
        updateWrapper.lambda().set(InternalVehicleRecord::getOutCarImageUrl, internalVehicleRecord.getOutCarImageUrl());
        this.update(updateWrapper);
    }

    @Override
    public List<InternalVehicleRecord> queryInternalVehicleAccessRecord() {
        QueryWrapper<InternalVehicleRecord> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(InternalVehicleRecord::getOutStatus, OutStatusEnum.OUTED.getCode());
        queryWrapper.lambda().eq(InternalVehicleRecord::getSyncStatus, SyncStatusEnum.NOT_SYNC.getCode());
        queryWrapper.lambda().between(InternalVehicleRecord::getUpdateTime, DateUtil.beginOfDay(DateUtil.parseDateTime(DateUtil.now())), DateUtil.endOfDay(DateUtil.parseDateTime(DateUtil.now())));
        return this.list(queryWrapper);
    }

    @Override
    public void updateForSync(String id) {
        UpdateWrapper<InternalVehicleRecord> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda().eq(InternalVehicleRecord::getId, id);
        updateWrapper.lambda().set(InternalVehicleRecord::getSyncStatus, SyncStatusEnum.SYNCED.getCode());
        this.update(updateWrapper);
    }

    @Override
    public void updateInternalVehicleRecord(InternalVehicle internalVehicle) {
        UpdateWrapper<InternalVehicleRecord> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda().eq(InternalVehicleRecord::getVehicleId, internalVehicle.getId());
        updateWrapper.lambda().set(InternalVehicleRecord::getEnvRegCode, internalVehicle.getEnvRegCode());
        updateWrapper.lambda().set(InternalVehicleRecord::getVinCode, internalVehicle.getVin());
        updateWrapper.lambda().set(InternalVehicleRecord::getPlateNumber, internalVehicle.getPlateNumber());
        updateWrapper.lambda().set(InternalVehicleRecord::getProductionDate, internalVehicle.getProductionDate());
        updateWrapper.lambda().set(InternalVehicleRecord::getRegistrationDate, internalVehicle.getRegistrationDate());
        updateWrapper.lambda().set(InternalVehicleRecord::getVehicleModel, internalVehicle.getVehicleModel());
        updateWrapper.lambda().set(InternalVehicleRecord::getFuelType, internalVehicle.getFuelType());
        updateWrapper.lambda().set(InternalVehicleRecord::getEmissionStandard, internalVehicle.getEmissionStandard());
        updateWrapper.lambda().set(InternalVehicleRecord::getNetworkStatus, internalVehicle.getNetworkStatus());
        updateWrapper.lambda().set(InternalVehicleRecord::getVehicleInventory, internalVehicle.getVehicleListImage());
        updateWrapper.lambda().set(InternalVehicleRecord::getDrivingLicense, internalVehicle.getDrivingLicenseImage());
        updateWrapper.lambda().set(InternalVehicleRecord::getOwnerInfo, internalVehicle.getOwnerInfo());
        updateWrapper.lambda().set(InternalVehicleRecord::getEnvironmentalListElectronicImgUrl, internalVehicle.getEnvironmentalListElectronicImgUrl());
        updateWrapper.lambda().set(InternalVehicleRecord::getHeavyVehicleEmissionStageImgUrl, internalVehicle.getHeavyVehicleEmissionStageImgUrl());
        this.update(updateWrapper);
    }

    @Override
    public void confirmExit(InternalVehicleRecordIdParam internalVehicleRecordIdParam) {
        UpdateWrapper<InternalVehicleRecord> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda().eq(InternalVehicleRecord::getId, internalVehicleRecordIdParam.getId());
        updateWrapper.lambda().set(InternalVehicleRecord::getOutStatus, OutStatusEnum.OUTED.getCode());
        this.update(updateWrapper);
    }
}
