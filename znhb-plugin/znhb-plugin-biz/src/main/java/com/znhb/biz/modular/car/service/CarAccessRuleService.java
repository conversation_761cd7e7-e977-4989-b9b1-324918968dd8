/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package com.znhb.biz.modular.car.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.znhb.biz.modular.car.entity.CarAccessRule;
import com.znhb.biz.modular.car.param.CarAccessRuleAddParam;
import com.znhb.biz.modular.car.param.CarAccessRuleEditParam;
import com.znhb.biz.modular.car.param.CarAccessRuleIdParam;
import com.znhb.biz.modular.car.param.CarAccessRulePageParam;

import java.util.List;

/**
 * 小车进出规则Service接口
 *
 * <AUTHOR>
 * @date  2025/04/10 15:00
 **/
public interface CarAccessRuleService extends IService<CarAccessRule> {

    /**
     * 获取小车进出规则分页
     *
     * @param carAccessRulePageParam 查询参数
     * @return 查询结果
     */
    Page<CarAccessRule> page(CarAccessRulePageParam carAccessRulePageParam);

    /**
     * 添加小车进出规则
     *
     * @param carAccessRuleAddParam 添加参数
     */
    void add(CarAccessRuleAddParam carAccessRuleAddParam);

    /**
     * 编辑小车进出规则
     *
     * @param carAccessRuleEditParam 编辑参数
     */
    void edit(CarAccessRuleEditParam carAccessRuleEditParam);

    /**
     * 删除小车进出规则
     *
     * @param carAccessRuleIdParam 删除参数
     */
    void delete(CarAccessRuleIdParam carAccessRuleIdParam);

    /**
     * 获取小车进出规则详情
     *
     * @param carAccessRuleIdParam 查询参数
     * @return 查询结果
     */
    CarAccessRule detail(CarAccessRuleIdParam carAccessRuleIdParam);

    /**
     * 获取小车进出规则列表
     *
     * @return 查询结果
     */
    List<CarAccessRule> list();
}
