package com.znhb.biz.modular.hik.core;

public class MathUtils {

	final protected static char[] hexArray = "0123456789ABCDEF".toCharArray();

	public static String bytesToHex(byte[] bytes) {
		char[] hexChars = new char[bytes.length * 2];
		for (int j = 0; j < bytes.length; j++) {
			int v = bytes[j] & 0xFF;
			hexChars[j * 2] = hexArray[v >>> 4];
			hexChars[j * 2 + 1] = hexArray[v & 0x0F];
		}
		return new String(hexChars).toLowerCase();
	}

	public static int writeByteBuffer(byte[] buffer, byte[] source, int offset) {
		for (byte b : source) {
			buffer[offset++] = b;
		}
		return offset;
	}

	public static int writeByteBufferMath(byte[] buffer, byte[] source, int offset) {
		for (int i = source.length; i >= 1; i--) {
			buffer[offset++] = source[i - 1];
		}
		return offset;
	}

	/**
	 * int到byte[] 由高位到低位
	 *
	 * @param i
	 *            需要转换为byte数组的整行值。
	 * @return byte数组
	 */
	public static byte[] int2byte(int i) {
		byte[] result = new byte[4];
		result[0] = (byte) ((i >> 24) & 0xFF);
		result[1] = (byte) ((i >> 16) & 0xFF);
		result[2] = (byte) ((i >> 8) & 0xFF);
		result[3] = (byte) (i & 0xFF);
		return result;

	}

	public static byte[] short2byte(short s) {
		byte[] b = new byte[2];
		for (int i = 0; i < 2; i++) {
			int offset = 16 - (i + 1) * 8; // 因为byte占4个字节，所以要计算偏移量
			b[i] = (byte) ((s >> offset) & 0xff); // 把16位分为2个8位进行分别存储
		}
		return b;
	}

	public static int byte2int(byte[] b) {
		int l = 0;
		for (int i = 0; i < 4; i++) {
			l <<= 8; // <<=和我们的 +=是一样的，意思就是 l = l << 8
			l |= (b[i] & 0xff); // 和上面也是一样的 l = l | (b[i]&0xff)
		}
		return l;
	}

}
