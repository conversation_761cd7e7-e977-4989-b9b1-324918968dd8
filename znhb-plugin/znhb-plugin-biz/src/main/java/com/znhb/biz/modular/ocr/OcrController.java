package com.znhb.biz.modular.ocr;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.znhb.biz.modular.ocr.param.OcrParam;
import com.znhb.biz.modular.ocr.service.OcrService;

import com.znhb.common.pojo.CommonResult;

import javax.annotation.Resource;

/**
 * ocr控制器
 */
@Api(tags = "ocr控制器")
@ApiSupport(author = "杜方炎", order = 1)
@RestController
@Validated
@ConditionalOnProperty(name = "ocr.hwy.enabled", havingValue = "true")
public class OcrController {

    @Resource
    private OcrService ocrService;

    @ApiOperationSupport(order = 1)
    @ApiOperation("华为云OCR_识别(驾驶证单页识别)")
    @PostMapping("/hwy/ocr/singleLicense")
    public CommonResult singleLicense(@RequestBody OcrParam param) {
        return CommonResult.data(ocrService.vehicleLicenseSingleSide(param));
    }

    @ApiOperationSupport(order = 1)
    @ApiOperation("华为云OCR_识别(驾驶证双页识别)")
    @PostMapping("/hwy/ocr/doubleLicense")
    public CommonResult doubleLicense(@RequestBody OcrParam param) {
        return CommonResult.data(ocrService.vehicleLicenseDoubleSide(param));
    }

}
