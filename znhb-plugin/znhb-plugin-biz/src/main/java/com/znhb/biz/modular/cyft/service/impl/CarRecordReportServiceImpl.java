/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package com.znhb.biz.modular.cyft.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.znhb.common.enums.CommonSortOrderEnum;
import com.znhb.common.exception.CommonException;
import com.znhb.common.page.CommonPageRequest;
import com.znhb.biz.modular.cyft.entity.CarRecordReport;
import com.znhb.biz.modular.cyft.mapper.CarRecordReportMapper;
import com.znhb.biz.modular.cyft.param.CarRecordReportAddParam;
import com.znhb.biz.modular.cyft.param.CarRecordReportEditParam;
import com.znhb.biz.modular.cyft.param.CarRecordReportIdParam;
import com.znhb.biz.modular.cyft.param.CarRecordReportPageParam;
import com.znhb.biz.modular.cyft.service.CarRecordReportService;

import java.util.List;

/**
 * CarRecordReportService接口实现类
 *
 * <AUTHOR>
 * @date  2025/03/27 08:50
 **/
@Service
@DS("slave")
public class CarRecordReportServiceImpl extends ServiceImpl<CarRecordReportMapper, CarRecordReport> implements CarRecordReportService {

    @Override
    public Page<CarRecordReport> page(CarRecordReportPageParam carRecordReportPageParam) {
        QueryWrapper<CarRecordReport> queryWrapper = new QueryWrapper<>();
        if(ObjectUtil.isNotEmpty(carRecordReportPageParam.getCarNumber())) {
            queryWrapper.lambda().like(CarRecordReport::getCarNumber, carRecordReportPageParam.getCarNumber());
        }
        if(ObjectUtil.isAllNotEmpty(carRecordReportPageParam.getSortField(), carRecordReportPageParam.getSortOrder())) {
            CommonSortOrderEnum.validate(carRecordReportPageParam.getSortOrder());
            queryWrapper.orderBy(true, carRecordReportPageParam.getSortOrder().equals(CommonSortOrderEnum.ASC.getValue()),
                    StrUtil.toUnderlineCase(carRecordReportPageParam.getSortField()));
        } else {
            queryWrapper.lambda().orderByAsc(CarRecordReport::getId);
        }
        return this.page(CommonPageRequest.defaultPage(), queryWrapper);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void add(CarRecordReportAddParam carRecordReportAddParam) {
        CarRecordReport carRecordReport = BeanUtil.toBean(carRecordReportAddParam, CarRecordReport.class);
        this.save(carRecordReport);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(CarRecordReportEditParam carRecordReportEditParam) {
        CarRecordReport carRecordReport = this.queryEntity(carRecordReportEditParam.getId());
        BeanUtil.copyProperties(carRecordReportEditParam, carRecordReport);
        this.updateById(carRecordReport);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<CarRecordReportIdParam> carRecordReportIdParamList) {
        // 执行删除
        this.removeByIds(CollStreamUtil.toList(carRecordReportIdParamList, CarRecordReportIdParam::getId));
    }

    @Override
    public CarRecordReport detail(CarRecordReportIdParam carRecordReportIdParam) {
        return this.queryEntity(carRecordReportIdParam.getId());
    }

    @Override
    public CarRecordReport queryEntity(String id) {
        CarRecordReport carRecordReport = this.getById(id);
        if(ObjectUtil.isEmpty(carRecordReport)) {
            throw new CommonException("CarRecordReport不存在，id值为：{}", id);
        }
        return carRecordReport;
    }

    @Override
    @DS("slave")
    public List<CarRecordReport> queryListForSync() {
        QueryWrapper<CarRecordReport> queryWrapper = new QueryWrapper<>();
        // 只查询未同步的数据  时间限制为当天  限制100条数据
        queryWrapper.lambda().eq(CarRecordReport::getSync, "0");
        queryWrapper.lambda().eq(CarRecordReport::getIsDelete, "0");
        queryWrapper.lambda().ge(CarRecordReport::getCreateTime, DateUtil.beginOfDay(DateUtil.parseDateTime(DateUtil.now())));
        queryWrapper.last("limit 100");
        return this.list(queryWrapper);
    }

    @Override
    @DS("slave")
    public void updateSyncStatus(String id) {
        UpdateWrapper<CarRecordReport> updateWrapper = new UpdateWrapper<>();
        //将id记录的sync字段更新为1
        updateWrapper.lambda().eq(CarRecordReport::getId, id);
        updateWrapper.lambda().set(CarRecordReport::getSync, "1");
        this.update(updateWrapper);
    }
}
