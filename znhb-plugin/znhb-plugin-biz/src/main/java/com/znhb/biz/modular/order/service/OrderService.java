/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package com.znhb.biz.modular.order.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.znhb.biz.modular.order.entity.Order;
import com.znhb.biz.modular.order.param.OrderAddParam;
import com.znhb.biz.modular.order.param.OrderEditParam;
import com.znhb.biz.modular.order.param.OrderIdParam;
import com.znhb.biz.modular.order.param.OrderPageParam;

import java.util.List;

/**
 * 车辆派单表Service接口
 *
 * <AUTHOR>
 * @date  2025/03/20 10:29
 **/
public interface OrderService extends IService<Order> {

    /**
     * 获取车辆派单表分页
     *
     * <AUTHOR>
     * @date  2025/03/20 10:29
     */
    Page<Order> page(OrderPageParam orderPageParam);

    /**
     * 添加车辆派单表
     *
     * <AUTHOR>
     * @date  2025/03/20 10:29
     */
    void add(OrderAddParam orderAddParam);

    /**
     * 编辑车辆派单表
     *
     * <AUTHOR>
     * @date  2025/03/20 10:29
     */
    void edit(OrderEditParam orderEditParam);

    /**
     * 删除车辆派单表
     *
     * <AUTHOR>
     * @date  2025/03/20 10:29
     */
    void delete(List<OrderIdParam> orderIdParamList);

    /**
     * 获取车辆派单表详情
     *
     * <AUTHOR>
     * @date  2025/03/20 10:29
     */
    Order detail(OrderIdParam orderIdParam);

    /**
     * 获取车辆派单表详情
     *
     * <AUTHOR>
     * @date  2025/03/20 10:29
     **/
    Order queryEntity(String id);


    /**
     * 获取车辆派单表详情
     *
     * <AUTHOR>
     **/
    Order queryGateOrder(String plateNumber, String orderStatus);

    /**
     * 更新订单状态
     */
    void updateOrderStatus(String orderId, String orderStatus);

}
