/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package com.znhb.biz.modular.car.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.znhb.biz.modular.car.entity.CarAccessRule;
import com.znhb.biz.modular.car.entity.CarRuleRelation;
import com.znhb.biz.modular.car.mapper.CarRuleRelationMapper;
import com.znhb.biz.modular.car.param.CarRuleRelationAddParam;
import com.znhb.biz.modular.car.param.CarRuleRelationIdParam;
import com.znhb.biz.modular.car.service.CarAccessRuleService;
import com.znhb.biz.modular.car.service.CarRuleRelationService;
import com.znhb.common.exception.CommonException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 小车规则关联Service接口实现类
 *
 * <AUTHOR>
 * @date  2025/04/10 15:00
 **/
@Service
public class CarRuleRelationServiceImpl extends ServiceImpl<CarRuleRelationMapper, CarRuleRelation> implements CarRuleRelationService {

    @Resource
    private CarAccessRuleService carAccessRuleService;

    @Override
    public List<CarRuleRelation> list(String carId) {
        // 直接使用 Mapper 中的方法获取关联的规则列表
        return baseMapper.getRelationListByCarId(carId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void add(CarRuleRelationAddParam param) {
        // 检查参数
        checkParam(param);

        // 创建实体
        CarRuleRelation carRuleRelation = BeanUtil.toBean(param, CarRuleRelation.class);

        // 保存
        this.save(carRuleRelation);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchAdd(List<CarRuleRelationAddParam> paramList) {
        if(CollUtil.isEmpty(paramList)) {
            throw new CommonException("参数不能为空");
        }

        List<CarRuleRelation> entityList = new ArrayList<>();

        for(CarRuleRelationAddParam param : paramList) {
            // 检查参数
            checkParam(param);

            // 创建实体
            CarRuleRelation carRuleRelation = BeanUtil.toBean(param, CarRuleRelation.class);
            entityList.add(carRuleRelation);
        }

        // 批量保存
        this.saveBatch(entityList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(CarRuleRelationIdParam param) {
        // 查询实体
        CarRuleRelation carRuleRelation = this.queryEntity(param.getId());

        // 删除
        this.removeById(carRuleRelation.getId());
    }

    /**
     * 检查参数
     *
     * @param param 参数
     */
    private void checkParam(CarRuleRelationAddParam param) {
        // 检查车辆ID
        if(StrUtil.isBlank(param.getCarId())) {
            throw new CommonException("车辆ID不能为空");
        }

        // 检查规则ID
        if(StrUtil.isBlank(param.getRuleId())) {
            throw new CommonException("规则ID不能为空");
        }

        // 检查规则是否存在
        CarAccessRule rule = carAccessRuleService.getById(param.getRuleId());
        if(ObjectUtil.isEmpty(rule)) {
            throw new CommonException("规则不存在");
        }

        // 检查车辆是否已经关联了规则（一辆车只能绑定一个规则）
        LambdaQueryWrapper<CarRuleRelation> carQueryWrapper = new LambdaQueryWrapper<>();
        carQueryWrapper.eq(CarRuleRelation::getCarId, param.getCarId());
        if(this.count(carQueryWrapper) > 0) {
            throw new CommonException("该车辆已经关联了规则，一辆车只能绑定一个规则");
        }
    }

    /**
     * 获取小车规则关联实体
     *
     * @param id 小车规则关联ID
     * @return 小车规则关联实体
     */
    private CarRuleRelation queryEntity(String id) {
        CarRuleRelation carRuleRelation = this.getById(id);
        if(ObjectUtil.isEmpty(carRuleRelation)) {
            throw new CommonException("小车规则关联不存在，id值为：{}", id);
        }
        return carRuleRelation;
    }
}
