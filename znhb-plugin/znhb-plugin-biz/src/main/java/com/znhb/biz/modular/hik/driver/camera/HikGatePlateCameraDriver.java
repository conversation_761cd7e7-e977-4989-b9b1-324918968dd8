package com.znhb.biz.modular.hik.driver.camera;

import lombok.extern.slf4j.Slf4j;
import com.znhb.biz.modular.hik.core.HCNetSDK;
import com.znhb.biz.modular.hik.core.HikSDKInitializer;
import com.znhb.biz.core.enums.DeviceTypeEnum;

/**
 * 海康门禁车牌识别机
 */
@Slf4j
public class HikGatePlateCameraDriver extends HikBaseCameraDriver{

    private final static String NAME = "海康车牌识别机驱动";

    private final HCNetSDK.NET_DVR_BARRIERGATE_CFG gateCfg = new HCNetSDK.NET_DVR_BARRIERGATE_CFG();

    public HikGatePlateCameraDriver() {
        super();
        defendJudge = true;
    }

    @Override
    public String type() {
        return DeviceTypeEnum.HIK_PLATE_CAMERA.getCode();
    }


    /**
     * @param op 0- 关闭道闸，1- 开启道闸，2- 停止道闸，3- 锁定道闸
     */
    private boolean ctrl(int op) {
        // dwCommand 宏定义 宏定义值 控制功能 lpInBuffer 对应结构体
        // NET_DVR_VEHICLE_DELINFO_CTRL 3125 删除设备内黑名单数据库信息
        // NET_DVR_VEHICLE_CONTROL_DELINFO
        // NET_DVR_BARRIERGATE_CTRL 3128 远程控制道闸 NET_DVR_BARRIERGATE_CFG
        // NET_DVR_GATELAMP_CTRL 3129 常亮灯功能 NET_DVR_GATELAMP_INFO

        // dwSize 结构体大小
        // dwChannel 通道号
        // byLaneNo 道闸号：0- 表示无效值(设备需要做有效值判断)，1- 道闸 1
        // byBarrierGateCtrl 控制参数：0- 关闭道闸，1- 开启道闸，2- 停止道闸（解锁），3- 锁定道闸
        // byRes 保留，置为 0
        gateCfg.dwChannel = 1;
        gateCfg.byBarrierGateCtrl = (byte) op;
        gateCfg.dwSize = gateCfg.size();
        gateCfg.byLaneNo = (byte) 1;
        gateCfg.write();

        boolean result = HikSDKInitializer.getInstance().NET_DVR_RemoteControl(userId, 3128, gateCfg.getPointer(), gateCfg.size());
        log.info(">>> {}-{} 开闸结果:{}", this.getDeviceName(), this.getDeviceIp(), result ? "成功" : "失败");
        if (!result) {
            log.error(">>> {}-{} 开闸失败，错误码为:{}", this.getDeviceName(), this.getDeviceIp(), HikSDKInitializer.getInstance().NET_DVR_GetLastError());
        }

        return result;
    }

    /**
     * 开闸
     */
    public boolean openGate() {
        return this.ctrl(1);
    }

    /**
     * 关闸
     */
    public boolean closeGate() {
        return this.ctrl(0);
    }

    /**
     * 解锁
     */
    public boolean unlockGate(){
        return this.ctrl(2);
    }

    /**
     * 锁定道闸
     */
    public boolean lockGate(){
        return this.ctrl(3);
    }
}
