package com.znhb.biz.modular.area.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;

/**
 * 区域ID参数
 *
 * <AUTHOR>
 * @date 2025-04-11
 */
@Getter
@Setter
@ApiModel("区域ID参数")
public class AreaIdParam {

    /** 主键ID */
    @ApiModelProperty(value = "主键ID", required = true)
    @NotBlank(message = "主键ID不能为空")
    private String id;
}
