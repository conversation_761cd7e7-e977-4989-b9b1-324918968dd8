package com.znhb.biz.modular.area.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.znhb.biz.modular.area.entity.Area;
import com.znhb.biz.modular.area.mapper.AreaMapper;
import com.znhb.biz.modular.area.param.AreaAddParam;
import com.znhb.biz.modular.area.param.AreaEditParam;
import com.znhb.biz.modular.area.param.AreaIdParam;
import com.znhb.biz.modular.area.param.AreaPageParam;
import com.znhb.biz.modular.area.result.AreaTreeResult;
import com.znhb.biz.modular.area.service.AreaService;
import com.znhb.biz.modular.barrierGate.param.BarrierGateIdParam;
import com.znhb.common.exception.CommonException;
import com.znhb.common.page.CommonPageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 区域 Service 实现类
 *
 * <AUTHOR>
 * @date 2025-04-11
 */
@Service
public class AreaServiceImpl extends ServiceImpl<AreaMapper, Area> implements AreaService {

    @Override
    public Page<Area> page(AreaPageParam areaPageParam) {
        QueryWrapper<Area> queryWrapper = new QueryWrapper<>();
        
        // 查询条件
        if (StrUtil.isNotBlank(areaPageParam.getAreaName())) {
            queryWrapper.lambda().like(Area::getAreaName, areaPageParam.getAreaName());
        }
        
        if (StrUtil.isNotBlank(areaPageParam.getAreaType())) {
            queryWrapper.lambda().eq(Area::getAreaType, areaPageParam.getAreaType());
        }
        
        if (StrUtil.isNotBlank(areaPageParam.getParentId())) {
            queryWrapper.lambda().eq(Area::getParentId, areaPageParam.getParentId());
        }
        
        // 排序
        queryWrapper.lambda().orderByAsc(Area::getSortCode);
        
        // 分页查询
        return this.page(CommonPageRequest.defaultPage(), queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void add(AreaAddParam areaAddParam) {
        // 检查区域名称是否重复
        checkAreaNameRepeat(areaAddParam.getAreaName(), areaAddParam.getParentId(), null);
        
        // 实体转换
        Area area = BeanUtil.toBean(areaAddParam, Area.class);
        
        // 设置默认值
        if (StrUtil.isBlank(area.getParentId())) {
            area.setParentId("0");
        }
        
        if (ObjectUtil.isNull(area.getSortCode())) {
            area.setSortCode(99);
        }
        
        // 保存区域
        this.save(area);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void edit(AreaEditParam areaEditParam) {
        // 检查区域是否存在
        Area area = this.getById(areaEditParam.getId());
        if (ObjectUtil.isNull(area)) {
            throw new CommonException("区域不存在");
        }
        
        // 检查区域名称是否重复
        checkAreaNameRepeat(areaEditParam.getAreaName(), areaEditParam.getParentId(), areaEditParam.getId());
        
        // 检查上级区域是否是自己或自己的子区域
        checkParentArea(areaEditParam.getId(), areaEditParam.getParentId());
        
        // 实体转换
        BeanUtil.copyProperties(areaEditParam, area);
        
        // 更新区域
        this.updateById(area);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(List<AreaIdParam> areaIdParamIds) {
        if (CollUtil.isEmpty(areaIdParamIds) && areaIdParamIds.size() == 1) {
            // 检查区域是否存在
            Area area = this.getById(areaIdParamIds.get(0).getId());
            if (ObjectUtil.isNull(area)) {
                throw new CommonException("区域不存在");
            }

            // 检查是否有子区域
            LambdaQueryWrapper<Area> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(Area::getParentId, areaIdParamIds.get(0).getId());
            long count = this.count(queryWrapper);
            if (count > 0) {
                throw new CommonException("请先删除子区域");
            }
            this.removeById(area.getId());
        }else if (CollUtil.isNotEmpty(areaIdParamIds)){
            // 删除区域
            this.removeByIds(CollStreamUtil.toList(areaIdParamIds, AreaIdParam::getId));
        }
    }

    @Override
    public Area detail(AreaIdParam areaIdParam) {
        // 获取区域详情
        return this.getById(areaIdParam.getId());
    }

    @Override
    public List<AreaTreeResult> tree() {
        // 获取所有区域
        List<Area> areaList = this.list();
        
        // 转换为树形结构
        return buildAreaTree(areaList, "0");
    }
    
    /**
     * 构建区域树形结构
     *
     * @param areaList 区域列表
     * @param parentId 父级ID
     * @return 区域树形列表
     */
    private List<AreaTreeResult> buildAreaTree(List<Area> areaList, String parentId) {
        if (CollUtil.isEmpty(areaList)) {
            return new ArrayList<>();
        }
        
        // 筛选当前层级的区域
        List<Area> currentLevelAreas = areaList.stream()
                .filter(area -> parentId.equals(area.getParentId()))
                .collect(Collectors.toList());
        
        // 构建树形结构
        List<AreaTreeResult> treeResults = new ArrayList<>();
        for (Area area : currentLevelAreas) {
            AreaTreeResult treeResult = new AreaTreeResult();
            BeanUtil.copyProperties(area, treeResult);
            
            // 递归构建子区域
            List<AreaTreeResult> children = buildAreaTree(areaList, area.getId());
            treeResult.setChildren(children);
            
            treeResults.add(treeResult);
        }
        
        return treeResults;
    }
    
    /**
     * 检查区域名称是否重复
     *
     * @param areaName 区域名称
     * @param parentId 父级ID
     * @param areaId   区域ID（编辑时使用）
     */
    private void checkAreaNameRepeat(String areaName, String parentId, String areaId) {
        if (StrUtil.isBlank(areaName)) {
            return;
        }
        
        LambdaQueryWrapper<Area> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Area::getAreaName, areaName);
        queryWrapper.eq(Area::getParentId, StrUtil.isBlank(parentId) ? "0" : parentId);
        
        // 编辑时排除自身
        if (StrUtil.isNotBlank(areaId)) {
            queryWrapper.ne(Area::getId, areaId);
        }
        
        long count = this.count(queryWrapper);
        if (count > 0) {
            throw new CommonException("同一层级下区域名称不能重复");
        }
    }
    
    /**
     * 检查上级区域是否是自己或自己的子区域
     *
     * @param areaId   区域ID
     * @param parentId 父级ID
     */
    private void checkParentArea(String areaId, String parentId) {
        if (StrUtil.isBlank(parentId) || "0".equals(parentId) || StrUtil.isBlank(areaId)) {
            return;
        }
        
        // 不能选择自己作为上级
        if (areaId.equals(parentId)) {
            throw new CommonException("不能选择自己作为上级区域");
        }
        
        // 获取所有子区域ID
        List<String> childrenIds = getChildrenIds(areaId);
        
        // 不能选择自己的子区域作为上级
        if (childrenIds.contains(parentId)) {
            throw new CommonException("不能选择自己的子区域作为上级区域");
        }
    }
    
    /**
     * 获取所有子区域ID
     *
     * @param areaId 区域ID
     * @return 子区域ID列表
     */
    private List<String> getChildrenIds(String areaId) {
        List<String> childrenIds = new ArrayList<>();
        
        // 查询直接子区域
        LambdaQueryWrapper<Area> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Area::getParentId, areaId);
        List<Area> children = this.list(queryWrapper);
        
        if (CollUtil.isNotEmpty(children)) {
            // 添加直接子区域ID
            List<String> directChildrenIds = children.stream()
                    .map(Area::getId)
                    .collect(Collectors.toList());
            childrenIds.addAll(directChildrenIds);
            
            // 递归获取所有子区域ID
            for (String childId : directChildrenIds) {
                childrenIds.addAll(getChildrenIds(childId));
            }
        }
        
        return childrenIds;
    }
}
