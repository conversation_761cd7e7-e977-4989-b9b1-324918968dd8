/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package com.znhb.biz.modular.car.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import com.znhb.common.pojo.CommonEntity;

/**
 * 小车与规则关联实体
 *
 * <AUTHOR>
 * @date  2025/04/10 15:00
 **/
@Getter
@Setter
@TableName("t_car_rule_relation")
public class CarRuleRelation extends CommonEntity {

    /** id */
    @TableId
    @ApiModelProperty(value = "id", position = 1)
    private String id;

    /** 小车ID */
    @ApiModelProperty(value = "小车ID", position = 2)
    private String carId;

    /** 规则ID */
    @ApiModelProperty(value = "规则ID", position = 3)
    private String ruleId;

    /** 规则名称 - 临时字段，不存入数据库 */
    @ApiModelProperty(value = "规则名称", position = 5)
    @TableField(exist = false)
    private String ruleName;

    /** 规则优先级 - 临时字段，不存入数据库 */
    @ApiModelProperty(value = "规则优先级", position = 6)
    @TableField(exist = false)
    private Integer priority;

    /** 启用时间限制 - 临时字段，不存入数据库 */
    @ApiModelProperty(value = "启用时间限制", position = 8)
    @TableField(exist = false)
    private Boolean enableTimeRule;

    /** 启用次数限制 - 临时字段，不存入数据库 */
    @ApiModelProperty(value = "启用次数限制", position = 9)
    @TableField(exist = false)
    private Boolean enableCountRule;

    /** 启用区域限制 - 临时字段，不存入数据库 */
    @ApiModelProperty(value = "启用区域限制", position = 10)
    @TableField(exist = false)
    private Boolean enableAreaRule;

    /** 启用时长限制 - 临时字段，不存入数据库 */
    @ApiModelProperty(value = "启用时长限制", position = 11)
    @TableField(exist = false)
    private Boolean enableDurationRule;
}
