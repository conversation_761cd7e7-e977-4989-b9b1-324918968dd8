package com.znhb.biz.modular.area.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.znhb.biz.modular.area.entity.Area;
import com.znhb.biz.modular.area.param.AreaAddParam;
import com.znhb.biz.modular.area.param.AreaEditParam;
import com.znhb.biz.modular.area.param.AreaIdParam;
import com.znhb.biz.modular.area.param.AreaPageParam;
import com.znhb.biz.modular.area.result.AreaTreeResult;

import java.util.List;

/**
 * 区域 Service 接口
 *
 * <AUTHOR>
 * @date 2025-04-11
 */
public interface AreaService extends IService<Area> {

    /**
     * 获取区域分页列表
     *
     * @param areaPageParam 区域分页参数
     * @return 区域分页列表
     */
    Page<Area> page(AreaPageParam areaPageParam);

    /**
     * 添加区域
     *
     * @param areaAddParam 区域添加参数
     */
    void add(AreaAddParam areaAddParam);

    /**
     * 编辑区域
     *
     * @param areaEditParam 区域编辑参数
     */
    void edit(AreaEditParam areaEditParam);

    /**
     * 删除区域
     *
     * @param areaIdParam 区域ID参数
     */
    void delete(List<AreaIdParam> areaIdParam);

    /**
     * 获取区域详情
     *
     * @param areaIdParam 区域ID参数
     * @return 区域详情
     */
    Area detail(AreaIdParam areaIdParam);

    /**
     * 获取区域树形列表
     *
     * @return 区域树形列表
     */
    List<AreaTreeResult> tree();
}
