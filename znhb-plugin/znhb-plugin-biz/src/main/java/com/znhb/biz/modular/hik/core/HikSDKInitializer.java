package com.znhb.biz.modular.hik.core;

import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.system.SystemUtil;
import com.sun.jna.Native;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * 海康威视SDK初始化器
 * 实现InitializingBean和DisposableBean接口以便在Spring容器启动和关闭时自动调用
 */
@Slf4j
@Component
public class HikSDKInitializer implements InitializingBean, DisposableBean {

    /** SDK实例 */
    private static HCNetSDK instance;

    /** 是否已初始化 */
    private static boolean initialized = false;

    /** SDK配置属性 */
    @Value("${hiksdk.enabled:false}")
    private boolean enabled;

    @Value("${hiksdk.linux-lib-path:/hik/linux}")
    private String linuxLibPath;

    @Value("${hiksdk.windows-lib-path:\\hik\\windows}")
    private String windowsLibPath;

    @Value("${hiksdk.connect-timeout:10}")
    private int connectTimeout;

    @Value("${hiksdk.reconnect-time:100}")
    private int reconnectTime;

    @Value("${hiksdk.check-online-timeout:30}")
    private int checkOnlineTimeout;

    @Value("${hiksdk.check-online-net-fail-max:3}")
    private int checkOnlineNetFailMax;

    /**
     * 获取SDK实例
     * @return SDK实例
     * @throws IllegalStateException 如果SDK未初始化
     */
    public static HCNetSDK getInstance() {
        if (!initialized || instance == null) {
            throw new IllegalStateException("海康SDK尚未初始化，请检查配置或日志");
        }
        return instance;
    }

    /**
     * 检查SDK是否已初始化
     * @return 是否已初始化
     */
    public static boolean isInitialized() {
        return initialized && instance != null;
    }

    /**
     * 初始化SDK
     * 在Spring容器启动时自动调用
     */
    @Override
    public void afterPropertiesSet() throws Exception {
        if (!enabled) {
            log.info("海康SDK初始化已禁用，跳过初始化");
            return;
        }

        try {
            log.info("开始初始化海康SDK...");
            // 加载库文件
            loadLibraries();
            
            // SDK初始化
            if (!instance.NET_DVR_Init()) {
                throw new RuntimeException("海康SDK初始化失败，错误码：" + instance.NET_DVR_GetLastError());
            }
            log.info("海康SDK初始化成功");
            
            // 设置通用参数
            configureGeneralSettings();
            
            // 设置网络参数
            configureNetworkSettings();
            
            // 设置设备检查
            configureDeviceChecking();
            
            // 设置回调
            configureCallbacks();
            
            initialized = true;
            log.info("海康SDK完成所有初始化设置");
        } catch (Exception e) {
            initialized = false;
            log.error("海康SDK初始化失败: {}", e.getMessage(), e);
            // 释放已加载的资源
            if (instance != null) {
                instance.NET_DVR_Cleanup();
            }
            throw new RuntimeException("海康SDK初始化失败", e);
        }
    }

    /**
     * 加载SDK库文件
     */
    private void loadLibraries() {
        try {
            if (SystemUtil.getOsInfo().isLinux()) {
                loadLinuxLibraries();
            } else {
                loadWindowsLibraries();
            }
        } catch (Exception e) {
            log.error("海康SDK库文件加载失败: {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 加载Linux环境下的库文件
     */
    private void loadLinuxLibraries() {
        // 根目录
        String baseDir = System.getProperty("user.dir");
        linuxLibPath = baseDir + linuxLibPath;
        
        // 设置libhcnetsdk.so路径
        String libPath = linuxLibPath + "/libhcnetsdk.so";
        log.info("加载linux海康SDK库: {}", libPath);
        instance = (HCNetSDK) Native.loadLibrary(libPath, HCNetSDK.class);


        // 设置HCNetSDKCom组件库路径
        log.info("设置海康组件库路径: {}", linuxLibPath);
        HCNetSDK.NET_DVR_LOCAL_SDK_PATH struComPath = new HCNetSDK.NET_DVR_LOCAL_SDK_PATH();
        System.arraycopy(linuxLibPath.getBytes(), 0, struComPath.sPath, 0, linuxLibPath.length());
        struComPath.write();
        instance.NET_DVR_SetSDKInitCfg(2, struComPath.getPointer());

        // 设置libcrypto.so路径
        String cryptoPath = linuxLibPath + "/libcrypto.so.1.1";
        log.info("设置libcrypto路径: {}", cryptoPath);
        HCNetSDK.BYTE_ARRAY ptrByteArrayCrypto = new HCNetSDK.BYTE_ARRAY(256);
        System.arraycopy(cryptoPath.getBytes(), 0, ptrByteArrayCrypto.byValue, 0, cryptoPath.length());
        ptrByteArrayCrypto.write();
        instance.NET_DVR_SetSDKInitCfg(3, ptrByteArrayCrypto.getPointer());

        // 设置libssl.so路径
        String sslPath = linuxLibPath + "/libssl.so.1.1";
        log.info("设置libssl路径: {}", sslPath);
        HCNetSDK.BYTE_ARRAY ptrByteArraySsl = new HCNetSDK.BYTE_ARRAY(256);
        System.arraycopy(sslPath.getBytes(), 0, ptrByteArraySsl.byValue, 0, sslPath.length());
        ptrByteArraySsl.write();
        instance.NET_DVR_SetSDKInitCfg(4, ptrByteArraySsl.getPointer());
    }

    /**
     * 加载Windows环境下的库文件
     */
    private void loadWindowsLibraries() {
        String baseDir = System.getProperty("user.dir");
        windowsLibPath = baseDir + windowsLibPath;
        String libPath = windowsLibPath + "\\HCNetSDK.dll";
        log.info("加载windows海康SDK库: {}", libPath);
        instance = (HCNetSDK) Native.loadLibrary(libPath, HCNetSDK.class);
    }

    /**
     * 配置SDK通用参数
     */
    private void configureGeneralSettings() {
        HCNetSDK.NET_DVR_LOCAL_GENERAL_CFG generalCfg = new HCNetSDK.NET_DVR_LOCAL_GENERAL_CFG();
        generalCfg.byAlarmJsonPictureSeparate = (byte) 1;
        generalCfg.write();
        boolean result = instance.NET_DVR_SetSDKLocalCfg(17, generalCfg.getPointer());
        checkResult(result, "设置通用参数");
    }

    /**
     * 配置网络连接参数
     */
    private void configureNetworkSettings() {
        instance.NET_DVR_SetConnectTime(connectTimeout, 1);
        instance.NET_DVR_SetReconnect(reconnectTime, true);
        log.info("海康SDK网络参数设置完成: 连接超时={}秒, 重连间隔={}毫秒", connectTimeout, reconnectTime);
    }

    /**
     * 配置设备检查参数
     */
    private void configureDeviceChecking() {
        HCNetSDK.NET_DVR_LOCAL_CHECK_DEV checkDev = new HCNetSDK.NET_DVR_LOCAL_CHECK_DEV();
        checkDev.dwCheckOnlineTimeout = checkOnlineTimeout;
        checkDev.dwCheckOnlineNetFailMax = checkOnlineNetFailMax;
        boolean result = instance.NET_DVR_SetSDKLocalCfg(10, checkDev.getPointer());
        checkResult(result, "设置设备检查参数");
    }

    /**
     * 配置回调函数
     */
    private void configureCallbacks() {
        //设置异常回调函数
        GlobalExceptionCallBack globalExceptionCallBack = SpringUtil.getBean(GlobalExceptionCallBack.class);
        boolean result = instance.NET_DVR_SetExceptionCallBack_V30(0, 0, globalExceptionCallBack, null);
        checkResult(result, "设置异常回调函数");

        //设置告警回调函数
        GlobalMessageCallBack globalMessageCallBack = SpringUtil.getBean(GlobalMessageCallBack.class);
        result = instance.NET_DVR_SetDVRMessageCallBack_V31(globalMessageCallBack, null);
        checkResult(result, "设置告警回调函数");
    }

    /**
     * 检查操作结果，失败时记录日志
     */
    private void checkResult(boolean success, String operation) {
        if (!success) {
            int errorCode = instance.NET_DVR_GetLastError();
            String msg = operation + "失败，错误码: " + errorCode;
            log.error(msg);
            // 对于关键操作，可以考虑抛出异常
            // throw new RuntimeException(msg);
        } else {
            log.info("{}成功", operation);
        }
    }

    /**
     * 释放SDK资源
     * 在Spring容器关闭时自动调用
     */
    @Override
    public void destroy() {
        if (initialized && instance != null) {
            log.info("正在释放海康SDK资源...");
            boolean result = instance.NET_DVR_Cleanup();
            log.info("海康SDK资源释放{}", result ? "成功" : "失败");
            initialized = false;
            instance = null;
        }
    }
}
