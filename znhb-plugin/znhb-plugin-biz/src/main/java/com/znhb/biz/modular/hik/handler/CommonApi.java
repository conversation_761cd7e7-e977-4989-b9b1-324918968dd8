package com.znhb.biz.modular.hik.handler;

import com.znhb.biz.modular.barrierGate.entity.BarrierGate;
import com.znhb.biz.modular.car.entity.CarInfo;
import com.znhb.biz.modular.hik.driver.barrierGate.BarrierGateDriver;
import com.znhb.biz.modular.hik.entity.CheckParam;
import com.znhb.biz.modular.hik.entity.MeterParam;
import com.znhb.biz.modular.order.entity.Order;

import java.io.UnsupportedEncodingException;
import java.util.Date;


public interface CommonApi {
    /**
     * 车辆洗车数据查询 返回true代表未清洗  false已清洗
     */
    boolean judgeNotWash(String car_number, String startTime, String endTime);

    /**
     * 车辆进厂验证
     */
    CheckParam checkInAction(String plateNumber, String vehicleType);

    /**
     * 车辆出厂验证
     */
    CheckParam checkOutAction(String plateNumber, String vehicleType) throws UnsupportedEncodingException;

    /**
     * 获取计量数据
     */
    MeterParam searchMeterData(String plateNumber, String entryTime, String exitTime, Order order);

    /**
     * 获取计量数据
     */
    MeterParam searchMeterData(String plateNumber, String entryTime, String exitTime);

    /**
     * 排放阶段验证
     */
    CheckParam emissionCheck(BarrierGateDriver driver, String emissionStage);

    /**
     * 小车入厂规则验证
     */
    CheckParam checkInCarAccessRule(CarInfo carInfo, BarrierGate barrierGate);

    /**
     * 小车出厂规则验证
     */
    CheckParam checkOutCarAccessRule(CarInfo carInfo, BarrierGate barrierGate, Date entryTime);
}
