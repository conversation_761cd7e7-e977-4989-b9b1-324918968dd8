/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package com.znhb.biz.modular.vehicleRecord.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.znhb.biz.modular.hik.handler.ruigang.RuiGangCommonApi;
import com.znhb.biz.core.enums.*;
import com.znhb.biz.modular.barrierGate.entity.BarrierGate;
import com.znhb.biz.modular.barrierGate.service.BarrierGateService;
import com.znhb.biz.modular.hik.entity.GoodsMeterInfo;
import com.znhb.biz.modular.hik.entity.MeterParam;
import com.znhb.biz.modular.materialcategory.entity.MaterialCategory;
import com.znhb.biz.modular.materialcategory.service.MaterialCategoryService;
import com.znhb.biz.modular.materialdetail.entity.MaterialDetail;
import com.znhb.biz.modular.materialdetail.service.MaterialDetailService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.znhb.common.enums.CommonSortOrderEnum;
import com.znhb.common.exception.CommonException;
import com.znhb.common.page.CommonPageRequest;
import com.znhb.biz.modular.vehicleRecord.entity.CarRecord;
import com.znhb.biz.modular.vehicleRecord.mapper.CarRecordMapper;
import com.znhb.biz.modular.vehicleRecord.param.CarRecordAddParam;
import com.znhb.biz.modular.vehicleRecord.param.CarRecordEditParam;
import com.znhb.biz.modular.vehicleRecord.param.CarRecordIdParam;
import com.znhb.biz.modular.vehicleRecord.param.CarRecordPageParam;
import com.znhb.biz.modular.vehicleRecord.service.CarRecordService;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 运输台账Service接口实现类
 *
 * <AUTHOR>
 * @date  2025/03/22 10:05
 **/
@Service
@Slf4j
public class CarRecordServiceImpl extends ServiceImpl<CarRecordMapper, CarRecord> implements CarRecordService {

    @Resource
    private BarrierGateService barrierGateService;

    @Resource
    private MaterialDetailService materialDetailService;

    @Resource
    private MaterialCategoryService materialCategoryService;

    @Resource
    private RuiGangCommonApi ruiGangCommonApi;

    @Override
    public Page<CarRecord> page(CarRecordPageParam carRecordPageParam) {
        QueryWrapper<CarRecord> queryWrapper = new QueryWrapper<>();
        if(ObjectUtil.isNotEmpty(carRecordPageParam.getPlateNumber())) {
            queryWrapper.lambda().like(CarRecord::getPlateNumber, carRecordPageParam.getPlateNumber());
        }
        if(ObjectUtil.isNotEmpty(carRecordPageParam.getVehicleCategory())) {
            queryWrapper.lambda().eq(CarRecord::getVehicleCategory, carRecordPageParam.getVehicleCategory());
        }
        if(ObjectUtil.isNotEmpty(carRecordPageParam.getInOrderNumber())) {
            queryWrapper.lambda().like(CarRecord::getInOrderNumber, carRecordPageParam.getInOrderNumber());
        }
        if(ObjectUtil.isNotEmpty(carRecordPageParam.getInGoodsName())) {
            queryWrapper.lambda().like(CarRecord::getInGoodsName, carRecordPageParam.getInGoodsName());
        }
        if(ObjectUtil.isNotEmpty(carRecordPageParam.getInGateId())) {
            queryWrapper.lambda().eq(CarRecord::getInGateId, carRecordPageParam.getInGateId());
        }
        if(ObjectUtil.isNotEmpty(carRecordPageParam.getStartInCreateTime()) && ObjectUtil.isNotEmpty(carRecordPageParam.getEndInCreateTime())) {
            queryWrapper.lambda().between(CarRecord::getInCreateTime, carRecordPageParam.getStartInCreateTime(), carRecordPageParam.getEndInCreateTime());
        }
        if(ObjectUtil.isNotEmpty(carRecordPageParam.getOutGateId())) {
            queryWrapper.lambda().eq(CarRecord::getOutGateId, carRecordPageParam.getOutGateId());
        }
        if(ObjectUtil.isNotEmpty(carRecordPageParam.getStartOutCreateTime()) && ObjectUtil.isNotEmpty(carRecordPageParam.getEndOutCreateTime())) {
            queryWrapper.lambda().between(CarRecord::getOutCreateTime, carRecordPageParam.getStartOutCreateTime(), carRecordPageParam.getEndOutCreateTime());
        }
        if(ObjectUtil.isAllNotEmpty(carRecordPageParam.getSortField(), carRecordPageParam.getSortOrder())) {
            CommonSortOrderEnum.validate(carRecordPageParam.getSortOrder());
            queryWrapper.orderBy(true, carRecordPageParam.getSortOrder().equals(CommonSortOrderEnum.ASC.getValue()),
                    StrUtil.toUnderlineCase(carRecordPageParam.getSortField()));
        } else {
            queryWrapper.lambda().orderByDesc(CarRecord::getUpdateTime);
        }
        Page<CarRecord> page = this.page(CommonPageRequest.defaultPage(), queryWrapper);
        if(ObjectUtil.isNotEmpty(page.getRecords())) {
            for(CarRecord carRecord: page.getRecords()) {
                MaterialDetail inDetail = materialDetailService.getById(carRecord.getInGoodsId());
                MaterialDetail outDetail = materialDetailService.getById(carRecord.getOutGoodsId());
                MaterialCategory inCategory = materialCategoryService.getById(carRecord.getInCategoryId());
                MaterialCategory outCategory = materialCategoryService.getById(carRecord.getOutCategoryId());
                if(ObjectUtil.isNotEmpty(inDetail)) {
                    carRecord.setInGoodsName(inDetail.getName());
                    carRecord.setInGoodsId(inDetail.getId());
                }
                if(ObjectUtil.isNotEmpty(outDetail)) {
                    carRecord.setOutGoodsName(outDetail.getName());
                    carRecord.setOutGoodsId(outDetail.getId());
                }
                if(ObjectUtil.isNotEmpty(inCategory)) {
                    carRecord.setInCategoryName(inCategory.getName());
                    carRecord.setInCategoryId(inCategory.getId());
                }
                if(ObjectUtil.isNotEmpty(outCategory)) {
                    carRecord.setOutCategoryName(outCategory.getName());
                    carRecord.setOutCategoryId(outCategory.getId());
                }
            }
        }
        return page;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void add(CarRecordAddParam carRecordAddParam) {
        CarRecord carRecord = BeanUtil.toBean(carRecordAddParam, CarRecord.class);
        String inGateId = carRecord.getInGateId();
        if(ObjectUtil.isNotEmpty(inGateId)) {
            BarrierGate barrierGate = barrierGateService.queryEntity(inGateId);
            carRecord.setInAreaId(barrierGate.getAreaId());
            carRecord.setInAreaName(barrierGate.getAreaName());
        }
        this.save(carRecord);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(CarRecordEditParam carRecordEditParam) {
        CarRecord carRecord = this.queryEntity(carRecordEditParam.getId());
        BeanUtil.copyProperties(carRecordEditParam, carRecord);
        String inGateId = carRecord.getInGateId();
        if(ObjectUtil.isNotEmpty(inGateId)) {
            BarrierGate barrierGate = barrierGateService.queryEntity(inGateId);
            carRecord.setEntryCaptureDeviceId(barrierGate.getBodyCaptureCameraId());
            carRecord.setInAreaId(barrierGate.getAreaId());
            carRecord.setInAreaName(barrierGate.getAreaName());
        }
        String outGateId = carRecordEditParam.getOutGateId();
        if(ObjectUtil.isNotEmpty(outGateId)) {
            BarrierGate barrierGate = barrierGateService.queryEntity(outGateId);
            carRecord.setExitCaptureDeviceId(barrierGate.getBodyCaptureCameraId());
            carRecord.setOutAreaId(barrierGate.getAreaId());
            carRecord.setOutAreaName(barrierGate.getAreaName());
        }

        this.updateById(carRecord);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<CarRecordIdParam> carRecordIdParamList) {
        // 执行删除
        this.removeByIds(CollStreamUtil.toList(carRecordIdParamList, CarRecordIdParam::getId));
    }

    @Override
    public CarRecord detail(CarRecordIdParam carRecordIdParam) {
        CarRecord carRecord = this.queryEntity(carRecordIdParam.getId());
        MaterialDetail inDetail = materialDetailService.getById(carRecord.getInGoodsId());
        MaterialDetail outDetail = materialDetailService.getById(carRecord.getOutGoodsId());
        MaterialCategory inCategory = materialCategoryService.getById(carRecord.getInCategoryId());
        MaterialCategory outCategory = materialCategoryService.getById(carRecord.getOutCategoryId());
        if(ObjectUtil.isNotEmpty(inDetail)) {
            carRecord.setInGoodsName(inDetail.getName());
            carRecord.setInGoodsId(inDetail.getId());
        }
        if(ObjectUtil.isNotEmpty(outDetail)) {
            carRecord.setOutGoodsName(outDetail.getName());
            carRecord.setOutGoodsId(outDetail.getId());
        }
        if(ObjectUtil.isNotEmpty(inCategory)) {
            carRecord.setInCategoryName(inCategory.getName());
            carRecord.setInCategoryId(inCategory.getId());
        }
        if(ObjectUtil.isNotEmpty(outCategory)) {
            carRecord.setOutCategoryName(outCategory.getName());
            carRecord.setOutCategoryId(outCategory.getId());
        }
        return carRecord;
    }

    @Override
    public CarRecord queryEntity(String id) {
        CarRecord carRecord = this.getById(id);
        if(ObjectUtil.isEmpty(carRecord)) {
            throw new CommonException("运输台账不存在，id值为：{}", id);
        }
        return carRecord;
    }

    @Override
    public CarRecord queryLastInRecord(String plateNumber) {
        QueryWrapper<CarRecord> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(CarRecord::getPlateNumber, plateNumber);
        //out_status 为 0 表示未出场
        queryWrapper.lambda().eq(CarRecord::getOutStatus, OutStatusEnum.NOT_OUT.getCode());
        queryWrapper.lambda().orderByDesc(CarRecord::getInCreateTime);
        queryWrapper.last("limit 1");
        return this.getOne(queryWrapper);
    }

    @Override
    public void confirmExit(CarRecordIdParam carRecordIdParam) {
        UpdateWrapper<CarRecord> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda().eq(CarRecord::getId, carRecordIdParam.getId());
        updateWrapper.lambda().set(CarRecord::getOutStatus, OutStatusEnum.OUTED.getCode());
        this.update(updateWrapper);
    }

    @Override
    public void cancelExit(CarRecordIdParam carRecordIdParam) {
        UpdateWrapper<CarRecord> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda().eq(CarRecord::getId, carRecordIdParam.getId());
        updateWrapper.lambda().set(CarRecord::getOutStatus, OutStatusEnum.NOT_OUT.getCode());
        updateWrapper.lambda().set(CarRecord::getCarWashStatus, CarWashStatusEnum.NOT_WASH.getCode());
        updateWrapper.lambda().set(CarRecord::getOutOrderNumber, null);
        updateWrapper.lambda().set(CarRecord::getOutGoodsName, null);
        updateWrapper.lambda().set(CarRecord::getOutGoodsId, null);
        updateWrapper.lambda().set(CarRecord::getOutCategoryId, null);
        updateWrapper.lambda().set(CarRecord::getOutCategoryName, null);
        updateWrapper.lambda().set(CarRecord::getOutWeight, null);
        updateWrapper.lambda().set(CarRecord::getOutPlateImgUrl, null);
        updateWrapper.lambda().set(CarRecord::getOutCarImgUrl, null);
        updateWrapper.lambda().set(CarRecord::getOutBodyImgUrl, null);
        updateWrapper.lambda().set(CarRecord::getOutTailImgUrl, null);
        updateWrapper.lambda().set(CarRecord::getOutGateId, null);
        updateWrapper.lambda().set(CarRecord::getOutGateName, null);
        updateWrapper.lambda().set(CarRecord::getOutCreateTime, null);
        updateWrapper.lambda().set(CarRecord::getSyncStatus, SyncStatusEnum.NOT_SYNC.getCode());
        this.update(updateWrapper);
    }

    @Override
    public List<CarRecord> queryCarRecordForSync() {
        QueryWrapper<CarRecord> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(CarRecord::getOutStatus, OutStatusEnum.OUTED.getCode());
        queryWrapper.lambda().eq(CarRecord::getSyncStatus, SyncStatusEnum.NOT_SYNC.getCode());
        queryWrapper.lambda().ge(CarRecord::getUpdateTime, DateUtil.beginOfDay(DateUtil.parseDateTime(DateUtil.now())));
        queryWrapper.lambda().le(CarRecord::getUpdateTime, DateUtil.endOfDay(DateUtil.parseDateTime(DateUtil.now())));
        return this.list(queryWrapper);
    }

    @Override
    public void updateForSync(String id) {
        UpdateWrapper<CarRecord> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda().eq(CarRecord::getId, id);
        updateWrapper.lambda().set(CarRecord::getSyncStatus, SyncStatusEnum.SYNCED.getCode());
        this.update(updateWrapper);
    }

    @Override
    public void fetchMeterData(CarRecordIdParam carRecordIdParam) {
        CarRecord carRecord = this.getById(carRecordIdParam.getId());
        MeterParam meterParam = ruiGangCommonApi.searchMeterData(carRecordIdParam.getPlateNumber(), DateUtil.formatDateTime(carRecord.getInCreateTime()), DateUtil.formatDateTime(carRecord.getOutCreateTime()));
        if (!meterParam.getResult()){
            throw new CommonException(meterParam.getMessage());
        }else {
            //更新计量数据
            GoodsMeterInfo meter = meterParam.getGoodsMeterInfo();
            //插入物料
            materialDetailService.saveForGate(meter.getInGoodsName(), meter.getInCategoryName());
            materialDetailService.saveForGate(meter.getOutGoodsName(), meter.getOutCategoryName());

            carRecord.setInOrderNumber(meter.getInOrderNumber());
            carRecord.setInGoodsName(meter.getInGoodsName());
            carRecord.setInCategoryName(meter.getInCategoryName());
            carRecord.setInGoodsUnit(meter.getInGoodsUnit());
            carRecord.setInWeight(meter.getInWeight());

            carRecord.setOutOrderNumber(meter.getOutOrderNumber());
            carRecord.setOutGoodsName(meter.getOutGoodsName());
            carRecord.setOutCategoryName(meter.getOutCategoryName());
            carRecord.setOutGoodsUnit(meter.getOutGoodsUnit());
            carRecord.setOutWeight(meter.getOutWeight());
            //更新车辆计量数据
            this.updateById(carRecord);
        }
    }

    @Override
    public void updateEntryCaptureImgUrl(String recordId, String bodyImgUrl, String tailImgUrl) {
        UpdateWrapper<CarRecord> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda().eq(CarRecord::getId, recordId);
        updateWrapper.lambda().set(CarRecord::getInBodyImgUrl, bodyImgUrl);
        updateWrapper.lambda().set(CarRecord::getInTailImgUrl, tailImgUrl);
        this.update(updateWrapper);
    }

    @Override
    public void updateExitCaptureImgUrl(String recordId, String bodyImgUrl, String tailImgUrl) {
        UpdateWrapper<CarRecord> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda().eq(CarRecord::getId, recordId);
        updateWrapper.lambda().set(CarRecord::getOutBodyImgUrl, bodyImgUrl);
        updateWrapper.lambda().set(CarRecord::getOutTailImgUrl, tailImgUrl);
        this.update(updateWrapper);
    }
}
