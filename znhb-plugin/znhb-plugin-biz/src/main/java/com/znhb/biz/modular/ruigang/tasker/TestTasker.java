package com.znhb.biz.modular.ruigang.tasker;

import com.znhb.biz.modular.cyft.mapper.CyftCarRecordMapper;
import com.znhb.biz.modular.vehicleRecord.mapper.CarRecordMapper;
import com.znhb.biz.modular.vehicleRecord.service.CarRecordService;
import com.znhb.common.timer.CommonTimerTaskRunner;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class TestTasker implements CommonTimerTaskRunner {

    @Resource
    private CyftCarRecordMapper cyftCarRecordMapper;


    @Override
    public void action() {
        List<Map<String, Object>> maps = cyftCarRecordMapper.cyftQueryForSync();
        log.info(maps.toString());
    }
}
