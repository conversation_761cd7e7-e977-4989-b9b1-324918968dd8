/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package com.znhb.biz.modular.car.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.znhb.biz.modular.car.entity.CarAccessRecord;
import com.znhb.biz.modular.car.param.*;

import java.util.List;

/**
 * 小车进出记录Service接口
 *
 * <AUTHOR>
 * @date  2025/04/10 15:00
 **/
public interface CarAccessRecordService extends IService<CarAccessRecord> {

    /**
     * 获取小车进出记录分页
     *
     * @param param 查询参数
     * @return 分页结果
     */
    Page<CarAccessRecord> page(CarAccessRecordPageParam param);

    /**
     * 添加小车进厂记录
     *
     * @param param 添加参数
     */
    void addEntry(CarAccessRecordAddParam param);

    /**
     * 登记小车出厂
     *
     * @param param 出厂参数
     */
    void registerExit(CarAccessRecordExitParam param);

    /**
     * 编辑小车进出记录
     *
     * @param param 编辑参数
     */
    void edit(CarAccessRecordEditParam param);

    /**
     * 删除小车进出记录
     *
     * @param param 删除参数
     */
    void delete(List<CarAccessRecordIdParam> param);

    /**
     * 获取小车进出记录详情
     *
     * @param param 查询参数
     * @return 小车进出记录详情
     */
    CarAccessRecord detail(CarAccessRecordIdParam param);

    /**
     * 获取未出厂的记录
     *
     * @param plateNumber 车牌号码
     * @return 未出厂的记录
     */
    CarAccessRecord queryLastInRecord(String plateNumber);

    /**
     * 获取当天进入次数
     *
     * @param plateNumber 车牌号码
     * @return 当天进入次数
     */
    long getTodayEntryCount(String plateNumber);

    /**
     * 查询需要同步的车辆进出记录
     *
     * @return 需要同步的车辆进出记录
     */
    List<CarAccessRecord> queryCarAccessRecordForSync();

    /**
     * 更新车辆进出记录为已同步
     *
     * @param id 车辆进出记录ID
     */
    void updateForSync(String id);

}
