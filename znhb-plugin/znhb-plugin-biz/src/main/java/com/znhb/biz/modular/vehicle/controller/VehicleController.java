/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package com.znhb.biz.modular.vehicle.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import com.znhb.biz.modular.vehicle.param.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import com.znhb.common.annotation.CommonLog;
import com.znhb.common.pojo.CommonResult;
import com.znhb.common.pojo.CommonValidList;
import com.znhb.biz.modular.vehicle.entity.Vehicle;
import com.znhb.biz.modular.vehicle.service.VehicleService;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.io.IOException;

/**
 * 车辆信息控制器
 *
 * <AUTHOR>
 * @date  2025/03/13 14:34
 */
@Api(tags = "车辆信息控制器")
@ApiSupport(author = "SNOWY_TEAM", order = 1)
@RestController
@Validated
public class VehicleController {

    @Resource
    private VehicleService vehicleService;

    /**
     * 获取车辆信息分页
     *
     * <AUTHOR>
     * @date  2025/03/13 14:34
     */
    @ApiOperationSupport(order = 1)
    @ApiOperation("获取车辆信息分页")
    @SaCheckPermission("/biz/vehicle/page")
    @GetMapping("/biz/vehicle/page")
    public CommonResult<Page<Vehicle>> page(VehiclePageParam vehiclePageParam) {
        return CommonResult.data(vehicleService.page(vehiclePageParam));
    }

    /**
     * 添加车辆信息
     *
     * <AUTHOR>
     * @date  2025/03/13 14:34
     */
    @ApiOperationSupport(order = 2)
    @ApiOperation("添加车辆信息")
    @CommonLog("添加车辆信息")
    @SaCheckPermission("/biz/vehicle/add")
    @PostMapping("/biz/vehicle/add")
    public CommonResult<String> add(@RequestBody @Valid VehicleAddParam vehicleAddParam) {
        vehicleService.add(vehicleAddParam);
        return CommonResult.ok();
    }

    /**
     * 添加车辆信息h5
     *
     * <AUTHOR>
     * @date  2025/03/13 14:34
     */
    @ApiOperationSupport(order = 2)
    @ApiOperation("移动端添加车辆信息")
    @CommonLog("移动端添加车辆信息")
    @PostMapping("/biz/vehicle/mobileAdd")
    public CommonResult<String> mobileAdd(@RequestBody @Valid VehicleAddParam vehicleAddParam) {
        vehicleService.add(vehicleAddParam);
        return CommonResult.ok();
    }


    /**
     * 编辑车辆信息
     *
     * <AUTHOR>
     * @date  2025/03/13 14:34
     */
    @ApiOperationSupport(order = 3)
    @ApiOperation("编辑车辆信息")
    @CommonLog("编辑车辆信息")
    @SaCheckPermission("/biz/vehicle/edit")
    @PostMapping("/biz/vehicle/edit")
    public CommonResult<String> edit(@RequestBody @Valid VehicleEditParam vehicleEditParam) {
        vehicleService.edit(vehicleEditParam);
        return CommonResult.ok();
    }

    /**
     * 删除车辆信息
     *
     * <AUTHOR>
     * @date  2025/03/13 14:34
     */
    @ApiOperationSupport(order = 4)
    @ApiOperation("删除车辆信息")
    @CommonLog("删除车辆信息")
    @SaCheckPermission("/biz/vehicle/delete")
    @PostMapping("/biz/vehicle/delete")
    public CommonResult<String> delete(@RequestBody @Valid @NotEmpty(message = "集合不能为空")
                                                   CommonValidList<VehicleIdParam> vehicleIdParamList) {
        vehicleService.delete(vehicleIdParamList);
        return CommonResult.ok();
    }

    /**
     * 获取车辆信息详情
     *
     * <AUTHOR>
     * @date  2025/03/13 14:34
     */
    @ApiOperationSupport(order = 5)
    @ApiOperation("获取车辆信息详情")
    @SaCheckPermission("/biz/vehicle/detail")
    @GetMapping("/biz/vehicle/detail")
    public CommonResult<Vehicle> detail(@Valid VehicleIdParam vehicleIdParam) {
        return CommonResult.data(vehicleService.detail(vehicleIdParam));
    }

    /**
     * 车辆审核接口
     * 
     * @param vehicleAuditParam 审核参数
     * @return 结果信息
     */
    @ApiOperationSupport(order = 7)
    @ApiOperation("车辆审核")
    @CommonLog("车辆审核")
    @SaCheckPermission("/biz/vehicle/audit")
    @PostMapping("/biz/vehicle/audit")
    public CommonResult<String> audit(@RequestBody @Valid VehicleAuditParam vehicleAuditParam) {
        vehicleService.audit(vehicleAuditParam);
        return CommonResult.ok();
    }

    /**
     * 导出车辆信息
     *
     * <AUTHOR>
     * @date 2025/03/19
     */
    @ApiOperationSupport(order = 8)
    @ApiOperation("导出车辆信息")
    @CommonLog("导出车辆信息")
    @SaCheckPermission("/biz/vehicle/export")
    @GetMapping(value = "/biz/vehicle/export",produces = MediaType.APPLICATION_OCTET_STREAM_VALUE)
    public void export(VehiclePageParam vehiclePageParam, HttpServletResponse response) throws IOException {
        vehicleService.export(vehiclePageParam, response);
    }

    /**
     * 根据车牌号和车牌颜色查询车辆信息
     *
     * <AUTHOR>
     * @date  2025/03/22 10:05
     */
    @ApiOperationSupport(order = 5)
    @ApiOperation("根据车牌号和车牌颜色查询车辆信息")
    @GetMapping("/biz/vehicle/loadVehicleByPlateNumber")
    public CommonResult<Vehicle> loadVehicleByPlateNumber(@Valid VehicleNumberAndColorParam VehiclePlateNumberParam) {
        return CommonResult.data(vehicleService.loadVehicleByPlateNumber(VehiclePlateNumberParam));
    }

    /**
     * 根据车牌号查询车辆信息
     *
     * <AUTHOR>
     * @date  2025/03/22 10:05
     */
    @ApiOperationSupport(order = 5)
    @ApiOperation("根据车牌号查询车辆信息")
    @GetMapping("/biz/vehicle/loadVehicleForOrder")
    public CommonResult<Object> loadVehicleForOrder(@Valid VehicleNumberParam vehicleNumberParam) {
        return CommonResult.data(vehicleService.loadVehicleForOrder(vehicleNumberParam));
    }
}
