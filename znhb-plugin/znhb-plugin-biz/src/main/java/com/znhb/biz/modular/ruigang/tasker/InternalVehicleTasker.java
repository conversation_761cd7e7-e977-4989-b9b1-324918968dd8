package com.znhb.biz.modular.ruigang.tasker;


import cn.hutool.json.JSONUtil;
import com.znhb.biz.api.SuperLowerApi;
import com.znhb.biz.modular.internalVehicle.entity.InternalVehicle;
import com.znhb.biz.modular.internalVehicle.service.InternalVehicleService;
import com.znhb.common.timer.CommonTimerTaskRunner;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @description: 内部运输车辆同步至超低平台
 */
@Slf4j
@Component
public class InternalVehicleTasker implements CommonTimerTaskRunner {

    @Resource
    private SuperLowerApi superLowerApi;

    @Resource
    private InternalVehicleService internalVehicleService;


    @Override
    public void action() {
        List<InternalVehicle> internalVehicles = internalVehicleService.queryInternalVehicleForSync();
        for (InternalVehicle internalVehicle : internalVehicles) {
            Map<String, Object> map = new HashMap<>();
            map.put("carNo", internalVehicle.getPlateNumber());
            map.put("code", internalVehicle.getEnvRegCode());
            map.put("level", internalVehicle.getEmissionStandard());
            map.put("date", internalVehicle.getProductionDate());
            map.put("carBrand", internalVehicle.getVehicleModel());
            map.put("registerDate", internalVehicle.getRegistrationDate());
            map.put("detailedUrl", internalVehicle.getVehicleListImage());
            map.put("travelUrl", internalVehicle.getDrivingLicenseImage());
            map.put("fuelType", internalVehicle.getFuelType());
            map.put("status", internalVehicle.getNetworkStatus());
            map.put("vin", internalVehicle.getVin());
            map.put("owner", internalVehicle.getOwnerInfo());
            map.put("heavyCarResultUrl", internalVehicle.getHeavyVehicleEmissionStageImgUrl());
            map.put("detailedResultUrl", internalVehicle.getEnvironmentalListElectronicImgUrl());
            log.info("内部车辆备案数据推送：{}", JSONUtil.toJsonStr(map));
            boolean result = superLowerApi.pushInternalVehicle(map);
            if (result) {
                internalVehicleService.updateForSync(internalVehicle.getId());
            }
        }
    }
}

