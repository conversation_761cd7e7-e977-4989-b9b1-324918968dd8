package com.znhb.biz.modular.cyft.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import java.util.Map;

@DS("slave")
public interface GoodsMapper {

    @Select("select * from t_eg_goods_class where matter_type = #{matter_type} and is_delete = '0' limit 1")
    Map<String, Object> getGoodsClassByName(@Param("matter_type") String matter_type);


    @Insert("insert into t_eg_goods_class (id, matter_type, is_qjys, is_delete, can_clean, create_time, type) " +
            "values (#{id}, #{matter_type}, '1', '0,' , '1', now(), '自动导入')")
    void insertGoodsClass(@Param("id") String id, @Param("matter_type") String matter_type);


    @Select("select * from t_eg_goods_detail where material_name = #{material_name} and is_delete = '0' limit 1")
    Map<String, Object> getGoodsDetailByName(@Param("material_name") String material_name);

    /**
     * goodsDetails.put("id", id);
     *                 goodsDetails.put("create_time", DateUtil.formatDateTime(DateUtil.date()));
     *                 goodsDetails.put("material_name", material);
     *                 goodsDetails.put("is_delete", "0");
     *                 goodsDetails.put("is_qjys", goodsClass.get("is_qjys"));
     *                 goodsDetails.put("can_clean", goodsClass.get("can_clean"));
     *                 goodsDetails.put("goods_id", goodsClass.get("id"));
     *                 goodsDetails.put("type", "自动导入");
     */
    @Insert("insert into t_eg_goods_detail (id, create_time, material_name, is_delete, is_qjys, can_clean, goods_id, type) " +
            "values (#{id}, now(), #{material_name}, '0', #{is_qjys}, #{can_clean}, #{goods_id}, '自动导入')")
    void insertGoodsDetail(@Param("id") String id, @Param("material_name") String material_name, @Param("is_qjys") String is_qjys, @Param("can_clean") String can_clean, @Param("goods_id") String goods_id);

}
