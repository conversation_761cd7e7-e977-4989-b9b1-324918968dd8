package com.znhb.biz.modular.ruigang.tasker;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.znhb.biz.api.SuperLowerApi;
import com.znhb.biz.modular.materialdetail.entity.MaterialDetail;
import com.znhb.biz.modular.materialdetail.service.MaterialDetailService;
import com.znhb.common.timer.CommonTimerTaskRunner;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 上报物料至超低平台
 */
@Slf4j
@Component
public class MaterialSyncTasker implements CommonTimerTaskRunner {

    @Resource
    private SuperLowerApi superLowerApi;

    @Resource
    private MaterialDetailService materialDetailService;


    @Override
    public void action() {
        List<MaterialDetail> materialDetails = materialDetailService.queryMaterialForSync();

        for (MaterialDetail materialDetail : materialDetails) {
            Map<String, Object> map = new HashMap<>();
            map.put("goodsName", materialDetail.getName());
            map.put("categoryType", materialDetail.getCategoryName());
            map.put("accessType", StrUtil.isNotBlank(materialDetail.getTransportType()) && StrUtil.equals("1", materialDetail.getTransportType()) ? "0" : "1");
            log.info("物料同步数据：{}", JSONUtil.toJsonStr(map));
            boolean result = superLowerApi.pushMaterial(map);
            if (result) {
                materialDetailService.updateForSync(materialDetail.getId());
            }
        }


    }
}
