package com.znhb.biz.modular.area.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 区域编辑参数
 *
 * <AUTHOR>
 * @date 2025-04-11
 */
@Getter
@Setter
@ApiModel("区域编辑参数")
public class AreaEditParam {

    /** 主键ID */
    @ApiModelProperty(value = "主键ID", required = true)
    @NotBlank(message = "主键ID不能为空")
    private String id;

    /** 区域类型 */
    @ApiModelProperty(value = "区域类型", required = true)
    @NotBlank(message = "区域类型不能为空")
    private String areaType;

    /** 区域名称 */
    @ApiModelProperty(value = "区域名称", required = true)
    @NotBlank(message = "区域名称不能为空")
    private String areaName;

    /** 区域编码 */
    @ApiModelProperty(value = "区域编码")
    private String areaCode;

    /** 区域负责人ID */
    @ApiModelProperty(value = "区域负责人ID")
    private String managerId;

    /** 区域负责人姓名 */
    @ApiModelProperty(value = "区域负责人姓名")
    private String managerName;

    /** 上级区域ID */
    @ApiModelProperty(value = "上级区域ID")
    private String parentId;

    /** 排序码 */
    @ApiModelProperty(value = "排序码")
    private Integer sortCode;

    /** 备注 */
    @ApiModelProperty(value = "备注")
    private String remark;
}
