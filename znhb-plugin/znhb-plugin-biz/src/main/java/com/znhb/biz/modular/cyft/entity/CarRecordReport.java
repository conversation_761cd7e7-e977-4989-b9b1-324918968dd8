/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package com.znhb.biz.modular.cyft.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * CarRecordReport实体
 *
 * <AUTHOR>
 * @date  2025/03/27 08:50
 **/
@Getter
@Setter
@TableName("t_eg_car_record_cyft")
public class CarRecordReport {

    /** ID */
    @TableId
    @ApiModelProperty(value = "ID", position = 1)
    private String id;

    /** IN_AREA_CODE */
    @ApiModelProperty(value = "IN_AREA_CODE", position = 2)
    private String inAreaCode;

    /** OUT_AREA_CODE */
    @ApiModelProperty(value = "OUT_AREA_CODE", position = 3)
    private String outAreaCode;

    /** IN_GATE_CODE */
    @ApiModelProperty(value = "IN_GATE_CODE", position = 4)
    private String inGateCode;

    /** OUT_GATE_CODE */
    @ApiModelProperty(value = "OUT_GATE_CODE", position = 5)
    private String outGateCode;

    /** IN_LIFT_ROD_TYPE */
    @ApiModelProperty(value = "IN_LIFT_ROD_TYPE", position = 6)
    private String inLiftRodType;

    /** OUT_LIFT_ROD_TYPE */
    @ApiModelProperty(value = "OUT_LIFT_ROD_TYPE", position = 7)
    private String outLiftRodType;

    /** IN_TIME */
    @ApiModelProperty(value = "IN_TIME", position = 8)
    private Date inTime;

    /** OUT_TIME */
    @ApiModelProperty(value = "OUT_TIME", position = 9)
    private Date outTime;

    /** IN_CAR_IMG_URL */
    @ApiModelProperty(value = "IN_CAR_IMG_URL", position = 10)
    private String inCarImgUrl;

    /** OUT_CAR_IMG_URL */
    @ApiModelProperty(value = "OUT_CAR_IMG_URL", position = 11)
    private String outCarImgUrl;

    /** IN_LIFT_ROD_IMG_URL */
    @ApiModelProperty(value = "IN_LIFT_ROD_IMG_URL", position = 12)
    private String inLiftRodImgUrl;

    /** OUT_LIFT_ROD_IMG_URL */
    @ApiModelProperty(value = "OUT_LIFT_ROD_IMG_URL", position = 13)
    private String outLiftRodImgUrl;

    /** CAR_NUMBER */
    @ApiModelProperty(value = "CAR_NUMBER", position = 14)
    private String carNumber;

    /** TEAM_NAME */
    @ApiModelProperty(value = "TEAM_NAME", position = 15)
    private String teamName;

    /** IN_GOODS_NAME */
    @ApiModelProperty(value = "IN_GOODS_NAME", position = 16)
    private String inGoodsName;

    /** OUT_GOODS_NAME */
    @ApiModelProperty(value = "OUT_GOODS_NAME", position = 17)
    private String outGoodsName;

    /** CONTRACT_NUMBER */
    @ApiModelProperty(value = "CONTRACT_NUMBER", position = 18)
    private String contractNumber;

    /** BUSINESS_TYPE */
    @ApiModelProperty(value = "BUSINESS_TYPE", position = 19)
    private String businessType;

    /** UNLOADING_LOCATION */
    @ApiModelProperty(value = "UNLOADING_LOCATION", position = 20)
    private String unloadingLocation;

    /** SOURCE */
    @ApiModelProperty(value = "SOURCE", position = 21)
    private String source;

    /** TARGET */
    @ApiModelProperty(value = "TARGET", position = 22)
    private String target;

    /** SUPPLIER */
    @ApiModelProperty(value = "SUPPLIER", position = 23)
    private String supplier;

    /** CUSTOMER */
    @ApiModelProperty(value = "CUSTOMER", position = 24)
    private String customer;

    /** RECEIVER */
    @ApiModelProperty(value = "RECEIVER", position = 25)
    private String receiver;

    /** RECEIVE_TIME */
    @ApiModelProperty(value = "RECEIVE_TIME", position = 26)
    private Date receiveTime;

    /** RECEIVE_GROSS */
    @ApiModelProperty(value = "RECEIVE_GROSS", position = 27)
    private String receiveGross;

    /** RECEIVE_GROSS_MEASURE_SCALE */
    @ApiModelProperty(value = "RECEIVE_GROSS_MEASURE_SCALE", position = 28)
    private String receiveGrossMeasureScale;

    /** RECEIVE_GROSS_MEASURE_POINT */
    @ApiModelProperty(value = "RECEIVE_GROSS_MEASURE_POINT", position = 29)
    private String receiveGrossMeasurePoint;

    /** RECEIVE_GROSS_TIME */
    @ApiModelProperty(value = "RECEIVE_GROSS_TIME", position = 30)
    private Date receiveGrossTime;

    /** RECEIVE_TARE */
    @ApiModelProperty(value = "RECEIVE_TARE", position = 31)
    private String receiveTare;

    /** RECEIVE_TARE_MEASURE_SCALE */
    @ApiModelProperty(value = "RECEIVE_TARE_MEASURE_SCALE", position = 32)
    private String receiveTareMeasureScale;

    /** RECEIVE_TARE_MEASURE_POINT */
    @ApiModelProperty(value = "RECEIVE_TARE_MEASURE_POINT", position = 33)
    private String receiveTareMeasurePoint;

    /** RECEIVE_TARE_TIME */
    @ApiModelProperty(value = "RECEIVE_TARE_TIME", position = 34)
    private Date receiveTareTime;

    /** RECEIVE_SUTTLE */
    @ApiModelProperty(value = "RECEIVE_SUTTLE", position = 35)
    private String receiveSuttle;

    /** RECEIVE_SUTTLE_MEASURE_SCALE */
    @ApiModelProperty(value = "RECEIVE_SUTTLE_MEASURE_SCALE", position = 36)
    private String receiveSuttleMeasureScale;

    /** RECEIVE_SUTTLE_TIME */
    @ApiModelProperty(value = "RECEIVE_SUTTLE_TIME", position = 37)
    private Date receiveSuttleTime;

    /** RECEIVE_SUTTLE_MEASURE_POINT */
    @ApiModelProperty(value = "RECEIVE_SUTTLE_MEASURE_POINT", position = 38)
    private String receiveSuttleMeasurePoint;

    /** DELIVERY_GROSS */
    @ApiModelProperty(value = "DELIVERY_GROSS", position = 39)
    private String deliveryGross;

    /** DELIVERY_GROSS_MEASURE_SCALE */
    @ApiModelProperty(value = "DELIVERY_GROSS_MEASURE_SCALE", position = 40)
    private String deliveryGrossMeasureScale;

    /** DELIVERY_GROSS_MEASURE_POINT */
    @ApiModelProperty(value = "DELIVERY_GROSS_MEASURE_POINT", position = 41)
    private String deliveryGrossMeasurePoint;

    /** DELIVERY_GROSS_TIME */
    @ApiModelProperty(value = "DELIVERY_GROSS_TIME", position = 42)
    private Date deliveryGrossTime;

    /** DELIVERY_TARE */
    @ApiModelProperty(value = "DELIVERY_TARE", position = 43)
    private String deliveryTare;

    /** DELIVERY_TARE_MEASURE_SCALE */
    @ApiModelProperty(value = "DELIVERY_TARE_MEASURE_SCALE", position = 44)
    private String deliveryTareMeasureScale;

    /** DELIVERY_TARE_TIME */
    @ApiModelProperty(value = "DELIVERY_TARE_TIME", position = 45)
    private Date deliveryTareTime;

    /** DELIVERY_TARE_MEASURE_POINT */
    @ApiModelProperty(value = "DELIVERY_TARE_MEASURE_POINT", position = 46)
    private String deliveryTareMeasurePoint;

    /** DELIVERY_SUTTLE */
    @ApiModelProperty(value = "DELIVERY_SUTTLE", position = 47)
    private String deliverySuttle;

    /** DELIVERY_SUTTLE_MEASURE_SCALE */
    @ApiModelProperty(value = "DELIVERY_SUTTLE_MEASURE_SCALE", position = 48)
    private String deliverySuttleMeasureScale;

    /** DELIVERY_SUTTLE_TIME */
    @ApiModelProperty(value = "DELIVERY_SUTTLE_TIME", position = 49)
    private Date deliverySuttleTime;

    /** DELIVERY_SUTTLE_MEASURE_POINT */
    @ApiModelProperty(value = "DELIVERY_SUTTLE_MEASURE_POINT", position = 50)
    private String deliverySuttleMeasurePoint;

    /** SETTLEMENT_AMOUNT */
    @ApiModelProperty(value = "SETTLEMENT_AMOUNT", position = 51)
    private String settlementAmount;

    /** SYNC */
    @ApiModelProperty(value = "SYNC", position = 52)
    private String sync;

    /** IS_DELETE */
    @ApiModelProperty(value = "IS_DELETE", position = 53)
    private String isDelete;

    /** CREATE_TIME */
    @ApiModelProperty(value = "CREATE_TIME", position = 54)
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /** MATERIAL_ID */
    @ApiModelProperty(value = "MATERIAL_ID", position = 55)
    private String materialId;

    /** GOODS_NAME */
    @ApiModelProperty(value = "GOODS_NAME", position = 56)
    private String goodsName;

    /** RECORD_ID */
    @ApiModelProperty(value = "RECORD_ID", position = 57)
    private String recordId;
}
