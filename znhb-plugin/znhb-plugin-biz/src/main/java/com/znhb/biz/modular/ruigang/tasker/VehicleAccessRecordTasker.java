package com.znhb.biz.modular.ruigang.tasker;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.znhb.biz.api.SuperLowerApi;
import com.znhb.biz.core.enums.GateCtrlTypeEnum;
import com.znhb.biz.modular.materialcategory.entity.MaterialCategory;
import com.znhb.biz.modular.materialcategory.service.MaterialCategoryService;
import com.znhb.biz.modular.materialdetail.entity.MaterialDetail;
import com.znhb.biz.modular.materialdetail.service.MaterialDetailService;
import com.znhb.biz.modular.vehicleRecord.entity.CarRecord;
import com.znhb.biz.modular.vehicleRecord.service.CarRecordService;
import com.znhb.biz.modular.device.entity.Device;
import com.znhb.biz.modular.device.service.DeviceService;
import com.znhb.common.timer.CommonTimerTaskRunner;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * 上报外部车辆运输台账至超低平台
 */
@Slf4j
@Component
public class VehicleAccessRecordTasker implements CommonTimerTaskRunner {

    @Resource
    private CarRecordService carRecordService;

    @Resource
    private SuperLowerApi superLowerApi;

    @Resource
    private DeviceService deviceService;

    @Resource
    private MaterialDetailService detailService;

    @Resource
    private MaterialCategoryService categoryService;

    @Override
    public void action() {
        List<CarRecord> carRecords = carRecordService.queryCarRecordForSync();
        for (CarRecord carRecord : carRecords) {
            // 上报车辆运输台账至超低平台
            Map<String, Object> map = new HashMap<>();
            map.put("logistics", carRecord.getId());
            map.put("carNo", carRecord.getPlateNumber());
            map.put("inTime", DateUtil.formatDateTime(carRecord.getInCreateTime()));
            map.put("inAreaCode", carRecord.getInAreaName());
            map.put("inGateCode", carRecord.getInGateName());
            map.put("inDoor", carRecord.getInGateName());
            map.put("inUrl", carRecord.getInCarImgUrl());
            map.put("inTopUrl", carRecord.getInBodyImgUrl());
            if (carRecord.getInCategoryId() != null) {
                MaterialCategory category = categoryService.getById(carRecord.getInCategoryId());
                if (ObjectUtil.isEmpty(category)) {
                    map.put("categoryType", carRecord.getInCategoryName());
                }else{
                    map.put("categoryType", category.getName());
                }

            }
            if (carRecord.getInGoodsId()!= null) {
                MaterialDetail detail = detailService.getById(carRecord.getInGoodsId());
                if (ObjectUtil.isEmpty(detail)) {
                    map.put("goodsName", carRecord.getInGoodsName());
                }else{
                    map.put("goodsName", detail.getName());
                }

            }
            map.put("weight", StrUtil.isEmpty(carRecord.getInWeight()) ? "0" : carRecord.getInWeight());
            map.put("outTime", DateUtil.formatDateTime(carRecord.getOutCreateTime()));
            map.put("outAreaCode", carRecord.getOutAreaName());
            map.put("outGateCode", carRecord.getOutGateName());
            map.put("outDoor", carRecord.getOutGateName());
            map.put("outUrl", carRecord.getOutCarImgUrl());
            map.put("outTopUrl", carRecord.getOutBodyImgUrl());
            if (carRecord.getOutCategoryId()!= null) {
                MaterialCategory category = categoryService.getById(carRecord.getOutCategoryId());
                if (ObjectUtil.isEmpty(category)) {
                    map.put("categoryType", carRecord.getOutCategoryName());
                }else{
                    map.put("outCategoryType", category.getName());
                }

            }
            if (carRecord.getOutGoodsId()!= null) {
                MaterialDetail detail = detailService.getById(carRecord.getOutGoodsId());
                if (ObjectUtil.isEmpty(detail)) {
                    map.put("outGoodsName", carRecord.getOutGoodsName());
                }else{
                    map.put("outGoodsName", detail.getName());
                }
            }
            map.put("outWeight", StrUtil.isEmpty(carRecord.getOutWeight())? "0" : carRecord.getOutWeight());
            map.put("inDoorType", StrUtil.equals(carRecord.getInGateCtrlType(), GateCtrlTypeEnum.AUTO.getCode()) ? 0 : 1);
            map.put("outDoorType", StrUtil.equals(carRecord.getOutGateCtrlType(),GateCtrlTypeEnum.AUTO.getCode()) ? 0 : 1);
            String entryCaptureDeviceId = carRecord.getEntryCaptureDeviceId();
            String exitCaptureDeviceId = carRecord.getExitCaptureDeviceId();
            Device entryDevice = deviceService.getById(entryCaptureDeviceId);
            if (ObjectUtil.isEmpty(entryDevice)) {
                map.put("inCameraIndexCode", "");
            }else{
                map.put("inCameraIndexCode", entryDevice.getCameraIndexCode());
            }
            Device exitDevice = deviceService.getById(exitCaptureDeviceId);
            if (ObjectUtil.isEmpty(exitDevice)) {
                map.put("outCameraIndexCode", "");
            }else{
                map.put("outCameraIndexCode", exitDevice.getCameraIndexCode());
            }

            log.info("上报车辆运输台账至超低平台:{}", JSONUtil.toJsonStr(map));
            boolean result = superLowerApi.pushVehicleAccessRecord(map);
            if (result) {
                carRecordService.updateForSync(carRecord.getId());
            }
        }
    }
}
