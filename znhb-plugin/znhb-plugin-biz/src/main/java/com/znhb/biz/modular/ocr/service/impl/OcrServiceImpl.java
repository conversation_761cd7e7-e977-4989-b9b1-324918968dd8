package com.znhb.biz.modular.ocr.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.huaweicloud.sdk.core.exception.ConnectionException;
import com.huaweicloud.sdk.core.exception.RequestTimeoutException;
import com.huaweicloud.sdk.core.exception.ServiceResponseException;
import com.huaweicloud.sdk.ocr.v1.OcrClient;
import com.huaweicloud.sdk.ocr.v1.model.RecognizeVehicleLicenseRequest;
import com.huaweicloud.sdk.ocr.v1.model.VehicleLicenseRequestBody;
import com.huaweicloud.sdk.ocr.v1.model.VehicleLicenseResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;
import com.znhb.biz.modular.ocr.param.OcrParam;
import com.znhb.biz.modular.ocr.service.OcrService;
import com.znhb.dev.api.DevFileApi;

import javax.annotation.Resource;

@Slf4j
@Service
@ConditionalOnProperty(name = "ocr.hwy.enabled", havingValue = "true")
public class OcrServiceImpl implements OcrService {

    @Resource
    private OcrClient hwyClient;

    @Resource
    private DevFileApi devFileApi;

    @Override
    public VehicleLicenseResult vehicleLicenseSingleSide(OcrParam param) {
        RecognizeVehicleLicenseRequest request = new RecognizeVehicleLicenseRequest();
        VehicleLicenseRequestBody body = new VehicleLicenseRequestBody();
        body.withReturnIssuingAuthority(true);
        body.withReturnTextLocation(false);
        request.withBody(body);
        VehicleLicenseResult result = new VehicleLicenseResult();
        try {
            //首页识别
            //body.withUrl(param.getFrontUrl());
            body.withImage(param.getFrontImage());
            body.withSide(OcrParam.FRONT_SIDE);
            VehicleLicenseResult frontResult = hwyClient.recognizeVehicleLicense(request).getResult();


            //副页识别
            //body.withUrl(param.getBackUrl());
            body.withImage(param.getBackImage());
            body.withSide(OcrParam.BACK_SIDE);
            VehicleLicenseResult backResult = hwyClient.recognizeVehicleLicense(request).getResult();

            //合并数据
            BeanUtil.copyProperties(frontResult, result);
            result.setFileNo(backResult.getFileNo());
            result.setApprovedPassengers(backResult.getApprovedPassengers());
            result.setGrossMass(backResult.getGrossMass());
            result.setUnladenMass(backResult.getUnladenMass());
            result.setApprovedLoad(backResult.getApprovedLoad());
            result.setDimension(backResult.getDimension());
            result.setTractionMass(backResult.getTractionMass());
            result.setRemarks(backResult.getRemarks());
            result.setInspectionRecord(backResult.getInspectionRecord());
            result.setCodeNumber(backResult.getCodeNumber());
            result.setEnergyType(backResult.getEnergyType());
            log.info("首页识别结果：{}", result);

        } catch (ConnectionException | RequestTimeoutException e) {
            log.error(e.toString());
        } catch (ServiceResponseException e) {
            log.error(String.valueOf(e.getHttpStatusCode()));
            log.error(e.getErrorCode());
            log.error(e.getErrorMsg());
        }
        return result;
    }

    @Override
    public VehicleLicenseResult vehicleLicenseDoubleSide(OcrParam param) {
        RecognizeVehicleLicenseRequest request = new RecognizeVehicleLicenseRequest();
        VehicleLicenseRequestBody body = new VehicleLicenseRequestBody();
        body.withReturnIssuingAuthority(true);
        body.withReturnTextLocation(false);
        request.withBody(body);
        VehicleLicenseResult result = null;
        try {
            //双页识别
            //body.withUrl(param.getUrl());
            body.withImage(param.getImage());
            body.withSide(OcrParam.DOUBLE_SIDE);
            result = hwyClient.recognizeVehicleLicense(request).getResult();
            log.info("双页识别结果：{}", result);
        } catch (ConnectionException | RequestTimeoutException e) {
            log.error(e.toString());
        } catch (ServiceResponseException e) {
            log.error(String.valueOf(e.getHttpStatusCode()));
            log.error(e.getErrorCode());
            log.error(e.getErrorMsg());
        }
        return result;
    }
}
