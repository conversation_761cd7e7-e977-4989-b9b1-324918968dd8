/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package com.znhb.biz.modular.barrierGate.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * t_barrier_gate添加参数
 *
 * <AUTHOR>
 * @date  2025/03/21 10:05
 **/
@Getter
@Setter
public class BarrierGateAddParam {

    /** 道闸名称 */
    @ApiModelProperty(value = "道闸名称", position = 2)
    private String name;

    /** 出入方向 */
    @ApiModelProperty(value = "出入方向", position = 3)
    private String direct;

    /** 车牌抓拍机id */
    @ApiModelProperty(value = "车牌抓拍机id", position = 4)
    private String plateCaptureCameraId;

    /** 车牌抓拍机 */
    @ApiModelProperty(value = "车牌抓拍机", position = 5)
    private String plateCaptureCameraName;

    /** 车身抓拍机id */
    @ApiModelProperty(value = "车身抓拍机id", position = 6)
    private String bodyCaptureCameraId;

    /** 车身抓拍机 */
    @ApiModelProperty(value = "车身抓拍机", position = 7)
    private String bodyCaptureCameraName;

    /** 车尾抓拍机id */
    @ApiModelProperty(value = "车尾抓拍机id", position = 8)
    private String tailCaptureCameraId;

    /** 车尾抓拍机 */
    @ApiModelProperty(value = "车尾抓拍机", position = 9)
    private String tailCaptureCameraName;

    /** 显示屏id */
    @ApiModelProperty(value = "显示屏id", position = 10)
    private String ledId;

    /** 显示屏 */
    @ApiModelProperty(value = "显示屏", position = 11)
    private String ledName;

    /** 道闸起杆方式（0系统自动抬杆1非自动抬杆） */
    @ApiModelProperty(value = "道闸起杆方式（0系统自动抬杆1非自动抬杆）", position = 12)
    private String gateAutoOpen;

    /** 是否需要洗车出厂(0不需要洗车1需要洗车) */
    @ApiModelProperty(value = "是否需要洗车出厂(0不需要洗车1需要洗车)", position = 13)
    private String needCarWash;

    /** 排放阶段管控 */
    @ApiModelProperty(value = "排放阶段管控", position = 14)
    private String needStageLimit;

    /** 排放阶段限制 */
    @ApiModelProperty(value = "排放阶段限制", position = 15)
    private String stageLimit;

    /** 道闸状态 */
    @ApiModelProperty(value = "道闸状态", position = 21)
    private String gateStatus;

    /** 所属区域 */
    @ApiModelProperty(value = "所属区域", position = 22)
    private String areaId;

}
