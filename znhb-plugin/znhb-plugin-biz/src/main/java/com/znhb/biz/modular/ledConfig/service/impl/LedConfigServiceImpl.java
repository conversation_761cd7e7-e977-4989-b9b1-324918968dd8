/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package com.znhb.biz.modular.ledConfig.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.znhb.common.enums.CommonSortOrderEnum;
import com.znhb.common.exception.CommonException;
import com.znhb.common.page.CommonPageRequest;
import com.znhb.biz.modular.ledConfig.entity.LedConfig;
import com.znhb.biz.modular.ledConfig.mapper.LedConfigMapper;
import com.znhb.biz.modular.ledConfig.param.LedConfigAddParam;
import com.znhb.biz.modular.ledConfig.param.LedConfigEditParam;
import com.znhb.biz.modular.ledConfig.param.LedConfigIdParam;
import com.znhb.biz.modular.ledConfig.param.LedConfigPageParam;
import com.znhb.biz.modular.ledConfig.service.LedConfigService;

import java.util.List;

/**
 * LED配置信息表Service接口实现类
 *
 * <AUTHOR>
 * @date  2025/05/12 16:23
 **/
@Service
public class LedConfigServiceImpl extends ServiceImpl<LedConfigMapper, LedConfig> implements LedConfigService {

    @Override
    public Page<LedConfig> page(LedConfigPageParam ledConfigPageParam) {
        QueryWrapper<LedConfig> queryWrapper = new QueryWrapper<>();
        if(ObjectUtil.isNotEmpty(ledConfigPageParam.getName())) {
            queryWrapper.lambda().like(LedConfig::getName, ledConfigPageParam.getName());
        }
        if(ObjectUtil.isAllNotEmpty(ledConfigPageParam.getSortField(), ledConfigPageParam.getSortOrder())) {
            CommonSortOrderEnum.validate(ledConfigPageParam.getSortOrder());
            queryWrapper.orderBy(true, ledConfigPageParam.getSortOrder().equals(CommonSortOrderEnum.ASC.getValue()),
                    StrUtil.toUnderlineCase(ledConfigPageParam.getSortField()));
        } else {
            queryWrapper.lambda().orderByAsc(LedConfig::getId);
        }
        return this.page(CommonPageRequest.defaultPage(), queryWrapper);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void add(LedConfigAddParam ledConfigAddParam) {
        LedConfig ledConfig = BeanUtil.toBean(ledConfigAddParam, LedConfig.class);
        this.save(ledConfig);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(LedConfigEditParam ledConfigEditParam) {
        LedConfig ledConfig = this.queryEntity(ledConfigEditParam.getId());
        BeanUtil.copyProperties(ledConfigEditParam, ledConfig);
        this.updateById(ledConfig);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<LedConfigIdParam> ledConfigIdParamList) {
        // 执行删除
        this.removeByIds(CollStreamUtil.toList(ledConfigIdParamList, LedConfigIdParam::getId));
    }

    @Override
    public LedConfig detail(LedConfigIdParam ledConfigIdParam) {
        return this.queryEntity(ledConfigIdParam.getId());
    }

    @Override
    public LedConfig queryEntity(String id) {
        LedConfig ledConfig = this.getById(id);
        if(ObjectUtil.isEmpty(ledConfig)) {
            throw new CommonException("LED配置信息表不存在，id值为：{}", id);
        }
        return ledConfig;
    }

    @Override
    public LedConfig queryByCode(String code) {
        QueryWrapper<LedConfig> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(LedConfig::getCode, code);
        queryWrapper.lambda().last("limit 1");
        return this.getOne(queryWrapper);
    }
}
