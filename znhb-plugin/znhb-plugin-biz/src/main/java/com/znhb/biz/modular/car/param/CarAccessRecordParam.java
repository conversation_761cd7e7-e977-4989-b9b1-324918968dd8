/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package com.znhb.biz.modular.car.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

/**
 * 小车进出记录参数
 *
 * <AUTHOR>
 * @date  2025/04/10 15:00
 **/
@Getter
@Setter
public class CarAccessRecordParam {

    /** id */
    @ApiModelProperty(value = "id")
    private String id;

    /** 小车ID */
    @ApiModelProperty(value = "小车ID")
    private String carId;

    /** 车牌号码 */
    @ApiModelProperty(value = "车牌号码")
    private String plateNumber;

    /** 进厂时间 */
    @ApiModelProperty(value = "进厂时间")
    private Date entryTime;

    /** 进厂时间范围开始 */
    @ApiModelProperty(value = "进厂时间范围开始")
    private String entryTimeStart;

    /** 进厂时间范围结束 */
    @ApiModelProperty(value = "进厂时间范围结束")
    private String entryTimeEnd;

    /** 进厂道闸 */
    @ApiModelProperty(value = "进厂道闸")
    private String entryGate;

    /** 进厂抓拍图片URL */
    @ApiModelProperty(value = "进厂抓拍图片URL")
    private String entryImageUrl;

    /** 出厂时间 */
    @ApiModelProperty(value = "出厂时间")
    private Date exitTime;

    /** 出厂时间范围开始 */
    @ApiModelProperty(value = "出厂时间范围开始")
    private String exitTimeStart;

    /** 出厂时间范围结束 */
    @ApiModelProperty(value = "出厂时间范围结束")
    private String exitTimeEnd;

    /** 出厂道闸 */
    @ApiModelProperty(value = "出厂道闸")
    private String exitGate;

    /** 出厂抓拍图片URL */
    @ApiModelProperty(value = "出厂抓拍图片URL")
    private String exitImageUrl;

    /** 状态(0-未出厂,1-已出厂) */
    @ApiModelProperty(value = "状态(0-未出厂,1-已出厂)")
    private String status;

    /** 停留时长(分钟) */
    @ApiModelProperty(value = "停留时长(分钟)")
    private Integer stayDuration;

    /** 排序字段 */
    @ApiModelProperty(value = "排序字段")
    private String sortField;

    /** 排序方式 */
    @ApiModelProperty(value = "排序方式")
    private String sortOrder;

    /** 批量操作ids */
    @ApiModelProperty(value = "批量操作ids")
    private List<String> ids;

    /** 开闸类型 */
    @ApiModelProperty(value = "入厂开闸类型")
    private String inOpenGateType;

    /** 开闸类型 */
    @ApiModelProperty(value = "出厂开闸类型")
    private String outOpenGateType;

    /** 入厂抓拍设备ID */
    @ApiModelProperty(value = "入厂抓拍设备ID", position = 15)
    private String entryCaptureDeviceId;

    /** 出厂抓拍设备ID */
    @ApiModelProperty(value = "出厂抓拍设备ID", position = 16)
    private String exitCaptureDeviceId;

    /** 入厂道闸id */
    @ApiModelProperty(value = "入厂道闸id", position = 17)
    private String entryGateId;

    /** 出厂道闸id */
    @ApiModelProperty(value = "出厂道闸id", position = 18)
    private String exitGateId;
}
