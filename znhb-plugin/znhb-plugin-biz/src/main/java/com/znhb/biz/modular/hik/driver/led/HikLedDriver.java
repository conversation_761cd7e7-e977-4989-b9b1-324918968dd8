package com.znhb.biz.modular.hik.driver.led;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;

import cn.hutool.extra.spring.SpringUtil;
import com.znhb.biz.core.enums.LedConfigEnum;
import com.znhb.biz.modular.ledConfig.entity.LedConfig;
import com.znhb.biz.modular.ledConfig.service.LedConfigService;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import com.znhb.biz.modular.hik.core.MathUtils;
import com.znhb.biz.modular.hik.driver.DeviceDriver;
import com.znhb.biz.modular.hik.entity.LedCommonConfig;
import com.znhb.biz.modular.hik.entity.LedMessage;

import java.net.Socket;


@EqualsAndHashCode(callSuper = true)
@Slf4j
@Data
public class HikLedDriver extends DeviceDriver {

    private final static String NAME = "海康LED显示屏驱动";

    private String orgName;

    private String ownerName;

    private String ruleName;

    private String vehicleType;


    private static final class Constants {
        static final byte AREA_TYPE_TEXT = 0x0E;
        static final byte AREA_TYPE_VOICE = 0x2D;
        static final int VOICE_INTERVAL = 2000;
        static final byte VOICE_REPEAT_COUNT = 0x02;
        static final int RESERVED_LENGTH = 14;
        static final String CHARSET = "gbk";
    }

    public void displayMessage(LedMessage messages,String code) {
        //通用配置
        LedCommonConfig ledCommonConfig = LedCommonConfig.getInstance();
        //动态配置
        LedConfigService ledConfigService = SpringUtil.getBean(LedConfigService.class);
        LedConfig ledConfig = ledConfigService.queryByCode(code);
        /*
         * 第一行 第二行仅配置颜色
         * 第三行 第四行配置文本和颜色
         */

        if (ObjectUtil.isNotEmpty(ledConfig)) {
            if (ObjectUtil.isNotEmpty(ledConfig.getFirstLineColor())) {
                ledCommonConfig.setLine1Color(ledConfig.getFirstLineColor());
            }
            if (ObjectUtil.isNotEmpty(ledConfig.getSecondLineColor())){
                ledCommonConfig.setLine2Color(ledConfig.getSecondLineColor());
            }
            if (ObjectUtil.isNotEmpty(ledConfig.getThirdLineColor())){
                ledCommonConfig.setLine3Color(ledConfig.getThirdLineColor());
            }
            if (ObjectUtil.isNotEmpty(ledConfig.getFourthLineColor())){
                ledCommonConfig.setLine4Color(ledConfig.getFourthLineColor());
            }
            thirdLineConfig(messages, ledConfig);
            fourthLineConfig(messages, ledConfig);
        }

        long startTime = System.currentTimeMillis();
        try (Socket socket = new Socket(this.getDevice().getIp(), Integer.parseInt(this.getDevice().getPort()))) {
            byte[] message = buildMessage(ledCommonConfig, messages);
            // 发送消息
            socket.getOutputStream().write(message);
            socket.getOutputStream().flush();
            log.info("LED发送消息中...");
            this.setOrgName(null);
            this.setOwnerName(null);
            this.setRuleName(null);
            this.setVehicleType(null);
        } catch (Exception e) {
            log.error("Failed to display message on LED: {}", e.getMessage(), e);
        } finally {
            long executionTime = System.currentTimeMillis() - startTime;
            if (executionTime > 1000) {
                log.info("LED operation took {} ms for device {} (ip={})",
                        executionTime,
                        this.getDeviceName(),
                        this.getDeviceIp());
            }
        }
    }

    private void thirdLineConfig(LedMessage messages, LedConfig ledConfig) {
        String thirdLineType = ledConfig.getThirdLineType();
        if (StrUtil.isNotBlank(thirdLineType)) {
            if (thirdLineType.equals(LedConfigEnum.orgName.getCode())){
                if (StrUtil.isNotEmpty(this.orgName)){
                    messages.setThirdLine(this.orgName);
                }
            }
            if (thirdLineType.equals(LedConfigEnum.vehicleType.getCode())){
                if (StrUtil.isNotEmpty(this.vehicleType)){
                    messages.setThirdLine(vehicleType);
                }
            }
            if (thirdLineType.equals(LedConfigEnum.customText.getCode())){
                if (StrUtil.isNotEmpty(ledConfig.getThirdLineText())){
                    messages.setThirdLine(ledConfig.getThirdLineText());
                }
            }
            if (thirdLineType.equals(LedConfigEnum.dateTime.getCode())){
                messages.setThirdLine(DateUtil.now());
            }
            if (thirdLineType.equals(LedConfigEnum.ruleName.getCode())){
                if (StrUtil.isNotEmpty(this.ruleName)){
                    messages.setThirdLine(ruleName);
                }
            }
            if (thirdLineType.equals(LedConfigEnum.ownerName.getCode())){
                if (StrUtil.isNotEmpty(this.ownerName)){
                    messages.setThirdLine(ownerName);
                }
            }

        }
    }

    private void fourthLineConfig(LedMessage messages, LedConfig ledConfig) {
        String fourthLineType = ledConfig.getFourthLineType();
        if (StrUtil.isNotBlank(fourthLineType)) {
            if (fourthLineType.equals(LedConfigEnum.orgName.getCode())){
                if (StrUtil.isNotEmpty(this.orgName)){
                    messages.setFourthLine(this.orgName);
                }
            }
            if (fourthLineType.equals(LedConfigEnum.vehicleType.getCode())){
                if (StrUtil.isNotEmpty(this.vehicleType)){
                    messages.setFourthLine(vehicleType);
                }
            }
            if (fourthLineType.equals(LedConfigEnum.customText.getCode())){
                if (StrUtil.isNotEmpty(ledConfig.getThirdLineText())){
                    messages.setFourthLine(ledConfig.getThirdLineText());
                }
            }
            if (fourthLineType.equals(LedConfigEnum.dateTime.getCode())){
                messages.setFourthLine(DateUtil.now());
            }
            if (fourthLineType.equals(LedConfigEnum.ruleName.getCode())){
                if (StrUtil.isNotEmpty(this.ruleName)){
                    messages.setFourthLine(ruleName);
                }
            }
            if (fourthLineType.equals(LedConfigEnum.ownerName.getCode())){
                if (StrUtil.isNotEmpty(this.ownerName)){
                    messages.setFourthLine(ownerName);
                }
            }

        }
    }

    private byte[] buildMessage(LedCommonConfig ledCommonConfig, LedMessage message) throws Exception {
        byte[] head = new byte[]{0x55, (byte) 0xaa, 0x00, 0x00};
        byte addr = (byte) 0x01;
        byte[] index = MathUtils.short2byte((short) 0);

        // 获取文本内容
        TextContent content = getTextContent(message);

        // 计算长度
        int total = calculateTotalLength(content);
        byte[] totalBytes = MathUtils.int2byte(total);

        // 构建消息
        byte[] msg = new byte[20 + total + 4];
        int offset = 0;

        // 写入头部信息
        offset = writeMessageHeader(msg, head, addr, (byte) -37, index, totalBytes, offset);

        // 写入数据部分
        offset = writeSubject(msg, offset, content, ledCommonConfig);

        // 写入结尾
        MathUtils.writeByteBuffer(msg, new byte[]{0x00, 0x00, (byte) 0x0d, 0x0a}, offset);

        return msg;
    }

    private static class TextContent {
        final byte[] firstLine;
        final byte[] secondLine;
        final byte[] thirdLine;
        final byte[] fourthLine;
        final byte[] voice;
        final int firstLen;
        final int secondLen;
        final int thirdLen;
        final int fourthLen;
        final int voiceLen;

        TextContent(byte[] firstLine, byte[] secondLine, byte[] thirdLine, byte[] fourthLine, byte[] voice) {
            this.firstLine = firstLine;
            this.secondLine = secondLine;
            this.thirdLine = thirdLine;
            this.fourthLine = fourthLine;
            this.voice = voice;
            this.firstLen = firstLine.length > 0 ? firstLine.length + 26 : 0;
            this.secondLen = secondLine.length > 0 ? secondLine.length + 26 : 0;
            this.thirdLen = thirdLine.length > 0 ? thirdLine.length + 26 : 0;
            this.fourthLen = fourthLine.length > 0 ? fourthLine.length + 26 : 0;
            this.voiceLen = 26 + voice.length;
        }
    }

    private TextContent getTextContent(LedMessage message) throws Exception {
        String firstLine = message.getFirstLine();
        String secondLine = message.getSecondLine();
        String thirdLine = message.getThirdLine();
        String forthLine = message.getFourthLine();
        if (StrUtil.isEmpty(firstLine)) {
            firstLine = "";
        }
        if (StrUtil.isEmpty(secondLine)) {
            secondLine = "";
        }
        if (StrUtil.isEmpty(thirdLine)) {
            thirdLine = "";
        }
        if (StrUtil.isEmpty(forthLine)) {
            forthLine = "";
        }
        String voiceText = firstLine + secondLine + thirdLine + forthLine;

        return new TextContent(
                firstLine.getBytes(Constants.CHARSET),
                secondLine.getBytes(Constants.CHARSET),
                thirdLine.getBytes(Constants.CHARSET),
                forthLine.getBytes(Constants.CHARSET),
                voiceText.getBytes(Constants.CHARSET)
        );
    }

    public static byte parseByte(String str) {
        if (str == null || str.isEmpty()) {
            throw new IllegalArgumentException("Input string cannot be null or empty");
        }
        return Byte.valueOf(str.replace("0x", ""), 16);
    }

    private int writeSubject(byte[] buffer, int offset, TextContent content, LedCommonConfig ledCommonConfig)
            throws Exception {
        validateInputs(content, ledCommonConfig);

        buffer[offset++] = (byte) 1;
        buffer[offset++] = (byte) 1;

        byte[] length = MathUtils.int2byte(calculateTotalLength(content) - 1);
        offset = MathUtils.writeByteBufferMath(buffer, length, offset);

        byte areaCount = calculateAreaCount(content);
        buffer[offset++] = areaCount;
        buffer[offset++] = (byte) 0;

        // 写入预留位
        offset = writeReservedBytes(buffer, offset, Constants.RESERVED_LENGTH);

        // 写入播放次数
        offset = writePlayTimes(buffer, offset, (byte) 30);

        // 写入文本区域
        offset = writeTextAreas(buffer, offset, content, ledCommonConfig);

        // 写入语音区域
        offset = writeVoice(buffer, offset, areaCount, content.voice, content.voiceLen);

        return offset;
    }

    private void validateInputs(TextContent content, LedCommonConfig ledCommonConfig) {
        if (content == null) {
            throw new IllegalArgumentException("TextContent cannot be null");
        }
        if (ledCommonConfig == null) {
            throw new IllegalArgumentException("ledConfig cannot be null");
        }
        // 验证必要的参数是否存在
        validateRequiredParams(ledCommonConfig, content.firstLen > 0, 1);
        validateRequiredParams(ledCommonConfig, content.secondLen > 0, 2);
        validateRequiredParams(ledCommonConfig, content.thirdLen > 0, 3);
        validateRequiredParams(ledCommonConfig, content.fourthLen > 0, 4);
    }

    private void validateRequiredParams(LedCommonConfig config, boolean isRequired, int lineNumber) {
        if (!isRequired) return;
        switch (lineNumber) {
            case 1:
                validateLineParams(config.getLine1X1(), config.getLine1Y1(),
                        config.getLine1X2(), config.getLine1Y2(),
                        config.getLine1Color(), config.getLine1Size(),
                        config.getLine1Action(), config.getLine1Stay(),
                        config.getLine1Speed(), 1);
                break;
            case 2:
                validateLineParams(config.getLine2X1(), config.getLine2Y1(),
                        config.getLine2X2(), config.getLine2Y2(),
                        config.getLine2Color(), config.getLine2Size(),
                        config.getLine2Action(), config.getLine2Stay(),
                        config.getLine2Speed(), 2);
                break;
            case 3:
                validateLineParams(config.getLine3X1(), config.getLine3Y1(),
                        config.getLine3X2(), config.getLine3Y2(),
                        config.getLine3Color(), config.getLine3Size(),
                        config.getLine3Action(), config.getLine3Stay(),
                        config.getLine3Speed(), 3);
            case 4:
                validateLineParams(config.getLine4X1(), config.getLine4Y1(),
                        config.getLine4X2(), config.getLine4Y2(),
                        config.getLine4Color(), config.getLine4Size(),
                        config.getLine4Action(), config.getLine4Stay(),
                        config.getLine4Speed(), 4);
                break;
        }
    }

    private void validateLineParams(Integer x1, Integer y1, Integer x2, Integer y2,
                                    String color, String size, String action, String stay, String speed,
                                    int lineNumber) {
        String errorPrefix = "第" + lineNumber + "行配置缺失：";

        if (x1 == null) throw new IllegalArgumentException(errorPrefix + "x1");
        if (y1 == null) throw new IllegalArgumentException(errorPrefix + "y1");
        if (x2 == null) throw new IllegalArgumentException(errorPrefix + "x2");
        if (y2 == null) throw new IllegalArgumentException(errorPrefix + "y2");
        if (color == null) throw new IllegalArgumentException(errorPrefix + "颜色值");
        if (size == null) throw new IllegalArgumentException(errorPrefix + "字体大小");
        if (action == null) throw new IllegalArgumentException(errorPrefix + "显示动作");
        if (stay == null) throw new IllegalArgumentException(errorPrefix + "停留时间");
        if (speed == null) throw new IllegalArgumentException(errorPrefix + "滚动速度");
    }

    private byte calculateAreaCount(TextContent content) {
        byte count = 1; // 语音区域
        if (content.firstLen > 0) count++;
        if (content.secondLen > 0) count++;
        if (content.thirdLen > 0) count++;
        if (content.fourthLen > 0) count++;
        return count;
    }

    private int writeReservedBytes(byte[] buffer, int offset, int count) {
        for (int i = 0; i < count; i++) {
            buffer[offset++] = (byte) 0;
        }
        return offset;
    }

    private int writePlayTimes(byte[] buffer, int offset, byte times) {
        buffer[offset++] = times;
        buffer[offset++] = (byte) 0;
        buffer[offset++] = (byte) 0;
        return offset;
    }

    private int writeTextAreas(byte[] buffer, int offset, TextContent content, LedCommonConfig config) throws Exception {
        int index = 0;

        if (content.firstLen > 0) {
            offset = writeTextArea(buffer, offset, ++index, content.firstLine, content.firstLen, config, 1);
        }
        if (content.secondLen > 0) {
            offset = writeTextArea(buffer, offset, ++index, content.secondLine, content.secondLen, config, 2);
        }
        if (content.thirdLen > 0) {
            offset = writeTextArea(buffer, offset, ++index, content.thirdLine, content.thirdLen, config, 3);
        }
        if (content.fourthLen > 0) {
            offset = writeTextArea(buffer, offset, ++index, content.fourthLine, content.fourthLen, config, 4);
        }
        return offset;
    }

    private int writeTextArea(byte[] buffer, int offset, int index, byte[] text, int length,
                              LedCommonConfig config, int lineNumber) throws Exception {
        // 根据行号获取配置参数
        int x1, y1, x2, y2;
        byte color, size, action, stay, speed;

        switch (lineNumber) {
            case 1:
                x1 = config.getLine1X1();
                y1 = config.getLine1Y1();
                x2 = config.getLine1X2();
                y2 = config.getLine1Y2();
                color = parseByte(config.getLine1Color());
                size = parseByte(config.getLine1Size());
                action = parseByte(config.getLine1Action());
                stay = parseByte(config.getLine1Stay());
                speed = parseByte(config.getLine1Speed());
                break;
            case 2:
                x1 = config.getLine2X1();
                y1 = config.getLine2Y1();
                x2 = config.getLine2X2();
                y2 = config.getLine2Y2();
                color = parseByte(config.getLine2Color());
                size = parseByte(config.getLine2Size());
                action = parseByte(config.getLine2Action());
                stay = parseByte(config.getLine2Stay());
                speed = parseByte(config.getLine2Speed());
                break;
            case 3:
                x1 = config.getLine3X1();
                y1 = config.getLine3Y1();
                x2 = config.getLine3X2();
                y2 = config.getLine3Y2();
                color = parseByte(config.getLine3Color());
                size = parseByte(config.getLine3Size());
                action = parseByte(config.getLine3Action());
                stay = parseByte(config.getLine3Stay());
                speed = parseByte(config.getLine3Speed());
                break;
            case 4:
                x1 = config.getLine4X1();
                y1 = config.getLine4Y1();
                x2 = config.getLine4X2();
                y2 = config.getLine4Y2();
                color = parseByte(config.getLine4Color());
                size = parseByte(config.getLine4Size());
                action = parseByte(config.getLine4Action());
                stay = parseByte(config.getLine4Stay());
                speed = parseByte(config.getLine4Speed());
                break;
            default:
                throw new IllegalArgumentException("无效的行号: " + lineNumber);
        }

        // 直接使用配置对象中的值，无需字符串转换
        return writeArea(buffer, offset, index, text, x1, y1, x2, y2,
                color, action, size, stay, speed, length);
    }

    private int writeVoice(byte[] buffer, int offset, int no, byte[] text, int area_length) {
        // 区域号
        buffer[offset++] = (byte) no;
        byte[] area_len = MathUtils.int2byte(area_length);

        // 区域数据长度
        offset = MathUtils.writeByteBufferMath(buffer, area_len, offset);

        // 区域数据类型
        buffer[offset++] = Constants.AREA_TYPE_VOICE;

        // 预留位
        buffer[offset++] = 0x00;
        buffer[offset++] = 0x00;
        buffer[offset++] = 0x00;
        buffer[offset++] = 0x00;
        buffer[offset++] = 0x00;
        buffer[offset++] = 0x00;
        buffer[offset++] = 0x00;
        buffer[offset++] = 0x00;
        buffer[offset++] = 0x00;
        buffer[offset++] = 0x00;

        // 播放控制方式
        buffer[offset++] = 0x00;

        // 多次播放间隔毫秒
        offset = MathUtils.writeByteBufferMath(buffer, MathUtils.int2byte(Constants.VOICE_INTERVAL), offset);

        // 播放次数
        buffer[offset++] = Constants.VOICE_REPEAT_COUNT;

        // 字符串长度
        offset = MathUtils.writeByteBufferMath(buffer, MathUtils.int2byte(text.length), offset);

        // 字符串
        offset = MathUtils.writeByteBuffer(buffer, text, offset);
        return offset;
    }

    private int writeArea(byte[] buffer, int offset, int no, byte[] text, int x1, int y1, int x2, int y2, byte color,
                          byte action, byte size, byte stay, byte speed, int area_length) {
        // 区域号
        buffer[offset++] = (byte) no;
        byte[] area_len = MathUtils.int2byte(area_length);

        // 区域数据长度
        offset = MathUtils.writeByteBufferMath(buffer, area_len, offset);

        // 区域数据类型
        buffer[offset++] = Constants.AREA_TYPE_TEXT;

        // 区域坐标
        offset = MathUtils.writeByteBufferMath(buffer, MathUtils.short2byte((short) x1), offset);
        offset = MathUtils.writeByteBufferMath(buffer, MathUtils.short2byte((short) y1), offset);
        offset = MathUtils.writeByteBufferMath(buffer, MathUtils.short2byte((short) x2), offset);
        offset = MathUtils.writeByteBufferMath(buffer, MathUtils.short2byte((short) y2), offset);

        // 字符颜色
        buffer[offset++] = color;

        // 预留位
        buffer[offset++] = 0x00;
        buffer[offset++] = 0x00;

        // 动作方式
        buffer[offset++] = action; // 静止

        // 预留位
        buffer[offset++] = 0x00;

        // 速度
        buffer[offset++] = speed;

        // 停留时间
        buffer[offset++] = stay;

        // 字体大小
        buffer[offset++] = size;

        // 字符串长度
        offset = MathUtils.writeByteBufferMath(buffer, MathUtils.int2byte(text.length), offset);

        // 字符串
        offset = MathUtils.writeByteBuffer(buffer, text, offset);
        return offset;
    }

    private int writeMessageHeader(byte[] buffer, byte[] head, byte addr, byte op, byte[] index, byte[] totalBytes, int offset) {
        offset = MathUtils.writeByteBuffer(buffer, head, offset);
        buffer[offset++] = addr;
        buffer[offset++] = 0x00;
        buffer[offset++] = 0x00;
        buffer[offset++] = op;
        offset = MathUtils.writeByteBuffer(buffer, index, offset);
        offset = MathUtils.writeByteBufferMath(buffer, totalBytes, offset);
        buffer[offset++] = 0x00;
        buffer[offset++] = 0x00;
        offset = MathUtils.writeByteBufferMath(buffer, totalBytes, offset);
        return offset;
    }

    private int calculateTotalLength(TextContent content) {
        return 25 + content.firstLen + content.secondLen + content.thirdLen + content.fourthLen + content.voiceLen;
    }

}
