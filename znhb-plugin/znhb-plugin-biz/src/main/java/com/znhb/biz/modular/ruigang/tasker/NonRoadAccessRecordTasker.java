package com.znhb.biz.modular.ruigang.tasker;


import cn.hutool.json.JSONUtil;
import com.znhb.biz.api.SuperLowerApi;
import com.znhb.biz.modular.nonRoadRecord.entity.NonRoadRecord;
import com.znhb.biz.modular.nonRoadRecord.service.NonRoadRecordService;
import com.znhb.common.timer.CommonTimerTaskRunner;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * @description: 非道路移动机械上传到环保平台
 */
@Slf4j
@Component
public class NonRoadAccessRecordTasker implements CommonTimerTaskRunner {

    @Resource
    private SuperLowerApi superLowerApi;

    @Resource
    private NonRoadRecordService nonRoadRecordService;

    @Override
    public void action() {
        List<NonRoadRecord> nonRoadRecords = nonRoadRecordService.queryNonRoadAccessRecordForSync();
        for (NonRoadRecord nonRoadRecord : nonRoadRecords) {
            Map<String, Object> map = new HashMap<>();
            map.put("accessId", nonRoadRecord.getId());
            map.put("pin", nonRoadRecord.getPinCode());
            map.put("inTime", nonRoadRecord.getInCreateTime());
            map.put("outTime", nonRoadRecord.getOutCreateTime());
            log.info("非道路移动机械台账数据推送：{}", JSONUtil.toJsonStr(map));
            boolean result = superLowerApi.pushNonRoadAccessRecord(map);
            if (result) {
                nonRoadRecordService.updateForSync(nonRoadRecord.getId());
            }
        }

    }
}
