package com.znhb.biz.core.enums;

import lombok.Getter;

@Getter
public enum CommonEnum {

    TEMPORARY_CAR("临时车"),

    NOT_RECORD("车辆未备案"),

    RECORD_NOT_VERIFY("备案未审核"),

    ILLEGAL_EMISSION_STAGE("排放阶段不达标"),

    NORMAL_PASS("正常通行"),

    OPEN_GATE_FAILED("开闸失败"),

    CHECK_VEHICLE("手动开闸，请检查车辆放行"),

    NO_IN_RECORD("无入厂记录"),

    DIRECT_IN("0"),

    DIRECT_OUT("1"),

    NO_ORDER("车辆未派单"),

    NOT_CAR_WASH("车辆未清洗"),

    GOODS_NOT_EXIST("物料不存在"),

    NO_METER_DATA("未获取到计量数据"),

    CAR_STATUS_NOT_ENABLE("车辆已禁用"),

    CAR_EXPIRED("小车有效期已过期"),

    CAR_HAS_ENTRY("车辆已在厂内"),

    CAR_HAS_EXIT("该车辆已出厂"),

    GATE_STATUS_CLOSE("0"),

    GATE_STATUS_OPEN("1"),

    GATE_STATUS_NOT_OPEN("道闸未启用"),

    //通用状态 0禁用  1启用
    STATUS_CLOSE("0"),

    STATUS_OPEN("1"),

    PLATE_NUMBER_NOT_ALLOWED_EMPTY("车牌号码不能为空"),

    CAR_NOT_EXIST("车辆不存在"),


    //led显示数据类型   正常通行0  禁止通行1
    LED_NORMAL("NORMAL_PASS"),

    LED_FORBID("FORBID_PASS");



    private final String codeMessage;

    CommonEnum(String codeMessage) {
        this.codeMessage = codeMessage;
    }

}
