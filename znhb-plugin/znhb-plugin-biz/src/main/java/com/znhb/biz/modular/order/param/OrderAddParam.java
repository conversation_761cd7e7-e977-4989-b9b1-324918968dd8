/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package com.znhb.biz.modular.order.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * 车辆派单表添加参数
 *
 * <AUTHOR>
 * @date  2025/03/20 10:29
 **/
@Getter
@Setter
public class OrderAddParam {

    /** id */
    @ApiModelProperty(value = "id", position = 1)
    private String id;

    /** 车牌号码 */
    @ApiModelProperty(value = "车牌号码", position = 3)
    private String plateNumber;

    /** 车辆备案ID */
    @ApiModelProperty(value = "车辆备案ID", position = 4)
    private String vehicleId;

    /** 物料名称 */
    @ApiModelProperty(value = "物料名称", position = 5)
    private String material;

    /** 物料分类 */
    @ApiModelProperty(value = "物料分类", position = 6)
    private String materialCategory;

    /** 重量 */
    @ApiModelProperty(value = "重量", position = 7)
    private String weight;

    /** 运输发货方 */
    @ApiModelProperty(value = "运输发货方", position = 8)
    private String sources;

    /** 运输收货方 */
    @ApiModelProperty(value = "运输收货方", position = 9)
    private String target;

    /** 是否需要洗车出厂 */
    @ApiModelProperty(value = "是否需要洗车出厂", position = 10)
    private String needCarWash;

    /** 进厂开始时间 */
    @ApiModelProperty(value = "进厂开始时间", position = 11)
    private Date startTime;

    /** 进厂结束时间 */
    @ApiModelProperty(value = "进厂结束时间", position = 12)
    private Date endTime;

    /** 运输台账id */
    @ApiModelProperty(value = "运输台账id", position = 13)
    private String recordId;

    /** 状态（0已派车，1已进厂，2已出厂，3已作废） */
    @ApiModelProperty(value = "状态（0已派车，1已进厂，2已出厂，3已作废）", position = 14)
    private String status;

    /** 派单类型（0单次生效1长期生效） */
    @ApiModelProperty(value = "派单类型（0单次生效1长期生效）", position = 15)
    private String orderType;

    /** 车辆类别（1外部运输2内部运输3非移机械） */
    @ApiModelProperty(value = "车辆类别（1外部运输2内部运输3非移机械）", position = 16)
    private String vehicleCategory;

    /** 计量单位 */
    @ApiModelProperty(value = "计量单位", position = 17)
    private String unit;


    /** 上传单据地址 */
    @ApiModelProperty(value = "上传单据地址", position = 18)
    private String orderImgUrl;

}
