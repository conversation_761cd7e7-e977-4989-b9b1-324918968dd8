/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package com.znhb.biz.modular.device.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import com.znhb.common.annotation.CommonLog;
import com.znhb.common.pojo.CommonResult;
import com.znhb.common.pojo.CommonValidList;
import com.znhb.biz.modular.device.entity.Device;
import com.znhb.biz.modular.device.param.DeviceAddParam;
import com.znhb.biz.modular.device.param.DeviceEditParam;
import com.znhb.biz.modular.device.param.DeviceIdParam;
import com.znhb.biz.modular.device.param.DevicePageParam;
import com.znhb.biz.modular.device.service.DeviceService;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;

/**
 * 设备信息控制器
 *
 * <AUTHOR>
 * @date  2025/03/20 16:10
 */
@Api(tags = "设备信息控制器")
@ApiSupport(author = "SNOWY_TEAM", order = 1)
@RestController
@Validated
public class DeviceController {

    @Resource
    private DeviceService deviceService;

    /**
     * 获取设备信息分页
     *
     * <AUTHOR>
     * @date  2025/03/20 16:10
     */
    @ApiOperationSupport(order = 1)
    @ApiOperation("获取设备信息分页")
    @SaCheckPermission("/biz/device/page")
    @GetMapping("/biz/device/page")
    public CommonResult<Page<Device>> page(DevicePageParam devicePageParam) {
        return CommonResult.data(deviceService.page(devicePageParam));
    }

    /**
     * 添加设备信息
     *
     * <AUTHOR>
     * @date  2025/03/20 16:10
     */
    @ApiOperationSupport(order = 2)
    @ApiOperation("添加设备信息")
    @CommonLog("添加设备信息")
    @SaCheckPermission("/biz/device/add")
    @PostMapping("/biz/device/add")
    public CommonResult<String> add(@RequestBody @Valid DeviceAddParam deviceAddParam) {
        deviceService.add(deviceAddParam);
        return CommonResult.ok();
    }

    /**
     * 编辑设备信息
     *
     * <AUTHOR>
     * @date  2025/03/20 16:10
     */
    @ApiOperationSupport(order = 3)
    @ApiOperation("编辑设备信息")
    @CommonLog("编辑设备信息")
    @SaCheckPermission("/biz/device/edit")
    @PostMapping("/biz/device/edit")
    public CommonResult<String> edit(@RequestBody @Valid DeviceEditParam deviceEditParam) {
        deviceService.edit(deviceEditParam);
        return CommonResult.ok();
    }

    /**
     * 删除设备信息
     *
     * <AUTHOR>
     * @date  2025/03/20 16:10
     */
    @ApiOperationSupport(order = 4)
    @ApiOperation("删除设备信息")
    @CommonLog("删除设备信息")
    @SaCheckPermission("/biz/device/delete")
    @PostMapping("/biz/device/delete")
    public CommonResult<String> delete(@RequestBody @Valid @NotEmpty(message = "集合不能为空")
                                                   CommonValidList<DeviceIdParam> deviceIdParamList) {
        deviceService.delete(deviceIdParamList);
        return CommonResult.ok();
    }

    /**
     * 获取设备信息详情
     *
     * <AUTHOR>
     * @date  2025/03/20 16:10
     */
    @ApiOperationSupport(order = 5)
    @ApiOperation("获取设备信息详情")
    @SaCheckPermission("/biz/device/detail")
    @GetMapping("/biz/device/detail")
    public CommonResult<Device> detail(@Valid DeviceIdParam deviceIdParam) {
        return CommonResult.data(deviceService.detail(deviceIdParam));
    }

    /**
     * 设备登录
     *
     * <AUTHOR>
     * @date  2025/03/20 16:10
     */
    @ApiOperationSupport(order = 6)
    @ApiOperation("设备登录")
    @SaCheckPermission("/biz/device/login")
    @GetMapping("/biz/device/login")
    public CommonResult login(@Valid DeviceIdParam deviceIdParam) {
        return CommonResult.data(deviceService.login(deviceIdParam));
    }


    /**
     * 设备布防
     *
     * <AUTHOR>
     * @date  2025/03/20 16:10
     */
    @ApiOperationSupport(order = 7)
    @ApiOperation("设备布防")
    @SaCheckPermission("/biz/device/defend")
    @PostMapping("/biz/device/defend")
    @CommonLog("设备布防")
    public CommonResult defend(@RequestBody @Valid DeviceIdParam deviceIdParam) {
        return CommonResult.data(deviceService.defend(deviceIdParam));
    }

    /**
     * 获取驱动类
     *
     * <AUTHOR>
     * @date  2025/03/20 16:10
     */
    @ApiOperationSupport(order = 8)
    @ApiOperation("获取驱动类")
    @GetMapping("/biz/device/getDriverClass")
    public CommonResult getDriverClass() {
        return CommonResult.data(deviceService.getDriverClass());
    }

    /**
     * 设备撤防
     *
     * <AUTHOR>
     * @date  2025/03/20 16:10
     */
    @ApiOperationSupport(order = 9)
    @ApiOperation("设备撤防")
    @SaCheckPermission("/biz/device/closeDefend")
    @PostMapping("/biz/device/closeDefend")
    @CommonLog("设备撤防")
    public CommonResult closeDefend(@RequestBody @Valid DeviceIdParam deviceIdParam) {
        return CommonResult.data(deviceService.closeDefend(deviceIdParam));
    }

    /**
     * 获取车牌识别机
     */
    @ApiOperationSupport(order = 9)
    @ApiOperation("获取车牌识别机")
    @GetMapping("/biz/device/getPlateCameraList")
    public CommonResult getPlateCameraList() {
        return CommonResult.data(deviceService.getPlateCameraList());
    }

    /**
     * 获取车身识别机
     */
    @ApiOperationSupport(order = 9)
    @ApiOperation("获取车身识别机")
    @GetMapping("/biz/device/getBodyCameraList")
    public CommonResult getBodyCameraList() {
        return CommonResult.data(deviceService.getBodyCameraList());
    }

    /**
     * 获取车尾识别机
     */
    @ApiOperationSupport(order = 9)
    @ApiOperation("获取车尾识别机")
    @GetMapping("/biz/device/getTailCameraList")
    public CommonResult getTailCameraList() {
        return CommonResult.data(deviceService.getTailCameraList());
    }

    /**
     * 获取LED显示屏
     */
    @ApiOperationSupport(order = 9)
    @ApiOperation("获取车尾识别机")
    @GetMapping("/biz/device/getLedList")
    public CommonResult getLedList() {
        return CommonResult.data(deviceService.getLedList());
    }


    /**
     * 获取ISC摄像头
     */
    @ApiOperationSupport(order = 10)
    @ApiOperation("获取ISC摄像头")
    @GetMapping("/biz/device/getIscList")
    public CommonResult getIscCameraList() {
        return CommonResult.data(deviceService.getIscCameraList());
    }





}
