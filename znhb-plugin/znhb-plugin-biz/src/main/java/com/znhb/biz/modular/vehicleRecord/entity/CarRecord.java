/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package com.znhb.biz.modular.vehicleRecord.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.znhb.common.pojo.CommonEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * 运输台账实体
 *
 * <AUTHOR>
 * @date  2025/03/22 10:05
 **/
@Getter
@Setter
@TableName("t_vehicle_record")

public class CarRecord extends CommonEntity {

    /** id */
    @TableId
    @ApiModelProperty(value = "id", position = 1)
    private String id;

    /** 车辆备案id */
    @ApiModelProperty(value = "车辆备案id", position = 2)
    private String vehicleId;

    /** 车牌号码 */
    @ApiModelProperty(value = "车牌号码", position = 3)
    private String plateNumber;

    /** 车牌颜色 */
    @ApiModelProperty(value = "车牌颜色", position = 4)
    private String plateColor;

    /** 车辆类别 */
    @ApiModelProperty(value = "车辆类别", position = 5)
    private String vehicleCategory;

    /** 运单号 */
    @ApiModelProperty(value = "运单号", position = 6)
    private String inOrderNumber;

    /** 运输货物 */
    @ApiModelProperty(value = "运输货物", position = 7)
    private String inGoodsName;

    /** 运输货物id */
    @ApiModelProperty(value = "运输货物id", position = 8)
    private String inGoodsId;

    /** 物料类型id */
    @ApiModelProperty(value = "物料类型id", position = 9)
    private String inCategoryId;

    /** 物料类型 */
    @ApiModelProperty(value = "物料类型", position = 10)
    private String inCategoryName;

    /** 运输重量 */
    @ApiModelProperty(value = "运输重量", position = 11)
    private String inWeight;

    /** 洗车状态 */
    @ApiModelProperty(value = "洗车状态", position = 12)
    private String carWashStatus;

    /** 进厂道闸id */
    @ApiModelProperty(value = "进厂道闸id", position = 13)
    private String inGateId;

    /** 进厂道闸 */
    @ApiModelProperty(value = "进厂道闸", position = 14)
    private String inGateName;

    /** 进厂时间 */
    @ApiModelProperty(value = "进厂时间", position = 15)
    private Date inCreateTime;

    /** 进厂小车牌照 */
    @ApiModelProperty(value = "进厂小车牌照", position = 16)
    private String inPlateImgUrl;

    /** 进厂大车牌照 */
    @ApiModelProperty(value = "进厂大车牌照", position = 17)
    private String inCarImgUrl;

    /** 进厂车身照片 */
    @ApiModelProperty(value = "进厂车身照片", position = 18)
    private String inBodyImgUrl;

    /** 进厂车尾照片 */
    @ApiModelProperty(value = "进厂车尾照片", position = 19)
    private String inTailImgUrl;

    /** 出场道闸id */
    @ApiModelProperty(value = "出场道闸id", position = 20)
    private String outGateId;

    /** 出场道闸 */
    @ApiModelProperty(value = "出场道闸", position = 21)
    private String outGateName;

    /** 出厂时间 */
    @ApiModelProperty(value = "出厂时间", position = 22)
    private Date outCreateTime;

    /** 出厂小车牌照 */
    @ApiModelProperty(value = "出厂小车牌照", position = 23)
    private String outPlateImgUrl;

    /** 出厂大车牌照 */
    @ApiModelProperty(value = "出厂大车牌照", position = 24)
    private String outCarImgUrl;

    /** 出厂车身照片 */
    @ApiModelProperty(value = "出厂车身照片", position = 25)
    private String outBodyImgUrl;

    /** 出厂车尾照片 */
    @ApiModelProperty(value = "出厂车尾照片", position = 26)
    private String outTailImgUrl;

    /** 同步状态 */
    @ApiModelProperty(value = "同步状态", position = 32)
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String syncStatus;

    /** 出场状态 */
    @ApiModelProperty(value = "出场状态", position = 33)
    private String outStatus;

    /** 运单号 */
    @ApiModelProperty(value = "运单号", position = 6)
    private String outOrderNumber;

    /** 运输货物 */
    @ApiModelProperty(value = "运输货物", position = 7)
    private String outGoodsName;

    /** 运输货物id */
    @ApiModelProperty(value = "运输货物id", position = 8)
    private String outGoodsId;

    /** 物料类型id */
    @ApiModelProperty(value = "物料类型id", position = 9)
    private String outCategoryId;

    /** 物料类型 */
    @ApiModelProperty(value = "物料类型", position = 10)
    private String outCategoryName;

    /** 运输重量 */
    @ApiModelProperty(value = "运输重量", position = 11)
    private String outWeight;

    /** 运输重量 */
    @ApiModelProperty(value = "重量单位", position = 11)
    private String inGoodsUnit;

    /** 运输重量 */
    @ApiModelProperty(value = "重量单位", position = 11)
    private String outGoodsUnit;

    /** 入厂区域 */
    @ApiModelProperty(value = "入厂区域", position = 12)
    private String inAreaId;

    /** 入厂区域 */
    @ApiModelProperty(value = "入厂区域", position = 13)
    private String inAreaName;

    /** 出厂区域 */
    @ApiModelProperty(value = "出厂区域", position = 14)
    private String outAreaId;

    /** 出厂区域 */
    @ApiModelProperty(value = "出厂区域", position = 15)
    private String outAreaName;

    /** 进厂开闸方式 */
    private String inGateCtrlType;

    /** 出厂开闸方式 */
    private String outGateCtrlType;

    /** 进场抓拍设备id */
    private String entryCaptureDeviceId;

    /** 出场抓拍设备id */
    private String exitCaptureDeviceId;

    /** 进场数据录入类型 */
    private String inRecordType;

    /** 出场数据录入类型 */
    private String outRecordType;

    /** 备注信息 */
    private String remark;
}
