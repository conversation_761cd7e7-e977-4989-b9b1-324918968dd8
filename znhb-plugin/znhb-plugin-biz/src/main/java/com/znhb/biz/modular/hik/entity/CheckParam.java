package com.znhb.biz.modular.hik.entity;

import com.znhb.biz.modular.car.entity.CarAccessRule;
import com.znhb.biz.modular.order.entity.Order;
import lombok.Data;

@Data
public class CheckParam {

    private Boolean result;

    private String message;

    private String plateNumber;

    //入厂
    private String entryOrderNumber;

    private String entryGoodsName;

    private String entryGoodsId;

    private String entryGoodsCategoryId;

    private String entryGoodsCategoryName;

    private String entryGoodsUnit;

    //出厂
    private String exitOrderNumber;

    private String exitGoodsName;

    private String exitGoodsId;

    private String exitGoodsCategoryId;

    private String exitGoodsCategoryName;

    private String exitGoodsUnit;

    private Boolean autoOpen;

    private Order order;

}
