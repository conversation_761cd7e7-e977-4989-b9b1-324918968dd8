/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package com.znhb.biz.modular.nonRoadMachinery.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.znhb.biz.core.enums.SyncStatusEnum;
import com.znhb.biz.modular.car.entity.CarInfo;
import com.znhb.biz.modular.car.service.CarInfoService;
import com.znhb.biz.modular.internalVehicle.entity.InternalVehicle;
import com.znhb.biz.modular.internalVehicle.service.InternalVehicleService;
import com.znhb.biz.modular.vehicle.entity.Vehicle;
import com.znhb.biz.modular.vehicle.service.VehicleService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.znhb.common.enums.CommonSortOrderEnum;
import com.znhb.common.exception.CommonException;
import com.znhb.common.page.CommonPageRequest;
import com.znhb.biz.modular.nonRoadMachinery.entity.NonRoadMachinery;
import com.znhb.biz.modular.nonRoadMachinery.mapper.NonRoadMachineryMapper;
import com.znhb.biz.modular.nonRoadMachinery.param.NonRoadMachineryAddParam;
import com.znhb.biz.modular.nonRoadMachinery.param.NonRoadMachineryEditParam;
import com.znhb.biz.modular.nonRoadMachinery.param.NonRoadMachineryIdParam;
import com.znhb.biz.modular.nonRoadMachinery.param.NonRoadMachineryPageParam;
import com.znhb.biz.modular.nonRoadMachinery.service.NonRoadMachineryService;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * 非道路移动机械信息表Service接口实现类
 *
 * <AUTHOR>
 * @date 2025/04/03 10:31
 **/
@Service
public class NonRoadMachineryServiceImpl extends ServiceImpl<NonRoadMachineryMapper, NonRoadMachinery> implements NonRoadMachineryService {


    @Resource
    private InternalVehicleService internalVehicleService;

    @Resource
    private VehicleService vehicleService;

    @Resource
    private CarInfoService carInfoService;

    @Override
    public Page<NonRoadMachinery> page(NonRoadMachineryPageParam nonRoadMachineryPageParam) {
        QueryWrapper<NonRoadMachinery> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotEmpty(nonRoadMachineryPageParam.getEnvRegCode())) {
            queryWrapper.lambda().like(NonRoadMachinery::getEnvRegCode, nonRoadMachineryPageParam.getEnvRegCode());
        }
        if (ObjectUtil.isNotEmpty(nonRoadMachineryPageParam.getPlateNumber())) {
            queryWrapper.lambda().like(NonRoadMachinery::getPlateNumber, nonRoadMachineryPageParam.getPlateNumber());
        }
        if (ObjectUtil.isNotEmpty(nonRoadMachineryPageParam.getFuelType())) {
            queryWrapper.lambda().eq(NonRoadMachinery::getFuelType, nonRoadMachineryPageParam.getFuelType());
        }
        if (ObjectUtil.isNotEmpty(nonRoadMachineryPageParam.getMachineryType())) {
            queryWrapper.lambda().eq(NonRoadMachinery::getMachineryType, nonRoadMachineryPageParam.getMachineryType());
        }
        if (ObjectUtil.isAllNotEmpty(nonRoadMachineryPageParam.getSortField(), nonRoadMachineryPageParam.getSortOrder())) {
            CommonSortOrderEnum.validate(nonRoadMachineryPageParam.getSortOrder());
            queryWrapper.orderBy(true, nonRoadMachineryPageParam.getSortOrder().equals(CommonSortOrderEnum.ASC.getValue()),
                    StrUtil.toUnderlineCase(nonRoadMachineryPageParam.getSortField()));
        } else {
            queryWrapper.lambda().orderByAsc(NonRoadMachinery::getId);
        }
        return this.page(CommonPageRequest.defaultPage(), queryWrapper);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void add(NonRoadMachineryAddParam nonRoadMachineryAddParam) {
        NonRoadMachinery nonRoadMachinery = BeanUtil.toBean(nonRoadMachineryAddParam, NonRoadMachinery.class);
        checkVehicle(nonRoadMachinery.getPlateNumber());
        this.save(nonRoadMachinery);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(NonRoadMachineryEditParam nonRoadMachineryEditParam) {
        NonRoadMachinery nonRoadMachinery = this.queryEntity(nonRoadMachineryEditParam.getId());
        BeanUtil.copyProperties(nonRoadMachineryEditParam, nonRoadMachinery);
        this.updateById(nonRoadMachinery);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<NonRoadMachineryIdParam> nonRoadMachineryIdParamList) {
        // 执行删除
        this.removeByIds(CollStreamUtil.toList(nonRoadMachineryIdParamList, NonRoadMachineryIdParam::getId));
    }

    @Override
    public NonRoadMachinery detail(NonRoadMachineryIdParam nonRoadMachineryIdParam) {
        return this.queryEntity(nonRoadMachineryIdParam.getId());
    }

    @Override
    public NonRoadMachinery queryEntity(String id) {
        NonRoadMachinery nonRoadMachinery = this.getById(id);
        if (ObjectUtil.isEmpty(nonRoadMachinery)) {
            throw new CommonException("非道路移动机械信息表不存在，id值为：{}", id);
        }
        return nonRoadMachinery;
    }

    @Override
    public NonRoadMachinery queryNumberEntity(String plateNumber) {
        QueryWrapper<NonRoadMachinery> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(NonRoadMachinery::getPlateNumber, plateNumber);
        return this.getOne(queryWrapper);
    }

    @Override
    public List<NonRoadMachinery> queryNonRoadMachineryForSync() {
        QueryWrapper<NonRoadMachinery> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(NonRoadMachinery::getSyncStatus, SyncStatusEnum.NOT_SYNC.getCode());
        queryWrapper.lambda().between(NonRoadMachinery::getUpdateTime, DateUtil.beginOfDay(DateUtil.parseDateTime(DateUtil.now())), DateUtil.endOfDay(DateUtil.parseDateTime(DateUtil.now())));
        return this.list(queryWrapper);
    }

    @Override
    public void updateForSync(String id) {
        UpdateWrapper<NonRoadMachinery> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda().eq(NonRoadMachinery::getId, id);
        updateWrapper.lambda().set(NonRoadMachinery::getSyncStatus, SyncStatusEnum.SYNCED.getCode());
        this.update(updateWrapper);
    }

    private void checkVehicle(String plateNumber) {
        InternalVehicle internalVehicle = internalVehicleService.queryNumberEntity(plateNumber);
        if (ObjectUtil.isNotEmpty(internalVehicle)) {
            throw new CommonException("该车辆已备案为内部车辆");
        }
        Vehicle vehicle = vehicleService.queryNumberEntity(plateNumber);
        if (ObjectUtil.isNotEmpty(vehicle)) {
            throw new CommonException("该车辆已备案为外部车辆");
        }
        CarInfo carInfo = carInfoService.queryByPlateNumber(plateNumber);
        if (ObjectUtil.isNotEmpty(carInfo)) {
            throw new CommonException("该车辆已备案为小车车辆");
        }
    }
}
