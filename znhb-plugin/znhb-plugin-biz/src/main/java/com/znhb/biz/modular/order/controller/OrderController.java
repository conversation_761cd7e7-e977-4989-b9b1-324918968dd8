/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package com.znhb.biz.modular.order.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import com.znhb.common.annotation.CommonLog;
import com.znhb.common.pojo.CommonResult;
import com.znhb.common.pojo.CommonValidList;
import com.znhb.biz.modular.order.entity.Order;
import com.znhb.biz.modular.order.param.OrderAddParam;
import com.znhb.biz.modular.order.param.OrderEditParam;
import com.znhb.biz.modular.order.param.OrderIdParam;
import com.znhb.biz.modular.order.param.OrderPageParam;
import com.znhb.biz.modular.order.service.OrderService;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;

/**
 * 车辆派单表控制器
 *
 * <AUTHOR>
 * @date  2025/03/20 10:29
 */
@Api(tags = "车辆派单表控制器")
@ApiSupport(author = "SNOWY_TEAM", order = 1)
@RestController
@Validated
public class OrderController {

    @Resource
    private OrderService orderService;

    /**
     * 获取车辆派单表分页
     *
     * <AUTHOR>
     * @date  2025/03/20 10:29
     */
    @ApiOperationSupport(order = 1)
    @ApiOperation("获取车辆派单表分页")
    @SaCheckPermission("/biz/order/page")
    @GetMapping("/biz/order/page")
    public CommonResult<Page<Order>> page(OrderPageParam orderPageParam) {
        return CommonResult.data(orderService.page(orderPageParam));
    }

    /**
     * 添加车辆派单表
     *
     * <AUTHOR>
     * @date  2025/03/20 10:29
     */
    @ApiOperationSupport(order = 2)
    @ApiOperation("添加车辆派单表")
    @CommonLog("添加车辆派单表")
    @SaCheckPermission("/biz/order/add")
    @PostMapping("/biz/order/add")
    public CommonResult<String> add(@RequestBody @Valid OrderAddParam orderAddParam) {
        orderService.add(orderAddParam);
        return CommonResult.ok();
    }

    /**
     * 编辑车辆派单表
     *
     * <AUTHOR>
     * @date  2025/03/20 10:29
     */
    @ApiOperationSupport(order = 3)
    @ApiOperation("编辑车辆派单表")
    @CommonLog("编辑车辆派单表")
    @SaCheckPermission("/biz/order/edit")
    @PostMapping("/biz/order/edit")
    public CommonResult<String> edit(@RequestBody @Valid OrderEditParam orderEditParam) {
        orderService.edit(orderEditParam);
        return CommonResult.ok();
    }

    /**
     * 删除车辆派单表
     *
     * <AUTHOR>
     * @date  2025/03/20 10:29
     */
    @ApiOperationSupport(order = 4)
    @ApiOperation("删除车辆派单表")
    @CommonLog("删除车辆派单表")
    @SaCheckPermission("/biz/order/delete")
    @PostMapping("/biz/order/delete")
    public CommonResult<String> delete(@RequestBody @Valid @NotEmpty(message = "集合不能为空")
                                                   CommonValidList<OrderIdParam> orderIdParamList) {
        orderService.delete(orderIdParamList);
        return CommonResult.ok();
    }

    /**
     * 获取车辆派单表详情
     *
     * <AUTHOR>
     * @date  2025/03/20 10:29
     */
    @ApiOperationSupport(order = 5)
    @ApiOperation("获取车辆派单表详情")
    @SaCheckPermission("/biz/order/detail")
    @GetMapping("/biz/order/detail")
    public CommonResult<Order> detail(@Valid OrderIdParam orderIdParam) {
        return CommonResult.data(orderService.detail(orderIdParam));
    }
}
