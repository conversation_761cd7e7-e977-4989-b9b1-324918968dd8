/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package com.znhb.biz.modular.nonRoadMachinery.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import com.znhb.biz.modular.internalVehicle.entity.InternalVehicle;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import com.znhb.common.annotation.CommonLog;
import com.znhb.common.pojo.CommonResult;
import com.znhb.common.pojo.CommonValidList;
import com.znhb.biz.modular.nonRoadMachinery.entity.NonRoadMachinery;
import com.znhb.biz.modular.nonRoadMachinery.param.NonRoadMachineryAddParam;
import com.znhb.biz.modular.nonRoadMachinery.param.NonRoadMachineryEditParam;
import com.znhb.biz.modular.nonRoadMachinery.param.NonRoadMachineryIdParam;
import com.znhb.biz.modular.nonRoadMachinery.param.NonRoadMachineryPageParam;
import com.znhb.biz.modular.nonRoadMachinery.service.NonRoadMachineryService;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;

/**
 * 非道路移动机械信息表控制器
 *
 * <AUTHOR>
 * @date  2025/04/03 10:31
 */
@Api(tags = "非道路移动机械信息表控制器")
@ApiSupport(author = "SNOWY_TEAM", order = 1)
@RestController
@Validated
public class NonRoadMachineryController {

    @Resource
    private NonRoadMachineryService nonRoadMachineryService;

    /**
     * 获取非道路移动机械信息表分页
     *
     * <AUTHOR>
     * @date  2025/04/03 10:31
     */
    @ApiOperationSupport(order = 1)
    @ApiOperation("获取非道路移动机械信息表分页")
    @SaCheckPermission("/biz/nonRoadMachinery/page")
    @GetMapping("/biz/nonRoadMachinery/page")
    public CommonResult<Page<NonRoadMachinery>> page(NonRoadMachineryPageParam nonRoadMachineryPageParam) {
        return CommonResult.data(nonRoadMachineryService.page(nonRoadMachineryPageParam));
    }

    /**
     * 添加非道路移动机械信息表
     *
     * <AUTHOR>
     * @date  2025/04/03 10:31
     */
    @ApiOperationSupport(order = 2)
    @ApiOperation("添加非道路移动机械信息表")
    @CommonLog("添加非道路移动机械信息表")
    @SaCheckPermission("/biz/nonRoadMachinery/add")
    @PostMapping("/biz/nonRoadMachinery/add")
    public CommonResult<String> add(@RequestBody @Valid NonRoadMachineryAddParam nonRoadMachineryAddParam) {
        nonRoadMachineryService.add(nonRoadMachineryAddParam);
        return CommonResult.ok();
    }

    /**
     * 编辑非道路移动机械信息表
     *
     * <AUTHOR>
     * @date  2025/04/03 10:31
     */
    @ApiOperationSupport(order = 3)
    @ApiOperation("编辑非道路移动机械信息表")
    @CommonLog("编辑非道路移动机械信息表")
    @SaCheckPermission("/biz/nonRoadMachinery/edit")
    @PostMapping("/biz/nonRoadMachinery/edit")
    public CommonResult<String> edit(@RequestBody @Valid NonRoadMachineryEditParam nonRoadMachineryEditParam) {
        nonRoadMachineryService.edit(nonRoadMachineryEditParam);
        return CommonResult.ok();
    }

    /**
     * 删除非道路移动机械信息表
     *
     * <AUTHOR>
     * @date  2025/04/03 10:31
     */
    @ApiOperationSupport(order = 4)
    @ApiOperation("删除非道路移动机械信息表")
    @CommonLog("删除非道路移动机械信息表")
    @SaCheckPermission("/biz/nonRoadMachinery/delete")
    @PostMapping("/biz/nonRoadMachinery/delete")
    public CommonResult<String> delete(@RequestBody @Valid @NotEmpty(message = "集合不能为空")
                                                   CommonValidList<NonRoadMachineryIdParam> nonRoadMachineryIdParamList) {
        nonRoadMachineryService.delete(nonRoadMachineryIdParamList);
        return CommonResult.ok();
    }

    /**
     * 获取非道路移动机械信息表详情
     *
     * <AUTHOR>
     * @date  2025/04/03 10:31
     */
    @ApiOperationSupport(order = 5)
    @ApiOperation("获取非道路移动机械信息表详情")
    @SaCheckPermission("/biz/nonRoadMachinery/detail")
    @GetMapping("/biz/nonRoadMachinery/detail")
    public CommonResult<NonRoadMachinery> detail(@Valid NonRoadMachineryIdParam nonRoadMachineryIdParam) {
        return CommonResult.data(nonRoadMachineryService.detail(nonRoadMachineryIdParam));
    }

    /**
     * 根据车牌号码查询非道路移动机械详情
     *
     * <AUTHOR>
     * @date  2025/04/03 09:24
     */
    @ApiOperationSupport(order = 6)
    @ApiOperation("根据车牌号码查询非道路移动机械详情")
    @SaCheckPermission("/biz/nonRoadMachinery/detailByPlateNumber")
    @GetMapping("/biz/nonRoadMachinery/detailByPlateNumber")
    public CommonResult<NonRoadMachinery> detailByPlateNumber(String plateNumber) {
        return CommonResult.data(nonRoadMachineryService.queryNumberEntity(plateNumber));
    }
}
