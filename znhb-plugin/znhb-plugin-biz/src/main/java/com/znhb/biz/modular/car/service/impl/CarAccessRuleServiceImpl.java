/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package com.znhb.biz.modular.car.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.znhb.biz.modular.car.entity.CarAccessRule;
import com.znhb.biz.modular.car.mapper.CarAccessRuleMapper;
import com.znhb.biz.modular.car.param.CarAccessRuleAddParam;
import com.znhb.biz.modular.car.param.CarAccessRuleEditParam;
import com.znhb.biz.modular.car.param.CarAccessRuleIdParam;
import com.znhb.biz.modular.car.param.CarAccessRulePageParam;
import com.znhb.biz.modular.car.service.CarAccessRuleService;
import com.znhb.common.enums.CommonSortOrderEnum;
import com.znhb.common.exception.CommonException;
import com.znhb.common.page.CommonPageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 小车进出规则Service接口实现类
 *
 * <AUTHOR>
 * @date  2025/04/10 15:00
 **/
@Service
public class CarAccessRuleServiceImpl extends ServiceImpl<CarAccessRuleMapper, CarAccessRule> implements CarAccessRuleService {

    @Override
    public Page<CarAccessRule> page(CarAccessRulePageParam carAccessRulePageParam) {
        QueryWrapper<CarAccessRule> queryWrapper = new QueryWrapper<>();
        if(ObjectUtil.isNotEmpty(carAccessRulePageParam.getRuleName())) {
            queryWrapper.lambda().like(CarAccessRule::getRuleName, carAccessRulePageParam.getRuleName());
        }
        if(ObjectUtil.isNotEmpty(carAccessRulePageParam.getStatus())) {
            queryWrapper.lambda().eq(CarAccessRule::getStatus, carAccessRulePageParam.getStatus());
        }
        if(ObjectUtil.isAllNotEmpty(carAccessRulePageParam.getSortField(), carAccessRulePageParam.getSortOrder())) {
            queryWrapper.orderBy(true, carAccessRulePageParam.getSortOrder().equals(CommonSortOrderEnum.ASC.getValue()),
                    StrUtil.toUnderlineCase(carAccessRulePageParam.getSortField()));
        } else {
            queryWrapper.lambda().orderByAsc(CarAccessRule::getPriority);
        }
        return this.page(CommonPageRequest.defaultPage(), queryWrapper);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void add(CarAccessRuleAddParam carAccessRuleAddParam) {
        CarAccessRule carAccessRule = BeanUtil.toBean(carAccessRuleAddParam, CarAccessRule.class);
        this.save(carAccessRule);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(CarAccessRuleEditParam carAccessRuleEditParam) {
        CarAccessRule carAccessRule = this.queryEntity(carAccessRuleEditParam.getId());
        // 显式处理需要清空的字段
        if (!carAccessRuleEditParam.isEnableTimeRule()) {
            carAccessRuleEditParam.setStartTime(null);
            carAccessRuleEditParam.setEndTime(null);
        }

        if (!carAccessRuleEditParam.isEnableCountRule()) {
            carAccessRuleEditParam.setMaxCount(null);
        }

        if (!carAccessRuleEditParam.isEnableInGateRule()) {
            carAccessRuleEditParam.setAllowedInGates(null);
        }

        if (!carAccessRuleEditParam.isEnableOutGateRule()) {
            carAccessRuleEditParam.setAllowedOutGates(null);
        }

        if (!carAccessRuleEditParam.isEnableDurationRule()) {
            carAccessRuleEditParam.setMaxDuration(null);
        }
        BeanUtil.copyProperties(carAccessRuleEditParam, carAccessRule);
        this.updateById(carAccessRule);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(CarAccessRuleIdParam carAccessRuleIdParam) {
        this.removeById(carAccessRuleIdParam.getId());
    }

    @Override
    public CarAccessRule detail(CarAccessRuleIdParam carAccessRuleIdParam) {
        return this.queryEntity(carAccessRuleIdParam.getId());
    }

    @Override
    public List<CarAccessRule> list() {
        LambdaQueryWrapper<CarAccessRule> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.orderByAsc(CarAccessRule::getPriority);
        return this.list(queryWrapper);
    }

    /**
     * 获取小车进出规则实体
     *
     * @param id 小车进出规则ID
     * @return 小车进出规则实体
     */
    private CarAccessRule queryEntity(String id) {
        CarAccessRule carAccessRule = this.getById(id);
        if(ObjectUtil.isEmpty(carAccessRule)) {
            throw new CommonException("小车进出规则不存在，id值为：{}", id);
        }
        return carAccessRule;
    }
}
