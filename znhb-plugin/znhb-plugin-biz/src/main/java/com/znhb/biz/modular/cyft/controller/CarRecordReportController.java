/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package com.znhb.biz.modular.cyft.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import com.znhb.common.annotation.CommonLog;
import com.znhb.common.pojo.CommonResult;
import com.znhb.common.pojo.CommonValidList;
import com.znhb.biz.modular.cyft.entity.CarRecordReport;
import com.znhb.biz.modular.cyft.param.CarRecordReportAddParam;
import com.znhb.biz.modular.cyft.param.CarRecordReportEditParam;
import com.znhb.biz.modular.cyft.param.CarRecordReportIdParam;
import com.znhb.biz.modular.cyft.param.CarRecordReportPageParam;
import com.znhb.biz.modular.cyft.service.CarRecordReportService;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;

/**
 * CarRecordReport控制器
 *
 * <AUTHOR>
 * @date  2025/03/27 08:50
 */
@Api(tags = "CarRecordReport控制器")
@ApiSupport(author = "SNOWY_TEAM", order = 1)
@RestController
@Validated
public class CarRecordReportController {

    @Resource
    private CarRecordReportService carRecordReportService;

    /**
     * 获取CarRecordReport分页
     *
     * <AUTHOR>
     * @date  2025/03/27 08:50
     */
    @ApiOperationSupport(order = 1)
    @ApiOperation("获取CarRecordReport分页")
    @SaCheckPermission("/biz/CarRecordReport/page")
    @GetMapping("/biz/CarRecordReport/page")
    public CommonResult<Page<CarRecordReport>> page(CarRecordReportPageParam carRecordReportPageParam) {
        return CommonResult.data(carRecordReportService.page(carRecordReportPageParam));
    }

    /**
     * 添加CarRecordReport
     *
     * <AUTHOR>
     * @date  2025/03/27 08:50
     */
    @ApiOperationSupport(order = 2)
    @ApiOperation("添加CarRecordReport")
    @CommonLog("添加CarRecordReport")
    @SaCheckPermission("/biz/CarRecordReport/add")
    @PostMapping("/biz/CarRecordReport/add")
    public CommonResult<String> add(@RequestBody @Valid CarRecordReportAddParam carRecordReportAddParam) {
        carRecordReportService.add(carRecordReportAddParam);
        return CommonResult.ok();
    }

    /**
     * 编辑CarRecordReport
     *
     * <AUTHOR>
     * @date  2025/03/27 08:50
     */
    @ApiOperationSupport(order = 3)
    @ApiOperation("编辑CarRecordReport")
    @CommonLog("编辑CarRecordReport")
    @SaCheckPermission("/biz/CarRecordReport/edit")
    @PostMapping("/biz/CarRecordReport/edit")
    public CommonResult<String> edit(@RequestBody @Valid CarRecordReportEditParam carRecordReportEditParam) {
        carRecordReportService.edit(carRecordReportEditParam);
        return CommonResult.ok();
    }

    /**
     * 删除CarRecordReport
     *
     * <AUTHOR>
     * @date  2025/03/27 08:50
     */
    @ApiOperationSupport(order = 4)
    @ApiOperation("删除CarRecordReport")
    @CommonLog("删除CarRecordReport")
    @SaCheckPermission("/biz/CarRecordReport/delete")
    @PostMapping("/biz/CarRecordReport/delete")
    public CommonResult<String> delete(@RequestBody @Valid @NotEmpty(message = "集合不能为空")
                                                   CommonValidList<CarRecordReportIdParam> carRecordReportIdParamList) {
        carRecordReportService.delete(carRecordReportIdParamList);
        return CommonResult.ok();
    }

    /**
     * 获取CarRecordReport详情
     *
     * <AUTHOR>
     * @date  2025/03/27 08:50
     */
    @ApiOperationSupport(order = 5)
    @ApiOperation("获取CarRecordReport详情")
    @SaCheckPermission("/biz/CarRecordReport/detail")
    @GetMapping("/biz/CarRecordReport/detail")
    public CommonResult<CarRecordReport> detail(@Valid CarRecordReportIdParam carRecordReportIdParam) {
        return CommonResult.data(carRecordReportService.detail(carRecordReportIdParam));
    }
}
