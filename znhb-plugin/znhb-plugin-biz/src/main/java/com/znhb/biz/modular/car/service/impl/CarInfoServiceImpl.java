/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package com.znhb.biz.modular.car.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.znhb.biz.modular.car.entity.CarInfo;
import com.znhb.biz.modular.car.mapper.CarInfoMapper;
import com.znhb.biz.modular.car.param.CarInfoAddParam;
import com.znhb.biz.modular.car.param.CarInfoEditParam;
import com.znhb.biz.modular.car.param.CarInfoIdParam;
import com.znhb.biz.modular.car.param.CarInfoPageParam;
import com.znhb.biz.modular.car.service.CarInfoService;
import com.znhb.biz.modular.car.service.CarRuleRelationService;
import com.znhb.biz.modular.car.param.CarRuleRelationAddParam;
import com.znhb.biz.modular.internalVehicle.entity.InternalVehicle;
import com.znhb.biz.modular.internalVehicle.service.InternalVehicleService;
import com.znhb.biz.modular.nonRoadMachinery.entity.NonRoadMachinery;
import com.znhb.biz.modular.nonRoadMachinery.service.NonRoadMachineryService;
import com.znhb.biz.modular.org.entity.BizOrg;
import com.znhb.biz.modular.org.service.BizOrgService;
import com.znhb.biz.modular.vehicle.entity.Vehicle;
import com.znhb.biz.modular.vehicle.service.VehicleService;
import com.znhb.common.enums.CommonSortOrderEnum;
import com.znhb.common.exception.CommonException;
import com.znhb.common.page.CommonPageRequest;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.IdUtil;
import com.znhb.common.util.CommonDownloadUtil;

/**
 * 小车信息Service接口实现类
 *
 * <AUTHOR>
 * @date  2025/04/10 15:00
 **/
@Service
@Slf4j
public class CarInfoServiceImpl extends ServiceImpl<CarInfoMapper, CarInfo> implements CarInfoService {


    @Resource
    private InternalVehicleService internalVehicleService;

    @Resource
    private VehicleService vehicleService;

    @Resource
    private NonRoadMachineryService nonRoadMachineryService;


    @Resource
    private CarRuleRelationService carRuleRelationService;

    @Resource
    private BizOrgService orgService;

    @Override
    public Page<CarInfo> page(CarInfoPageParam param) {
        QueryWrapper<CarInfo> queryWrapper = new QueryWrapper<>();
        // 根据车牌号码模糊查询
        if(ObjectUtil.isNotEmpty(param.getPlateNumber())) {
            queryWrapper.lambda().like(CarInfo::getPlateNumber, param.getPlateNumber());
        }
        //根据车牌颜色查询
        if(ObjectUtil.isNotEmpty(param.getPlateColor())) {
            queryWrapper.lambda().eq(CarInfo::getPlateColor, param.getPlateColor());
        }

        // 根据车主姓名模糊查询
        if(ObjectUtil.isNotEmpty(param.getOwnerName())) {
            queryWrapper.lambda().like(CarInfo::getOwnerName, param.getOwnerName());
        }
        // 根据状态查询
        if(ObjectUtil.isNotEmpty(param.getStatus())) {
            queryWrapper.eq("c.status", param.getStatus());
        }
        // 根据机构ID查询
        if(ObjectUtil.isNotEmpty(param.getOrgId())) {
            queryWrapper.lambda().eq(CarInfo::getOrgId, param.getOrgId());
        }
        // 排序
        if(ObjectUtil.isAllNotEmpty(param.getSortField(), param.getSortOrder())) {
            CommonSortOrderEnum.validate(param.getSortOrder());
            queryWrapper.orderBy(true, param.getSortOrder().equals(CommonSortOrderEnum.ASC.getValue()),
                    StrUtil.toUnderlineCase(param.getSortField()));
        } else {
            // 默认按创建时间倒序
            queryWrapper.lambda().orderByDesc(CarInfo::getCreateTime);
        }
        queryWrapper.eq("c.DELETE_FLAG", "NOT_DELETE");
        // 使用关联查询方法
        Page<CarInfo> carInfoPage = baseMapper.pageWithRule(CommonPageRequest.defaultPage(), queryWrapper);
        // 获取所有机构
        List<BizOrg> allOrgList = orgService.list();
        for (CarInfo record : carInfoPage.getRecords()) {
            List<BizOrg> orgList = orgService.getParentListById(allOrgList, record.getOrgId(), true);


            System.out.println(JSONUtil.toJsonStr(orgList));
            StringBuilder orgName = new StringBuilder();
            if (ObjectUtil.isNotEmpty(orgList)) {
                //将orgList根据parentId重新升序排列
                orgList.sort(Comparator.comparing(BizOrg::getParentId));
                for (BizOrg bizOrg : orgList) {
                    orgName.append(bizOrg.getName()).append("/");
                }
            }
            if (orgName.length() > 0) {
                orgName.deleteCharAt(orgName.length() - 1);
            }
            record.setOrgName(orgName.toString());
        }
        return carInfoPage;
    }

    @Override
    public void add(CarInfoAddParam param) {
        // 校验参数
        checkParam(param, false);
        // 实体转换
        CarInfo carInfo = BeanUtil.toBean(param, CarInfo.class);
        // 保存
        checkVehicle(param.getPlateNumber());
        this.save(carInfo);
    }

    @Override
    public void edit(CarInfoEditParam param) {
        // 校验参数
        checkParam(param, true);

        // 获取原始数据
        CarInfo carInfo = this.queryEntity(param.getId());

        // 实体转换
        BeanUtil.copyProperties(param, carInfo);

        // 更新
        this.updateById(carInfo);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<CarInfoIdParam> paramList) {
        // 获取ID列表
        List<String> idList = CollStreamUtil.toList(paramList, CarInfoIdParam::getId);

        // 逻辑删除
        this.removeByIds(idList);
    }

    @Override
    public CarInfo detail(CarInfoIdParam param) {
        return this.queryEntity(param.getId());
    }


    /**
     * 校验参数
     *
     * @param param 参数
     * @param isEdit 是否编辑
     */
    private void checkParam(Object param, boolean isEdit) {
        String plateNumber = null;
        String id = null;

        if (param instanceof CarInfoAddParam) {
            plateNumber = ((CarInfoAddParam) param).getPlateNumber();
        } else if (param instanceof CarInfoEditParam) {
            plateNumber = ((CarInfoEditParam) param).getPlateNumber();
            id = ((CarInfoEditParam) param).getId();
        } else {
            throw new CommonException("参数类型错误");
        }

        // 校验车牌号码是否为空
        if(StrUtil.isEmpty(plateNumber)) {
            throw new CommonException("车牌号码不能为空");
        }

        // 校验车牌号码是否重复
        QueryWrapper<CarInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(CarInfo::getPlateNumber, plateNumber);
        if(isEdit) {
            queryWrapper.lambda().ne(CarInfo::getId, id);
        }
        if(this.count(queryWrapper) > 0) {
            throw new CommonException("车牌号码已存在");
        }
    }

    /**
     * 获取小车信息
     *
     * @param id 小车信息ID
     * @return 小车信息
     */
    private CarInfo queryEntity(String id) {
        CarInfo carInfo = this.getById(id);
        if(ObjectUtil.isEmpty(carInfo)) {
            throw new CommonException("小车信息不存在");
        }
        return carInfo;
    }

    @Override
    public CarInfo queryByPlateNumber(String plateNumber) {
        LambdaQueryWrapper<CarInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CarInfo::getPlateNumber, plateNumber);
        queryWrapper.last("limit 1");
        return this.getOne(queryWrapper);
    }

    @Override
    public void downloadTemplate(HttpServletResponse response) throws IOException {
        try {
            // 创建临时文件
            File tempFile = FileUtil.file(FileUtil.getTmpDir() + FileUtil.FILE_SEPARATOR +
                       "车辆信息导入模板_" + IdUtil.fastSimpleUUID() + ".xlsx");

            // 创建模板数据
            List<CarImportTemplate> list = new ArrayList<>();
            CarImportTemplate template = new CarImportTemplate();
            template.setPlateNumber("京A12345");
            template.setPlateColor("蓝色");
            template.setOrgId("1234567890");
            template.setOwnerIdCard("110101199001011234");
            template.setRuleId("1234567890");
            template.setValidityPeriod("2025-01-01 00:00:00 ~ 2025-12-31 23:59:59");
            template.setOwnerName("张三");
            template.setOwnerPhone("13800138000");
            list.add(template);

            // 设置样式
            WriteCellStyle headWriteCellStyle = new WriteCellStyle();
            WriteFont headWriteFont = new WriteFont();
            headWriteFont.setFontHeightInPoints((short) 12);
            headWriteFont.setBold(true);
            headWriteCellStyle.setWriteFont(headWriteFont);

            WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
            WriteFont contentWriteFont = new WriteFont();
            contentWriteFont.setFontHeightInPoints((short) 11);
            contentWriteCellStyle.setWriteFont(contentWriteFont);

            HorizontalCellStyleStrategy horizontalCellStyleStrategy =
                    new HorizontalCellStyleStrategy(headWriteCellStyle, contentWriteCellStyle);

            // 写入模板到临时文件
            EasyExcel.write(tempFile, CarImportTemplate.class)
                    .registerWriteHandler(horizontalCellStyleStrategy)
                    .sheet("车辆信息")
                    .doWrite(list);

            // 使用通用下载工具类下载文件
            CommonDownloadUtil.download(tempFile, response);

            // 删除临时文件
            FileUtil.del(tempFile);
        } catch (Exception e) {
            log.error("下载模板失败", e);
            throw new CommonException("下载模板失败: " + e.getMessage());
        }
    }



    /**
     * 导入模板数据对象
     */
    @Getter
    @Setter
    public static class CarImportTemplate {
        @ExcelProperty(value = "车牌号码*")
        private String plateNumber;

        @ExcelProperty(value = "车牌颜色*")
        private String plateColor;

        @ExcelProperty(value = "所在部门ID")
        private String orgId;

        @ExcelProperty(value = "身份证号码")
        private String ownerIdCard;

        @ExcelProperty(value = "通行规则ID")
        private String ruleId;

        @ExcelProperty(value = "有效期")
        private String validityPeriod;

        @ExcelProperty(value = "车主姓名")
        private String ownerName;

        @ExcelProperty(value = "车主电话")
        private String ownerPhone;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void importCarInfo(MultipartFile file) throws IOException {
        if (file == null || file.isEmpty()) {
            throw new CommonException("导入文件不能为空");
        }

        try {
            // 读取Excel数据
            List<CarImportTemplate> dataList = EasyExcel.read(file.getInputStream())
                    .head(CarImportTemplate.class)
                    .sheet()
                    .doReadSync();

            if (dataList.isEmpty()) {
                throw new CommonException("导入文件数据为空");
            }

            // 处理数据
            List<CarInfo> carInfoList = new ArrayList<>();
            for (CarImportTemplate row : dataList) {
                // 检查必填项
                String plateNumber = row.getPlateNumber();
                if (StrUtil.isEmpty(plateNumber)) {
                    continue; // 跳过空行
                }

                // 检查车牌是否已存在
                CarInfo existingCar = this.queryByPlateNumber(plateNumber);
                if (existingCar != null) {
                    throw new CommonException("车牌号码" + plateNumber + "已存在");
                }

                // 创建新车辆信息
                CarInfo carInfo = new CarInfo();
                carInfo.setPlateNumber(plateNumber);
                carInfo.setPlateColor(row.getPlateColor());
                carInfo.setOrgId(row.getOrgId());
                carInfo.setOwnerIdCard(row.getOwnerIdCard());
                carInfo.setValidityPeriodTimeRange(row.getValidityPeriod());
                carInfo.setOwnerName(row.getOwnerName());
                carInfo.setOwnerPhone(row.getOwnerPhone());
                carInfo.setStatus("1"); // 默认启用

                carInfoList.add(carInfo);
            }

            if (carInfoList.isEmpty()) {
                throw new CommonException("没有有效的数据可导入");
            }

            // 批量保存
            this.saveBatch(carInfoList);

            // 处理规则关联
            for (int i = 0; i < carInfoList.size(); i++) {
                CarInfo carInfo = carInfoList.get(i);
                CarImportTemplate row = dataList.get(i);

                // 如果有规则ID，创建规则关联
                String ruleId = row.getRuleId();
                if (!StrUtil.isEmpty(ruleId)) {
                    try {
                        CarRuleRelationAddParam relationParam = new CarRuleRelationAddParam();
                        relationParam.setCarId(carInfo.getId());
                        relationParam.setRuleId(ruleId);
                        carRuleRelationService.add(relationParam);
                    } catch (Exception e) {
                        log.error("创建规则关联失败", e);
                        // 不抛出异常，继续处理其他车辆
                    }
                }
            }

        } catch (Exception e) {
            log.error("导入车辆信息失败", e);
            throw new CommonException("导入车辆信息失败: " + e.getMessage());
        }
    }

    private void checkVehicle(String plateNumber) {
        InternalVehicle internalVehicle = internalVehicleService.queryNumberEntity(plateNumber);
        if (ObjectUtil.isNotEmpty(internalVehicle)) {
            throw new CommonException("该车辆已备案为内部车辆");
        }
        Vehicle vehicle = vehicleService.queryNumberEntity(plateNumber);
        if (ObjectUtil.isNotEmpty(vehicle)) {
            throw new CommonException("该车辆已备案为外部车辆");
        }
        NonRoadMachinery nonRoadMachinery = nonRoadMachineryService.queryNumberEntity(plateNumber);
        if (ObjectUtil.isNotEmpty(nonRoadMachinery)) {
            throw new CommonException("该车辆已备案为非移机械");
        }
    }
}
