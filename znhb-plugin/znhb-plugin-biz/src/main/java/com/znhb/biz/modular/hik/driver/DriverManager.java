package com.znhb.biz.modular.hik.driver;



import cn.hutool.core.util.StrUtil;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import com.znhb.biz.modular.hik.driver.barrierGate.BarrierGateDriver;
import com.znhb.biz.modular.hik.driver.camera.HikBaseCameraDriver;

import java.util.HashMap;
import java.util.Map;

@Slf4j
public class DriverManager {
    @Getter
    private static DriverManager instance = new DriverManager();

    //设备驱动集合
    private static final Map<String, DeviceDriver> driverMap = new HashMap<>();


    //道闸驱动集合
    private static final Map<String, BarrierGateDriver> gateDriverMap = new HashMap<>();

    private DriverManager() {
    }

    //存储设备驱动 根据id存放
    public void putDriver(DeviceDriver driver) {
        driverMap.put(driver.getDeviceId(), driver);
    }

    //获取设备驱动 根据ip获取
    public DeviceDriver getDriverByIp(String ip) {
        for (DeviceDriver d : driverMap.values()) {
            if (d.getDeviceIp().equals(ip)) {
                return d;
            }
        }
        return null;
    }

    //获取设备驱动
    public DeviceDriver getDriverById(String id) {
        for (DeviceDriver d : driverMap.values()) {
            if (d.getDeviceId().equals(id)) {
                return d;
            }
        }
        return null;
    }


    //存储道闸驱动
    public void putDriver(BarrierGateDriver driver) {
        gateDriverMap.put(driver.getBarrierGate().getId(), driver);
    }

    //删除道闸驱动
    public void removeDriver(BarrierGateDriver driver) {
        gateDriverMap.remove(driver.getBarrierGate().getId());
    }

    //根据车牌抓拍摄像头驱动获取道闸驱动
    public BarrierGateDriver getGateDriverByPlateCamera(HikBaseCameraDriver cameraDriver) {
        for (BarrierGateDriver driver : gateDriverMap.values()) {
            String plateCaptureCameraId = driver.getBarrierGate().getPlateCaptureCameraId();
            if(StrUtil.isNotEmpty(plateCaptureCameraId) && plateCaptureCameraId.equals(cameraDriver.getDeviceId())){
                return driver;
            }
        }
        return null;
    }
    //根据车身抓拍摄像头驱动获取道闸驱动
    public BarrierGateDriver getGateDriverByBodyCamera(HikBaseCameraDriver cameraDriver) {
        for (BarrierGateDriver driver : gateDriverMap.values()) {
            String bodyCaptureCameraId = driver.getBarrierGate().getBodyCaptureCameraId();
            if(StrUtil.isNotEmpty(bodyCaptureCameraId) && bodyCaptureCameraId.equals(cameraDriver.getDeviceId())){
                return driver;
            }
        }
        return null;
    }
    //根据车尾抓拍摄像头驱动获取道闸驱动
    public BarrierGateDriver getGateDriverByTailCamera(HikBaseCameraDriver cameraDriver) {
        for (BarrierGateDriver driver : gateDriverMap.values()) {
            String tailCaptureCameraId = driver.getBarrierGate().getTailCaptureCameraId();
            if(StrUtil.isNotEmpty(tailCaptureCameraId) && tailCaptureCameraId.equals(cameraDriver.getDeviceId())){
                return driver;
            }
        }
        return null;
    }

    //根据id获取道闸驱动
    public BarrierGateDriver getGateDriverById(String id) {
        for (BarrierGateDriver d : gateDriverMap.values()) {
            if (d.getBarrierGate().getId().equals(id)) {
                return d;
            }
        }
        return null;
    }

}
