package com.znhb.biz.modular.hik.handler.qunli;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.znhb.biz.core.enums.*;
import com.znhb.biz.modular.barrierGate.entity.BarrierGate;
import com.znhb.biz.modular.car.entity.CarAccessRule;
import com.znhb.biz.modular.car.entity.CarInfo;
import com.znhb.biz.modular.car.entity.CarRuleRelation;
import com.znhb.biz.modular.car.service.CarAccessRecordService;
import com.znhb.biz.modular.car.service.CarAccessRuleService;
import com.znhb.biz.modular.car.service.CarRuleRelationService;
import com.znhb.biz.modular.hik.driver.barrierGate.BarrierGateDriver;
import com.znhb.biz.modular.hik.entity.CheckParam;
import com.znhb.biz.modular.hik.entity.GoodsMeterInfo;
import com.znhb.biz.modular.hik.entity.MeterParam;
import com.znhb.biz.modular.hik.handler.CommonApi;
import com.znhb.biz.modular.materialdetail.entity.MaterialDetail;
import com.znhb.biz.modular.materialdetail.service.MaterialDetailService;
import com.znhb.biz.modular.order.entity.Order;
import com.znhb.biz.modular.order.service.OrderService;
import com.znhb.dev.api.DevConfigApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component("qunLiCommonApi")
@Slf4j
public class QunLiCommonApi implements CommonApi {

    @Resource
    private DevConfigApi devConfigApi;

    @Resource
    private OrderService orderService;

    @Resource
    private MaterialDetailService materialDetailService;

    @Resource
    private CarAccessRuleService carAccessRuleService;

    @Resource
    private CarRuleRelationService carRuleRelationService;

    @Resource
    private CarAccessRecordService carAccessRecordService;

    /**
     * 车辆洗车数据查询 返回true代表未清洗  false已清洗
     */
    public boolean judgeNotWash(String car_number, String startTime, String endTime) {
        try {
            //从超低平台获取洗车数据 查询是否洗车
            String url = devConfigApi.getEnvBaseUrl() + "queryCarWash";
            Map<String, Object> m = new HashMap<>();
            m.put("carNo", car_number);
            m.put("startTime", startTime);
            m.put("endTime", endTime);
            log.info("获取洗车数据入参{}",JSONUtil.toJsonStr(m));
            String content = HttpUtil.post(url, m);
            log.info("洗车响应数据{}", content);
            JSONObject obj = JSONUtil.parseObj(content);
            int code = obj.getInt("code");
            if (code == 200) {
                Object data = obj.get("data");
                return ObjectUtil.isEmpty(data);
            }else{
                return true;
            }
        } catch (Exception e) {
            log.info(e.getMessage(), e);
            return true;
        }
    }

    /**
     * 车辆进厂验证
     */
    public CheckParam checkInAction(String plateNumber, String vehicleType) {
        CheckParam checkParam = new CheckParam();
        //内部车辆
        if (vehicleType.equals(VehicleCategoryEnum.INTERNAL.getCode())) {
            //是否有系统派单
            Order order = orderService.queryGateOrder(plateNumber, OrderStatusEnum.DISPATCHED.getCode());
            if (ObjectUtil.isEmpty(order)) {
                checkParam.setResult(false);
                checkParam.setMessage(CommonEnum.NO_ORDER.getCodeMessage());
                return checkParam;
            }
            orderService.updateOrderStatus(plateNumber, OrderStatusEnum.ENTERED.getCode());
            checkParam.setResult(true);
            checkParam.setOrder(order);
            return checkParam;
        }
        //外部车辆
        if (vehicleType.equals(VehicleCategoryEnum.EXTERNAL.getCode())) {
            //是否有系统派单
            Order order = orderService.queryGateOrder(plateNumber, OrderStatusEnum.DISPATCHED.getCode());
            if (ObjectUtil.isEmpty(order)) {
                try {
                    //从第三方物流系统获取车辆入场许可

                } catch (Exception e) {
                    log.info("入场许可异常：{}", e.getMessage(), e);
                }
            }else{
                MaterialDetail materialDetail = materialDetailService.saveForGate(order.getMaterial(), order.getMaterialCategory());
                checkParam.setEntryOrderNumber(order.getOrderNumber());
                checkParam.setEntryGoodsId(materialDetail.getId());
                checkParam.setEntryGoodsName(materialDetail.getName());
                checkParam.setEntryGoodsCategoryId(materialDetail.getCategoryId());
                checkParam.setEntryGoodsCategoryName(materialDetail.getCategoryName());
                orderService.updateOrderStatus(plateNumber, OrderStatusEnum.ENTERED.getCode());
            }
        }
        checkParam.setResult(true);
        return checkParam;
    }

    /**
     * 车辆出厂验证
     */
    public CheckParam checkOutAction(String plateNumber, String vehicleType) throws UnsupportedEncodingException {
        CheckParam checkParam = new CheckParam();
        checkParam.setResult(true);
        //内部车辆
        if (vehicleType.equals(VehicleCategoryEnum.INTERNAL.getCode())) {
            //是否有系统派单
            Order order = orderService.queryGateOrder(plateNumber, OrderStatusEnum.ENTERED.getCode());
            if (ObjectUtil.isEmpty(order)) {
                //从第三方物流系统获取出厂许可

                checkParam.setResult(false);
                checkParam.setMessage(CommonEnum.NO_ORDER.getCodeMessage());
                return checkParam;
            }else{
                orderService.updateOrderStatus(plateNumber, OrderStatusEnum.EXITED.getCode());
            }
        }
        //外部车辆
        if (vehicleType.equals(VehicleCategoryEnum.EXTERNAL.getCode())) {
            //是否有系统派单
            Order order = orderService.queryGateOrder(plateNumber, OrderStatusEnum.ENTERED.getCode());
            if (ObjectUtil.isEmpty(order)) {
                //从第三方物流系统获取出厂许可

            }else{
                checkParam.setOrder(order);
                orderService.updateOrderStatus(plateNumber, OrderStatusEnum.EXITED.getCode());
            }
        }
        return checkParam;
    }

    /**
     * 获取计量数据
     */
    public MeterParam searchMeterData(String plateNumber, String entryTime, String exitTime, Order order) {
        if (ObjectUtil.isEmpty(order)) {
            return  this.searchMeterData(plateNumber, entryTime, exitTime);
        }
        MeterParam meterParam = new MeterParam();
        GoodsMeterInfo goodsMeterInfo = new GoodsMeterInfo();
        goodsMeterInfo.setSource(order.getSources());
        goodsMeterInfo.setTarget(order.getTarget());
        goodsMeterInfo.setOutOrderNumber(order.getOrderNumber());
        goodsMeterInfo.setOutGoodsName(order.getMaterial());
        goodsMeterInfo.setOutCategoryName(order.getMaterialCategory());
        meterParam.setResult(true);
        meterParam.setGoodsMeterInfo(goodsMeterInfo);
        return meterParam;
    }

    /**
     * 获取计量数据
     */
    public MeterParam searchMeterData(String plateNumber, String entryTime, String exitTime) {
        return new MeterParam();
    }

    /**
     * 排放阶段验证
     */
    public CheckParam emissionCheck(BarrierGateDriver driver, String emissionStage) {
        CheckParam checkParam = new CheckParam();
        boolean gateNeedStageLimit = driver.getBarrierGate().isNeedStageLimit();
        String gateStageLimit = driver.getBarrierGate().getStageLimit();
        if (gateNeedStageLimit) {
            if (NumberUtil.isNumber(gateStageLimit)) {
                if (Integer.parseInt(emissionStage) < Integer.parseInt(gateStageLimit)) {
                    //不满足排放要求
                    checkParam.setResult(false);
                    checkParam.setMessage(CommonEnum.ILLEGAL_EMISSION_STAGE.getCodeMessage());
                    return checkParam;
                }
            } else {
                if (!StrUtil.equals(EmissionStageEnum.ELECTRIC.getCode(), gateStageLimit)) {
                    //不满足排放要求

                    checkParam.setResult(false);
                    checkParam.setMessage(CommonEnum.ILLEGAL_EMISSION_STAGE.getCodeMessage());
                    return checkParam;
                }
            }
            log.info("车辆排放阶段满足条件，车辆排放阶段为：{}，限制排放阶段为：{}", driver.getEmissionName(), gateStageLimit);
        }
        checkParam.setResult(true);
        return checkParam;
    }


    /**
     * 小车入厂规则验证
     */
    @Override
    public CheckParam checkInCarAccessRule(CarInfo carInfo, BarrierGate barrierGate) {
        CheckParam checkParam = new CheckParam();
        checkParam.setResult(true);

        // 获取车辆关联的规则
        List<CarRuleRelation> ruleRelations = carRuleRelationService.list(carInfo.getId());
        if (ruleRelations.isEmpty()) {
            // 没有关联规则，不通过
            checkParam.setResult(false);
            checkParam.setMessage("车辆未绑定规则");
            return checkParam;
        }

        // 获取关联的规则
        CarRuleRelation relation = ruleRelations.get(0); // 一辆车只能绑定一个规则
        CarAccessRule rule = carAccessRuleService.getById(relation.getRuleId());
        if (rule == null) {
            // 规则不存在，默认不通过
            checkParam.setResult(false);
            checkParam.setMessage("规则不存在或已删除");
            return checkParam;
        }

        // 检查规则状态
        if (!RuleStatusEnum.ENABLE.getCode().equals(rule.getStatus())) {
            // 规则已禁用，默认不通过
            checkParam.setResult(false);
            checkParam.setMessage("规则已禁用");
            return checkParam;
        }

        // 检查适用日期
        if (StrUtil.isNotBlank(rule.getApplyDays())) {
            LocalDate today = LocalDate.now();
            DayOfWeek dayOfWeek = today.getDayOfWeek();
            int dayValue = dayOfWeek.getValue(); // 1-7 对应周一到周日

            String[] applyDays = rule.getApplyDays().split(",");
            boolean dayMatch = false;
            for (String day : applyDays) {
                if (String.valueOf(dayValue).equals(day)) {
                    dayMatch = true;
                    break;
                }
            }

            if (!dayMatch) {
                checkParam.setResult(false);
                checkParam.setMessage("当前日期不在规则适用范围内，禁止出入厂。");
                return checkParam;
            }
        }

        // 检查时间限制
        if (rule.isEnableTimeRule() && rule.getStartTime() != null && rule.getEndTime() != null) {
            LocalTime now = LocalTime.now();
            LocalTime startTime = rule.getStartTime().toLocalTime();
            LocalTime endTime = rule.getEndTime().toLocalTime();

            if (now.isBefore(startTime) || now.isAfter(endTime)) {
                checkParam.setResult(false);
                checkParam.setMessage("当前时间不在允许进入时间范围内");
                return checkParam;
            }
        }

        // 检查次数限制
        if (rule.isEnableCountRule() && rule.getMaxCount() != null) {
            // 获取当天进入次数
            long todayCount = carAccessRecordService.getTodayEntryCount(carInfo.getPlateNumber());
            if (todayCount >= rule.getMaxCount()) {
                checkParam.setResult(false);
                checkParam.setMessage("当日进入次数已达上限");
                return checkParam;
            }
        }

        // 检查入厂道闸限制
        if (rule.isEnableInGateRule() && StrUtil.isNotBlank(rule.getAllowedInGates())) {
            String gateId = barrierGate.getId();
            String[] allowedInGateIds = rule.getAllowedInGates().split(",");
            boolean areaMatch = false;
            for (String allowedInGateId : allowedInGateIds) {
                if (gateId.equals(allowedInGateId)) {
                    areaMatch = true;
                    break;
                }
            }

            if (!areaMatch) {
                checkParam.setResult(false);
                checkParam.setMessage("当前道闸不允许入厂");
                return checkParam;
            }
        }

        checkParam.setAutoOpen(rule.isEntryGateOpenType());
        return checkParam;
    }


    /**
     * 小车出厂规则验证
     */
    @Override
    public CheckParam checkOutCarAccessRule(CarInfo carInfo, BarrierGate barrierGate, Date entryTime) {

        CheckParam checkParam = new CheckParam();
        checkParam.setResult(true);

        // 获取车辆关联的规则
        List<CarRuleRelation> ruleRelations = carRuleRelationService.list(carInfo.getId());
        if (ruleRelations.isEmpty()) {
            // 没有关联规则，不通过
            checkParam.setResult(false);
            checkParam.setMessage("车辆未绑定规则");
            return checkParam;
        }

        // 获取关联的规则
        CarRuleRelation relation = ruleRelations.get(0); // 一辆车只能绑定一个规则
        CarAccessRule rule = carAccessRuleService.getById(relation.getRuleId());
        if (rule == null) {
            // 规则不存在，默认不通过
            checkParam.setResult(false);
            checkParam.setMessage("规则不存在或已删除");
            return checkParam;
        }

        // 检查规则状态
        if (!RuleStatusEnum.ENABLE.getCode().equals(rule.getStatus())) {
            // 规则已禁用，默认不通过
            checkParam.setResult(false);
            checkParam.setMessage("规则已禁用");
            return checkParam;
        }

        // 检查适用日期
        if (StrUtil.isNotBlank(rule.getApplyDays())) {
            LocalDate today = LocalDate.now();
            DayOfWeek dayOfWeek = today.getDayOfWeek();
            int dayValue = dayOfWeek.getValue(); // 1-7 对应周一到周日

            String[] applyDays = rule.getApplyDays().split(",");
            boolean dayMatch = false;
            for (String day : applyDays) {
                if (String.valueOf(dayValue).equals(day)) {
                    dayMatch = true;
                    break;
                }
            }

            if (!dayMatch) {
                checkParam.setResult(false);
                checkParam.setMessage("当前日期不在规则适用范围内，禁止出入厂。");
                return checkParam;
            }
        }

        // 检查出厂道闸限制
        if (rule.isEnableOutGateRule() && StrUtil.isNotBlank(rule.getAllowedOutGates())) {
            String gateId = barrierGate.getId();
            String[] allowedOutGateIds = rule.getAllowedOutGates().split(",");
            boolean areaMatch = false;
            for (String allowedOutGateId : allowedOutGateIds) {
                if (gateId.equals(allowedOutGateId)) {
                    areaMatch = true;
                    break;
                }
            }

            if (!areaMatch) {
                checkParam.setResult(false);
                checkParam.setMessage("当前道闸不允许出厂");
                return checkParam;
            }
        }

        //停留时长限制
        if (rule.isEnableDurationRule()) {
            Integer maxDuration = rule.getMaxDuration();
            Integer minDuration = rule.getMinDuration();

            long stayDuration = (DateUtil.date().getTime() - entryTime.getTime()) / (1000 * 60);

            if (stayDuration < minDuration) {
                checkParam.setResult(false);
                checkParam.setMessage("停留时长" + stayDuration + "分钟，至少停留" + minDuration + "分钟可出厂。");
                return checkParam;
            }

            if (stayDuration > maxDuration) {
                checkParam.setResult(false);
                checkParam.setMessage("停留时长" + stayDuration + "分钟，超过最大停留时间" + maxDuration + "禁止出厂。");
                return checkParam;
            }

        }

        checkParam.setAutoOpen(rule.isEntryGateOpenType());
        return checkParam;
    }
}
