/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package com.znhb.biz.modular.car.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import com.znhb.biz.modular.car.entity.CarInfo;
import com.znhb.biz.modular.car.param.CarInfoAddParam;
import com.znhb.biz.modular.car.param.CarInfoEditParam;
import com.znhb.biz.modular.car.param.CarInfoIdParam;
import com.znhb.biz.modular.car.param.CarInfoPageParam;
import com.znhb.biz.modular.car.service.CarInfoService;
import com.znhb.common.annotation.CommonLog;
import com.znhb.common.pojo.CommonResult;
import com.znhb.common.pojo.CommonValidList;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.io.IOException;
import java.util.List;

/**
 * 小车信息控制器
 *
 * <AUTHOR>
 * @date  2025/04/10 15:00
 **/
@Api(tags = "小车信息控制器")
@ApiSupport(author = "ZNHB_TEAM", order = 1)
@RestController
@Validated
public class CarInfoController {

    @Resource
    private CarInfoService carInfoService;

    /**
     * 获取小车信息分页
     *
     * @param carInfoPageParam 查询参数
     * @return 查询结果
     */
    @ApiOperation("获取小车信息分页")
    @ApiOperationSupport(order = 1)
    @SaCheckPermission("/biz/car/info/page")
    @GetMapping("/biz/car/info/page")
    public CommonResult<Page<CarInfo>> page(CarInfoPageParam carInfoPageParam) {
        return CommonResult.data(carInfoService.page(carInfoPageParam));
    }

    /**
     * 添加小车信息
     *
     * @param carInfoAddParam 添加参数
     * @return 添加结果
     */
    @ApiOperation("添加小车信息")
    @ApiOperationSupport(order = 2)
    @CommonLog("添加小车信息")
    @SaCheckPermission("/biz/car/info/add")
    @PostMapping("/biz/car/info/add")
    public CommonResult<String> add(@RequestBody @Valid CarInfoAddParam carInfoAddParam) {
        carInfoService.add(carInfoAddParam);
        return CommonResult.ok();
    }

    /**
     * 编辑小车信息
     *
     * @param carInfoEditParam 编辑参数
     * @return 编辑结果
     */
    @ApiOperation("编辑小车信息")
    @ApiOperationSupport(order = 3)
    @CommonLog("编辑小车信息")
    @SaCheckPermission("/biz/car/info/edit")
    @PostMapping("/biz/car/info/edit")
    public CommonResult<String> edit(@RequestBody @Valid CarInfoEditParam carInfoEditParam) {
        carInfoService.edit(carInfoEditParam);
        return CommonResult.ok();
    }

    /**
     * 删除小车信息
     *
     * @param carInfoIdParamList 删除参数
     * @return 删除结果
     */
    @ApiOperation("删除小车信息")
    @ApiOperationSupport(order = 4)
    @CommonLog("删除小车信息")
    @SaCheckPermission("/biz/car/info/delete")
    @PostMapping("/biz/car/info/delete")
    public CommonResult<String> delete(@RequestBody @NotEmpty(message = "参数不能为空")
                                     @Valid CommonValidList<CarInfoIdParam> carInfoIdParamList) {
        carInfoService.delete(carInfoIdParamList);
        return CommonResult.ok();
    }

    /**
     * 获取小车信息详情
     *
     * @param carInfoIdParam 查询参数
     * @return 查询结果
     */
    @ApiOperation("获取小车信息详情")
    @ApiOperationSupport(order = 5)
    @SaCheckPermission("/biz/car/info/detail")
    @GetMapping("/biz/car/info/detail")
    public CommonResult<CarInfo> detail(@Valid CarInfoIdParam carInfoIdParam) {
        return CommonResult.data(carInfoService.detail(carInfoIdParam));
    }

    /**
     * 下载小车信息导入模板
     *
     * @param response HTTP响应对象
     */
    @ApiOperation("下载小车信息导入模板")
    @ApiOperationSupport(order = 6)
    @SaCheckPermission("/biz/car/info/downloadTemplate")
    @GetMapping("/biz/car/info/downloadTemplate")
    public void downloadTemplate(HttpServletResponse response) throws IOException {
        carInfoService.downloadTemplate(response);
    }

    /**
     * 导入小车信息
     *
     * @param file 导入文件
     * @return 导入结果
     */
    @ApiOperation("导入小车信息")
    @ApiOperationSupport(order = 7)
    @CommonLog("导入小车信息")
    @SaCheckPermission("/biz/car/info/import")
    @PostMapping("/biz/car/info/import")
    public CommonResult<String> importCarInfo(@RequestParam("file") MultipartFile file) throws IOException {
        carInfoService.importCarInfo(file);
        return CommonResult.ok();
    }
}
