/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package com.znhb.biz.modular.internalVehicleRecord.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import com.znhb.biz.modular.vehicleRecord.param.CarRecordIdParam;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import com.znhb.common.annotation.CommonLog;
import com.znhb.common.pojo.CommonResult;
import com.znhb.common.pojo.CommonValidList;
import com.znhb.biz.modular.internalVehicleRecord.entity.InternalVehicleRecord;
import com.znhb.biz.modular.internalVehicleRecord.param.InternalVehicleRecordAddParam;
import com.znhb.biz.modular.internalVehicleRecord.param.InternalVehicleRecordEditParam;
import com.znhb.biz.modular.internalVehicleRecord.param.InternalVehicleRecordIdParam;
import com.znhb.biz.modular.internalVehicleRecord.param.InternalVehicleRecordPageParam;
import com.znhb.biz.modular.internalVehicleRecord.service.InternalVehicleRecordService;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;

/**
 * 厂内运输台账控制器
 *
 * <AUTHOR>
 * @date  2025/04/03 11:37
 */
@Api(tags = "厂内运输台账控制器")
@ApiSupport(author = "SNOWY_TEAM", order = 1)
@RestController
@Validated
public class InternalVehicleRecordController {

    @Resource
    private InternalVehicleRecordService internalVehicleRecordService;

    /**
     * 获取厂内运输台账分页
     *
     * <AUTHOR>
     * @date  2025/04/03 11:37
     */
    @ApiOperationSupport(order = 1)
    @ApiOperation("获取厂内运输台账分页")
    @SaCheckPermission("/biz/internalVehicleRecord/page")
    @GetMapping("/biz/internalVehicleRecord/page")
    public CommonResult<Page<InternalVehicleRecord>> page(InternalVehicleRecordPageParam internalVehicleRecordPageParam) {
        return CommonResult.data(internalVehicleRecordService.page(internalVehicleRecordPageParam));
    }

    /**
     * 添加厂内运输台账
     *
     * <AUTHOR>
     * @date  2025/04/03 11:37
     */
    @ApiOperationSupport(order = 2)
    @ApiOperation("添加厂内运输台账")
    @CommonLog("添加厂内运输台账")
    @SaCheckPermission("/biz/internalVehicleRecord/add")
    @PostMapping("/biz/internalVehicleRecord/add")
    public CommonResult<String> add(@RequestBody @Valid InternalVehicleRecordAddParam internalVehicleRecordAddParam) {
        internalVehicleRecordService.add(internalVehicleRecordAddParam);
        return CommonResult.ok();
    }

    /**
     * 编辑厂内运输台账
     *
     * <AUTHOR>
     * @date  2025/04/03 11:37
     */
    @ApiOperationSupport(order = 3)
    @ApiOperation("编辑厂内运输台账")
    @CommonLog("编辑厂内运输台账")
    @SaCheckPermission("/biz/internalVehicleRecord/edit")
    @PostMapping("/biz/internalVehicleRecord/edit")
    public CommonResult<String> edit(@RequestBody @Valid InternalVehicleRecordEditParam internalVehicleRecordEditParam) {
        internalVehicleRecordService.edit(internalVehicleRecordEditParam);
        return CommonResult.ok();
    }

    /**
     * 删除厂内运输台账
     *
     * <AUTHOR>
     * @date  2025/04/03 11:37
     */
    @ApiOperationSupport(order = 4)
    @ApiOperation("删除厂内运输台账")
    @CommonLog("删除厂内运输台账")
    @SaCheckPermission("/biz/internalVehicleRecord/delete")
    @PostMapping("/biz/internalVehicleRecord/delete")
    public CommonResult<String> delete(@RequestBody @Valid @NotEmpty(message = "集合不能为空")
                                                   CommonValidList<InternalVehicleRecordIdParam> internalVehicleRecordIdParamList) {
        internalVehicleRecordService.delete(internalVehicleRecordIdParamList);
        return CommonResult.ok();
    }

    /**
     * 获取厂内运输台账详情
     *
     * <AUTHOR>
     * @date  2025/04/03 11:37
     */
    @ApiOperationSupport(order = 5)
    @ApiOperation("获取厂内运输台账详情")
    @SaCheckPermission("/biz/internalVehicleRecord/detail")
    @GetMapping("/biz/internalVehicleRecord/detail")
    public CommonResult<InternalVehicleRecord> detail(@Valid InternalVehicleRecordIdParam internalVehicleRecordIdParam) {
        return CommonResult.data(internalVehicleRecordService.detail(internalVehicleRecordIdParam));
    }

    /**
     * 确认出厂
     * <AUTHOR>
     * @date  2025/03/22 10:05
     */
    @ApiOperationSupport(order = 6)
    @ApiOperation("确认出厂")
    @GetMapping("/biz/internalVehicleRecord/confirmExit")
    public CommonResult<String> confirmExit(@Valid InternalVehicleRecordIdParam internalVehicleRecordIdParam) {
        internalVehicleRecordService.confirmExit(internalVehicleRecordIdParam);
        return CommonResult.ok();
    }
}
