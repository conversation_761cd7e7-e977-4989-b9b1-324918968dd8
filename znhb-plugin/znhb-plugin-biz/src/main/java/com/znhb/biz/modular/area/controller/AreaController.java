package com.znhb.biz.modular.area.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.znhb.biz.modular.area.entity.Area;
import com.znhb.biz.modular.area.param.AreaAddParam;
import com.znhb.biz.modular.area.param.AreaEditParam;
import com.znhb.biz.modular.area.param.AreaIdParam;
import com.znhb.biz.modular.area.param.AreaPageParam;
import com.znhb.biz.modular.area.result.AreaTreeResult;
import com.znhb.biz.modular.area.service.AreaService;
import com.znhb.common.annotation.CommonLog;
import com.znhb.common.pojo.CommonResult;
import com.znhb.common.pojo.CommonValidList;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 区域控制器
 *
 * <AUTHOR>
 * @date 2025-04-11
 */
@Api(tags = "区域控制器")
@RestController
public class AreaController {

    @Resource
    private AreaService areaService;

    /**
     * 获取区域分页列表
     *
     * @param areaPageParam 区域分页参数
     * @return 区域分页列表
     */
    @ApiOperation("获取区域分页列表")
    @GetMapping("/biz/area/page")
    public CommonResult<Page<Area>> page(AreaPageParam areaPageParam) {
        return CommonResult.data(areaService.page(areaPageParam));
    }

    /**
     * 添加区域
     *
     * @param areaAddParam 区域添加参数
     * @return 添加结果
     */
    @ApiOperation("添加区域")
    @CommonLog("添加区域")
    @PostMapping("/biz/area/add")
    public CommonResult<String> add(@RequestBody @Validated AreaAddParam areaAddParam) {
        areaService.add(areaAddParam);
        return CommonResult.ok();
    }

    /**
     * 编辑区域
     *
     * @param areaEditParam 区域编辑参数
     * @return 编辑结果
     */
    @ApiOperation("编辑区域")
    @CommonLog("编辑区域")
    @PostMapping("/biz/area/edit")
    public CommonResult<String> edit(@RequestBody @Validated AreaEditParam areaEditParam) {
        areaService.edit(areaEditParam);
        return CommonResult.ok();
    }

    /**
     * 删除区域
     *
     * @param areaIdParamIds 区域ID参数
     * @return 删除结果
     */
    @ApiOperation("删除区域")
    @CommonLog("删除区域")
    @PostMapping("/biz/area/delete")
    public CommonResult<String> delete(@RequestBody @Validated CommonValidList<AreaIdParam> areaIdParamIds) {
        areaService.delete(areaIdParamIds);
        return CommonResult.ok();
    }

    /**
     * 获取区域详情
     *
     * @param areaIdParam 区域ID参数
     * @return 区域详情
     */
    @ApiOperation("获取区域详情")
    @GetMapping("/biz/area/detail")
    public CommonResult<Area> detail(@Validated AreaIdParam areaIdParam) {
        return CommonResult.data(areaService.detail(areaIdParam));
    }

    /**
     * 获取区域树形列表
     *
     * @return 区域树形列表
     */
    @ApiOperation("获取区域树形列表")
    @GetMapping("/biz/area/tree")
    public CommonResult<List<AreaTreeResult>> tree() {
        return CommonResult.data(areaService.tree());
    }
}
