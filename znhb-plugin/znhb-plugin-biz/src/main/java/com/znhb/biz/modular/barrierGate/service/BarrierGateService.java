/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package com.znhb.biz.modular.barrierGate.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.znhb.biz.modular.barrierGate.entity.BarrierGate;
import com.znhb.biz.modular.barrierGate.param.BarrierGateAddParam;
import com.znhb.biz.modular.barrierGate.param.BarrierGateEditParam;
import com.znhb.biz.modular.barrierGate.param.BarrierGateIdParam;
import com.znhb.biz.modular.barrierGate.param.BarrierGatePageParam;

import java.util.List;

/**
 * t_barrier_gateService接口
 *
 * <AUTHOR>
 * @date  2025/03/21 10:05
 **/
public interface BarrierGateService extends IService<BarrierGate> {

    /**
     * 获取t_barrier_gate分页
     *
     * <AUTHOR>
     * @date  2025/03/21 10:05
     */
    Page<BarrierGate> page(BarrierGatePageParam barrierGatePageParam);

    /**
     * 添加t_barrier_gate
     *
     * <AUTHOR>
     * @date  2025/03/21 10:05
     */
    void add(BarrierGateAddParam barrierGateAddParam);

    /**
     * 编辑t_barrier_gate
     *
     * <AUTHOR>
     * @date  2025/03/21 10:05
     */
    void edit(BarrierGateEditParam barrierGateEditParam);

    /**
     * 删除t_barrier_gate
     *
     * <AUTHOR>
     * @date  2025/03/21 10:05
     */
    void delete(List<BarrierGateIdParam> barrierGateIdParamList);

    /**
     * 获取t_barrier_gate详情
     *
     * <AUTHOR>
     * @date  2025/03/21 10:05
     */
    BarrierGate detail(BarrierGateIdParam barrierGateIdParam);

    /**
     * 获取t_barrier_gate详情
     *
     * <AUTHOR>
     * @date  2025/03/21 10:05
     **/
    BarrierGate queryEntity(String id);
}
