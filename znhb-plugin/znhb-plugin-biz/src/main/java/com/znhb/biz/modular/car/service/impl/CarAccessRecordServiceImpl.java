/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package com.znhb.biz.modular.car.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.znhb.biz.core.enums.CommonEnum;
import com.znhb.biz.core.enums.OutStatusEnum;
import com.znhb.biz.core.enums.SyncStatusEnum;
import com.znhb.biz.modular.barrierGate.entity.BarrierGate;
import com.znhb.biz.modular.barrierGate.service.BarrierGateService;
import com.znhb.biz.modular.car.entity.CarAccessRecord;
import com.znhb.biz.modular.car.entity.CarInfo;
import com.znhb.biz.modular.car.mapper.CarAccessRecordMapper;
import com.znhb.biz.modular.car.param.*;
import com.znhb.biz.modular.car.service.CarAccessRecordService;
import com.znhb.biz.modular.car.service.CarInfoService;
import com.znhb.common.enums.CommonSortOrderEnum;
import com.znhb.common.exception.CommonException;
import com.znhb.common.page.CommonPageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 小车进出记录Service接口实现类
 *
 * <AUTHOR>
 * @date 2025/04/10 15:00
 **/
@Service
public class CarAccessRecordServiceImpl extends ServiceImpl<CarAccessRecordMapper, CarAccessRecord> implements CarAccessRecordService {

    @Resource
    private CarInfoService carInfoService;

    @Resource
    private BarrierGateService barrierGateService;

    @Override
    public Page<CarAccessRecord> page(CarAccessRecordPageParam param) {
        QueryWrapper<CarAccessRecord> queryWrapper = new QueryWrapper<>();
        // 根据车牌号码模糊查询
        if (ObjectUtil.isNotEmpty(param.getPlateNumber())) {
            queryWrapper.lambda().like(CarAccessRecord::getPlateNumber, param.getPlateNumber());
        }
        // 根据小车ID查询
        if (ObjectUtil.isNotEmpty(param.getCarId())) {
            queryWrapper.lambda().like(CarAccessRecord::getCarId, param.getCarId());
        }
        // 根据状态查询
        if (ObjectUtil.isNotEmpty(param.getStatus())) {
            queryWrapper.lambda().like(CarAccessRecord::getStatus, param.getStatus());
        }
        // 根据进厂时间范围查询
        if (ObjectUtil.isNotEmpty(param.getEntryTimeStart())) {
            queryWrapper.lambda().gt(CarAccessRecord::getEntryTime, param.getEntryTimeStart());
        }
        if (ObjectUtil.isNotEmpty(param.getEntryTimeEnd())) {
            queryWrapper.lambda().lt(CarAccessRecord::getEntryTime, param.getEntryTimeEnd());
        }
        // 根据出厂时间范围查询
        if (ObjectUtil.isNotEmpty(param.getExitTimeStart())) {
            queryWrapper.lambda().gt(CarAccessRecord::getExitTime, param.getExitTimeStart());
        }
        if (ObjectUtil.isNotEmpty(param.getExitTimeEnd())) {
            queryWrapper.lambda().lt(CarAccessRecord::getExitTime, param.getExitTimeEnd());
        }
        // 排序
        if (ObjectUtil.isAllNotEmpty(param.getSortField(), param.getSortOrder())) {
            CommonSortOrderEnum.validate(param.getSortOrder());
            queryWrapper.orderBy(true, param.getSortOrder().equals(CommonSortOrderEnum.ASC.getValue()),
                    StrUtil.toUnderlineCase(param.getSortField()));
        } else {
            // 默认按进厂时间倒序
            queryWrapper.lambda().orderByDesc(CarAccessRecord::getEntryTime);
        }
        return this.page(CommonPageRequest.defaultPage(), queryWrapper);
    }

    @Override
    public void addEntry(CarAccessRecordAddParam param) {
        // 校验参数
        checkEntryParam(param);

        // 实体转换
        CarAccessRecord carAccessRecord = BeanUtil.toBean(param, CarAccessRecord.class);

        // 设置默认值
        carAccessRecord.setStatus(OutStatusEnum.NOT_OUT.getCode()); // 0-未出厂
        if (carAccessRecord.getEntryTime() == null) {
            carAccessRecord.setEntryTime(new Date()); // 设置当前时间为进厂时间
        }

        if (ObjectUtil.isNotEmpty(carAccessRecord.getEntryGateId())) {
            //查询道闸信息
            BarrierGate barrierGate = barrierGateService.getById(carAccessRecord.getEntryGateId());
            if (ObjectUtil.isNotEmpty(barrierGate)) {
                carAccessRecord.setEntryGate(barrierGate.getName());
                carAccessRecord.setEntryCaptureDeviceId(barrierGate.getBodyCaptureCameraId());
            }
        }


        // 保存
        this.save(carAccessRecord);
        param.setId(carAccessRecord.getId());
    }

    @Override
    public void registerExit(CarAccessRecordExitParam param) {

        // 获取原始数据
        CarAccessRecord carAccessRecord = this.queryEntity(param.getId());

        // 校验出厂状态
        if (OutStatusEnum.OUTED.getCode().equals(carAccessRecord.getStatus())) {
            throw new CommonException(CommonEnum.CAR_HAS_EXIT.getCodeMessage());
        }

        // 设置出厂信息
        Date exitTime = param.getExitTime() != null ? param.getExitTime() : new Date();
        carAccessRecord.setExitTime(exitTime);
        carAccessRecord.setExitGate(param.getExitGate());
        carAccessRecord.setExitImageUrl(param.getExitImageUrl());
        carAccessRecord.setStatus(OutStatusEnum.OUTED.getCode());
        carAccessRecord.setExitGateId(param.getExitGateId());
        carAccessRecord.setOutOpenGateType(param.getOutOpenGateType());
        carAccessRecord.setRemark(param.getRemark());
        if (ObjectUtil.isNotEmpty(param.getExitGateId())) {
            //查询道闸信息
            BarrierGate barrierGate = barrierGateService.getById(param.getExitGateId());
            if (ObjectUtil.isNotEmpty(barrierGate)) {
                carAccessRecord.setExitGate(barrierGate.getName());
                carAccessRecord.setExitCaptureDeviceId(barrierGate.getBodyCaptureCameraId());
            }
        }

        // 计算停留时长（分钟）
        if (carAccessRecord.getEntryTime() != null) {
            long stayDuration = (carAccessRecord.getExitTime().getTime() - carAccessRecord.getEntryTime().getTime()) / (1000 * 60);
            carAccessRecord.setStayDuration((int) stayDuration);
        }

        // 更新
        this.updateById(carAccessRecord);
    }

    @Override
    public void edit(CarAccessRecordEditParam param) {
        // 校验参数
        if (StrUtil.isEmpty(param.getId())) {
            throw new CommonException("记录ID不能为空");
        }

        // 获取原始数据
        CarAccessRecord carAccessRecord = this.queryEntity(param.getId());

        // 实体转换
        BeanUtil.copyProperties(param, carAccessRecord);

        // 如果修改了进出厂时间，重新计算停留时长
        if (carAccessRecord.getEntryTime() != null && carAccessRecord.getExitTime() != null) {
            long stayDuration = (carAccessRecord.getExitTime().getTime() - carAccessRecord.getEntryTime().getTime()) / (1000 * 60);
            carAccessRecord.setStayDuration((int) stayDuration);
        }

        // 更新
        this.updateById(carAccessRecord);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<CarAccessRecordIdParam> paramList) {
        // 获取ID列表
        List<String> idList = CollStreamUtil.toList(paramList, CarAccessRecordIdParam::getId);

        // 逻辑删除
        this.removeByIds(idList);
    }

    @Override
    public CarAccessRecord detail(CarAccessRecordIdParam param) {
        return this.queryEntity(param.getId());
    }

    /**
     * 校验进厂参数
     *
     * @param param 参数
     */
    private void checkEntryParam(CarAccessRecordAddParam param) {
        // 校验车牌号码是否为空
        if (StrUtil.isEmpty(param.getPlateNumber())) {
            throw new CommonException(CommonEnum.PLATE_NUMBER_NOT_ALLOWED_EMPTY.getCodeMessage());
        }

        // 校验车辆是否存在
        if (StrUtil.isNotEmpty(param.getCarId())) {
            CarInfo carInfo = carInfoService.getById(param.getCarId());
            if (ObjectUtil.isEmpty(carInfo)) {
                throw new CommonException(CommonEnum.CAR_NOT_EXIST.getCodeMessage());
            }

            // 校验车辆状态
            if (!CommonEnum.STATUS_OPEN.getCodeMessage().equals(carInfo.getStatus())) {
                throw new CommonException(CommonEnum.CAR_STATUS_NOT_ENABLE.getCodeMessage());
            }
        }

        // 校验该车辆是否已在厂内（未出厂）
        QueryWrapper<CarAccessRecord> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(CarAccessRecord::getPlateNumber, param.getPlateNumber())
                .eq(CarAccessRecord::getStatus, "0"); // 0-未出厂
        if (this.count(queryWrapper) > 0) {
            throw new CommonException(CommonEnum.CAR_HAS_ENTRY.getCodeMessage());
        }
    }

    /**
     * 获取小车进出记录
     *
     * @param id 小车进出记录ID
     * @return 小车进出记录
     */
    private CarAccessRecord queryEntity(String id) {
        CarAccessRecord carAccessRecord = this.getById(id);
        if (ObjectUtil.isEmpty(carAccessRecord)) {
            throw new CommonException("小车进出记录不存在");
        }
        return carAccessRecord;
    }

    @Override
    public CarAccessRecord queryLastInRecord(String plateNumber) {
        LambdaQueryWrapper<CarAccessRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CarAccessRecord::getPlateNumber, plateNumber);
        queryWrapper.eq(CarAccessRecord::getStatus, OutStatusEnum.NOT_OUT.getCode()); // 0-未出厂
        queryWrapper.orderByDesc(CarAccessRecord::getEntryTime);
        queryWrapper.last("limit 1");
        return this.getOne(queryWrapper);
    }

    @Override
    public long getTodayEntryCount(String plateNumber) {
        // 获取当天开始时间和结束时间
        Date beginOfDay = DateUtil.beginOfDay(new Date());
        Date endOfDay = DateUtil.endOfDay(new Date());

        LambdaQueryWrapper<CarAccessRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CarAccessRecord::getPlateNumber, plateNumber);
        queryWrapper.ge(CarAccessRecord::getEntryTime, beginOfDay);
        queryWrapper.lt(CarAccessRecord::getEntryTime, endOfDay);
        return this.count(queryWrapper);
    }

    @Override
    public List<CarAccessRecord> queryCarAccessRecordForSync() {
        LambdaQueryWrapper<CarAccessRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CarAccessRecord::getStatus, OutStatusEnum.OUTED.getCode());
        queryWrapper.eq(CarAccessRecord::getSyncStatus, SyncStatusEnum.NOT_SYNC.getCode());
        queryWrapper.ge(CarAccessRecord::getUpdateTime, DateUtil.beginOfDay(new Date()));
        queryWrapper.le(CarAccessRecord::getUpdateTime, DateUtil.endOfDay(new Date()));
        return this.list(queryWrapper);
    }

    @Override
    public void updateForSync(String id) {
        UpdateWrapper<CarAccessRecord> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda().eq(CarAccessRecord::getId, id).set(CarAccessRecord::getSyncStatus, SyncStatusEnum.SYNCED.getCode());
        this.update(updateWrapper);
    }
}
