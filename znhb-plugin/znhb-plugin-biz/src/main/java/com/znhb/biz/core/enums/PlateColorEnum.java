package com.znhb.biz.core.enums;


import lombok.Getter;

@Getter
public enum PlateColorEnum {

    YELLOW("yellow", "黄"),

    BLUE("blue", "蓝"),

    GREEN("green", "绿");

    private final String colorCode;
    private final String colorName;

    public final static String PLATE_COLOR = "PLATE_COLOR";

    PlateColorEnum(String colorCode, String colorName) {
        this.colorCode = colorCode;
        this.colorName = colorName;
    }

    public static String getColorName(String colorCode) {
        for (PlateColorEnum plateColorEnum : PlateColorEnum.values()) {
            if (plateColorEnum.getColorCode().equals(colorCode)) {
                return plateColorEnum.getColorName();
            }
        }
        return null;
    }
}
