/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package com.znhb.biz.modular.car.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;

/**
 * 小车信息编辑参数
 *
 * <AUTHOR>
 * @date  2025/04/10 15:00
 **/
@Getter
@Setter
public class CarInfoEditParam {

    /** id */
    @ApiModelProperty(value = "id", required = true, position = 1)
    @NotBlank(message = "id不能为空")
    private String id;

    /** 车牌号码 */
    @ApiModelProperty(value = "车牌号码", required = true, position = 2)
    @NotBlank(message = "车牌号码不能为空")
    private String plateNumber;

    /** 车牌颜色 */
    @ApiModelProperty(value = "车牌颜色", position = 3)
    private String plateColor;

    /** 车主姓名 */
    @ApiModelProperty(value = "车主姓名", position = 4)
    private String ownerName;

    /** 车主电话 */
    @ApiModelProperty(value = "车主电话", position = 5)
    private String ownerPhone;

    /** 车主身份证号 */
    @ApiModelProperty(value = "车主身份证号", position = 6)
    private String ownerIdCard;

    /** 机构id */
    @ApiModelProperty(value = "机构id", position = 7)
    private String orgId;

    /** 机构名称 */
    @ApiModelProperty(value = "机构名称", position = 8)
    private String orgName;

    /** 状态(0-禁用,1-启用) */
    @ApiModelProperty(value = "状态(0-禁用,1-启用)", position = 9)
    private String status;

    /** 备注 */
    @ApiModelProperty(value = "备注", position = 10)
    private String remark;

    /** 有效期时间范围 */
    @ApiModelProperty(value = "有效期时间范围", position = 11)
    private String validityPeriodTimeRange;

    /** 禁用原因 */
    @ApiModelProperty(value = "禁用原因", position = 12)
    private String disableReason;

    /** 驾驶证照片 */
    @ApiModelProperty(value = "驾驶证照片", position = 14)
    private String driverLicenseImgUrl;

    /** 行驶证照片 */
    @ApiModelProperty(value = "行驶证照片", position = 15)
    private String vehicleLicenseImgUrl;

    /** 车身照片 */
    @ApiModelProperty(value = "车身照片", position = 16)
    private String carBodyImgUrl;

    /** 其他照片 */
    @ApiModelProperty(value = "其他照片", position = 17)
    private String otherImgUrl;
}
