/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package com.znhb.biz.modular.ledConfig.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

/**
 * LED配置信息表编辑参数
 *
 * <AUTHOR>
 * @date  2025/05/12 16:23
 **/
@Getter
@Setter
public class LedConfigEditParam {

    /** LED配置ID */
    @ApiModelProperty(value = "LED配置ID", required = true, position = 1)
    @NotBlank(message = "id不能为空")
    private String id;

    /** 配置名称 */
    @ApiModelProperty(value = "配置名称", position = 2)
    private String name;

    /** 第一行文字颜色 */
    @ApiModelProperty(value = "第一行文字颜色", position = 4)
    private String firstLineColor;

    /** 第二行文字颜色 */
    @ApiModelProperty(value = "第二行文字颜色", position = 6)
    private String secondLineColor;

    /** 第三行显示文字 */
    @ApiModelProperty(value = "第三行显示文字", position = 7)
    private String thirdLineText;

    /** 第三行文字颜色 */
    @ApiModelProperty(value = "第三行文字颜色", position = 8)
    private String thirdLineColor;

    /** 第四行显示文字 */
    @ApiModelProperty(value = "第四行显示文字", position = 9)
    private String fourthLineText;

    /** 第四行文字颜色 */
    @ApiModelProperty(value = "第四行文字颜色", position = 10)
    private String fourthLineColor;

    /** 状态：0-禁用，1-启用 */
    @ApiModelProperty(value = "状态：0-禁用，1-启用", position = 11)
    private Boolean status;

    /** 第三行显示文字类型 */
    @ApiModelProperty(value = "第三行显示文字类型")
    private String thirdLineType;

    /** 第四行显示文字类型 */
    @ApiModelProperty(value = "第四行显示文字类型")
    private String fourthLineType;

    /** code */
    @ApiModelProperty(value = "code")
    private String code;

}
