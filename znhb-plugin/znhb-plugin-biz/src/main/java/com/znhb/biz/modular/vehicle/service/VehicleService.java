/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package com.znhb.biz.modular.vehicle.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.znhb.biz.modular.vehicle.entity.Vehicle;
import com.znhb.biz.modular.vehicle.param.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;

/**
 * 车辆信息Service接口
 *
 * <AUTHOR>
 * @date  2025/03/13 14:34
 **/
public interface VehicleService extends IService<Vehicle> {

    /**
     * 获取车辆信息分页
     *
     * <AUTHOR>
     * @date  2025/03/13 14:34
     */
    Page<Vehicle> page(VehiclePageParam vehiclePageParam);

    /**
     * 添加车辆信息
     *
     * <AUTHOR>
     * @date  2025/03/13 14:34
     */
    void add(VehicleAddParam vehicleAddParam);

    /**
     * 编辑车辆信息
     *
     * <AUTHOR>
     * @date  2025/03/13 14:34
     */
    void edit(VehicleEditParam vehicleEditParam);

    /**
     * 删除车辆信息
     *
     * <AUTHOR>
     * @date  2025/03/13 14:34
     */
    void delete(List<VehicleIdParam> vehicleIdParamList);

    /**
     * 获取车辆信息详情
     *
     * <AUTHOR>
     * @date  2025/03/13 14:34
     */
    Vehicle detail(VehicleIdParam vehicleIdParam);

    /**
     * 获取车辆信息详情
     *
     * <AUTHOR>
     * @date  2025/03/13 14:34
     */
    Vehicle detail(VehicleNumberParam vehicleNumberParam);

    /**
     * 获取车辆信息详情
     *
     * <AUTHOR>
     * @date  2025/03/13 14:34
     **/
    Vehicle queryEntity(String id);

    /**
     * 获取车辆信息详情
     *
     * <AUTHOR>
     * @date  2025/03/13 14:34
     **/
    Vehicle queryNumberEntity(String plateNumber);

    /**
     * 车辆审核
     *
     * @param vehicleAuditParam 审核参数
     */
    void audit(VehicleAuditParam vehicleAuditParam);

    /**
     * 车辆导出
     *
     * @param vehiclePageParam 车辆参数
     */
    void export(VehiclePageParam vehiclePageParam, HttpServletResponse response);

    /**
     * 车辆查询车牌号
     *
     * @param vehiclePlateNumberParam

     */
    Vehicle loadVehicleByPlateNumber(VehicleNumberAndColorParam vehiclePlateNumberParam);

    /**
     *  车辆查询车牌号
     * @param vehicleNumberParam
     * @return
     */
    Object loadVehicleForOrder(VehicleNumberParam vehicleNumberParam);

    /**
     * 车辆备案同步
     * @return
     * <AUTHOR>
     * @date  2025/03/13 14:34
     */
    List<Vehicle> queryVehicleForSync();

    /**
     * 车辆备案同步
     * @return
     * <AUTHOR>
     * @date  2025/03/13 14:34
     */
    void updateForSync(String id);
}
