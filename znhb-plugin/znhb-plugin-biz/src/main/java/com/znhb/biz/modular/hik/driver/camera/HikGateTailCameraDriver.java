package com.znhb.biz.modular.hik.driver.camera;

import lombok.extern.slf4j.Slf4j;
import com.znhb.biz.core.enums.DeviceTypeEnum;

/**
 * 海康车尾抓拍机
 */
@Slf4j
public class HikGateTailCameraDriver extends HikBaseCameraDriver{

    private final static String NAME = "海康车尾识别机驱动";

    public HikGateTailCameraDriver() {
        super();
        defendJudge = true;
    }

    @Override
    public String type() {
        return DeviceTypeEnum.TAIL_CAMERA.getCode();
    }
}
