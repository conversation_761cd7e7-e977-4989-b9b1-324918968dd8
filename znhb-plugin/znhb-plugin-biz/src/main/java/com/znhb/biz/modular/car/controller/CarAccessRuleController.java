/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package com.znhb.biz.modular.car.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import com.znhb.biz.modular.car.entity.CarAccessRule;
import com.znhb.biz.modular.car.param.CarAccessRuleAddParam;
import com.znhb.biz.modular.car.param.CarAccessRuleEditParam;
import com.znhb.biz.modular.car.param.CarAccessRuleIdParam;
import com.znhb.biz.modular.car.param.CarAccessRulePageParam;
import com.znhb.biz.modular.car.service.CarAccessRuleService;
import com.znhb.common.annotation.CommonLog;
import com.znhb.common.pojo.CommonResult;
import com.znhb.common.pojo.CommonValidList;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 小车进出规则控制器
 *
 * <AUTHOR>
 * @date  2025/04/10 15:00
 **/
@Api(tags = "小车进出规则控制器")
@ApiSupport(author = "SNOWY_TEAM", order = 1)
@RestController
@Validated
public class CarAccessRuleController {

    @Resource
    private CarAccessRuleService carAccessRuleService;

    /**
     * 获取小车进出规则分页
     *
     * @param carAccessRulePageParam 查询参数
     * @return 查询结果
     */
    @ApiOperationSupport(order = 1)
    @ApiOperation("获取小车进出规则分页")
    @SaCheckPermission("/biz/car/accessRule/page")
    @GetMapping("/biz/car/accessRule/page")
    public CommonResult<Page<CarAccessRule>> page(CarAccessRulePageParam carAccessRulePageParam) {
        return CommonResult.data(carAccessRuleService.page(carAccessRulePageParam));
    }

    /**
     * 添加小车进出规则
     *
     * @param carAccessRuleAddParam 添加参数
     * @return 添加结果
     */
    @ApiOperationSupport(order = 2)
    @ApiOperation("添加小车进出规则")
    @CommonLog("添加小车进出规则")
    @SaCheckPermission("/biz/car/accessRule/add")
    @PostMapping("/biz/car/accessRule/add")
    public CommonResult<String> add(@RequestBody @Valid CarAccessRuleAddParam carAccessRuleAddParam) {
        carAccessRuleService.add(carAccessRuleAddParam);
        return CommonResult.ok();
    }

    /**
     * 编辑小车进出规则
     *
     * @param carAccessRuleEditParam 编辑参数
     * @return 编辑结果
     */
    @ApiOperationSupport(order = 3)
    @ApiOperation("编辑小车进出规则")
    @CommonLog("编辑小车进出规则")
    @SaCheckPermission("/biz/car/accessRule/edit")
    @PostMapping("/biz/car/accessRule/edit")
    public CommonResult<String> edit(@RequestBody @Valid CarAccessRuleEditParam carAccessRuleEditParam) {
        carAccessRuleService.edit(carAccessRuleEditParam);
        return CommonResult.ok();
    }

    /**
     * 删除小车进出规则
     *
     * @param carAccessRuleIdParam 删除参数
     * @return 删除结果
     */
    @ApiOperationSupport(order = 4)
    @ApiOperation("删除小车进出规则")
    @CommonLog("删除小车进出规则")
    @SaCheckPermission("/biz/car/accessRule/delete")
    @PostMapping("/biz/car/accessRule/delete")
    public CommonResult<String> delete(@RequestBody @Valid CarAccessRuleIdParam carAccessRuleIdParam) {
        carAccessRuleService.delete(carAccessRuleIdParam);
        return CommonResult.ok();
    }

    /**
     * 批量删除小车进出规则
     *
     * @param carAccessRuleIdParamList 批量删除参数
     * @return 批量删除结果
     */
    @ApiOperationSupport(order = 5)
    @ApiOperation("批量删除小车进出规则")
    @CommonLog("批量删除小车进出规则")
    @SaCheckPermission("/biz/car/accessRule/batchDelete")
    @PostMapping("/biz/car/accessRule/batchDelete")
    public CommonResult<String> batchDelete(@RequestBody @Valid @NotEmpty(message = "集合不能为空")
                                                CommonValidList<CarAccessRuleIdParam> carAccessRuleIdParamList) {
        carAccessRuleIdParamList.forEach(carAccessRuleService::delete);
        return CommonResult.ok();
    }

    /**
     * 获取小车进出规则详情
     *
     * @param carAccessRuleIdParam 查询参数
     * @return 查询结果
     */
    @ApiOperationSupport(order = 6)
    @ApiOperation("获取小车进出规则详情")
    @SaCheckPermission("/biz/car/accessRule/detail")
    @GetMapping("/biz/car/accessRule/detail")
    public CommonResult<CarAccessRule> detail(@Valid CarAccessRuleIdParam carAccessRuleIdParam) {
        return CommonResult.data(carAccessRuleService.detail(carAccessRuleIdParam));
    }

    /**
     * 获取小车进出规则列表
     *
     * @return 查询结果
     */
    @ApiOperationSupport(order = 7)
    @ApiOperation("获取小车进出规则列表")
    @SaCheckPermission("/biz/car/accessRule/list")
    @GetMapping("/biz/car/accessRule/list")
    public CommonResult<List<CarAccessRule>> list() {
        return CommonResult.data(carAccessRuleService.list());
    }
}
