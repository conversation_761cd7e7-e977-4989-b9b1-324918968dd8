/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package com.znhb.biz.modular.car.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import com.znhb.biz.modular.car.entity.CarAccessRecord;
import com.znhb.biz.modular.car.param.*;
import com.znhb.biz.modular.car.service.CarAccessRecordService;
import com.znhb.common.annotation.CommonLog;
import com.znhb.common.pojo.CommonResult;
import com.znhb.common.pojo.CommonValidList;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 小车进出记录控制器
 *
 * <AUTHOR>
 * @date  2025/04/10 15:00
 **/
@Api(tags = "小车进出记录控制器")
@ApiSupport(author = "ZNHB_TEAM", order = 2)
@RestController
@Validated
public class CarAccessRecordController {

    @Resource
    private CarAccessRecordService carAccessRecordService;

    /**
     * 获取小车进出记录分页
     *
     * @param carAccessRecordPageParam 查询参数
     * @return 查询结果
     */
    @ApiOperation("获取小车进出记录分页")
    @ApiOperationSupport(order = 1)
    @SaCheckPermission("/biz/car/access/page")
    @GetMapping("/biz/car/access/page")
    public CommonResult<Page<CarAccessRecord>> page(CarAccessRecordPageParam carAccessRecordPageParam) {
        return CommonResult.data(carAccessRecordService.page(carAccessRecordPageParam));
    }

    /**
     * 添加小车进厂记录
     *
     * @param carAccessRecordAddParam 添加参数
     * @return 添加结果
     */
    @ApiOperation("添加小车进厂记录")
    @ApiOperationSupport(order = 2)
    @CommonLog("添加小车进厂记录")
    @SaCheckPermission("/biz/car/access/entry")
    @PostMapping("/biz/car/access/entry")
    public CommonResult<String> addEntry(@RequestBody @Valid CarAccessRecordAddParam carAccessRecordAddParam) {
        carAccessRecordService.addEntry(carAccessRecordAddParam);
        return CommonResult.ok();
    }

    /**
     * 登记小车出厂
     *
     * @param carAccessRecordExitParam 出厂参数
     * @return 登记结果
     */
    @ApiOperation("登记小车出厂")
    @ApiOperationSupport(order = 3)
    @CommonLog("登记小车出厂")
    @SaCheckPermission("/biz/car/access/exit")
    @PostMapping("/biz/car/access/exit")
    public CommonResult<String> registerExit(@RequestBody @Valid CarAccessRecordExitParam carAccessRecordExitParam) {
        carAccessRecordService.registerExit(carAccessRecordExitParam);
        return CommonResult.ok();
    }

    /**
     * 编辑小车进出记录
     *
     * @param carAccessRecordEditParam 编辑参数
     * @return 编辑结果
     */
    @ApiOperation("编辑小车进出记录")
    @ApiOperationSupport(order = 4)
    @CommonLog("编辑小车进出记录")
    @SaCheckPermission("/biz/car/access/edit")
    @PostMapping("/biz/car/access/edit")
    public CommonResult<String> edit(@RequestBody @Valid CarAccessRecordEditParam carAccessRecordEditParam) {
        carAccessRecordService.edit(carAccessRecordEditParam);
        return CommonResult.ok();
    }

    /**
     * 删除小车进出记录
     *
     * @param carAccessRecordIdParamList 删除参数
     * @return 删除结果
     */
    @ApiOperation("删除小车进出记录")
    @ApiOperationSupport(order = 5)
    @CommonLog("删除小车进出记录")
    @SaCheckPermission("/biz/car/access/delete")
    @PostMapping("/biz/car/access/delete")
    public CommonResult<String> delete(@RequestBody @NotEmpty(message = "参数不能为空")
                                     @Valid CommonValidList<CarAccessRecordIdParam> carAccessRecordIdParamList) {
        carAccessRecordService.delete(carAccessRecordIdParamList);
        return CommonResult.ok();
    }

    /**
     * 获取小车进出记录详情
     *
     * @param carAccessRecordIdParam 查询参数
     * @return 查询结果
     */
    @ApiOperation("获取小车进出记录详情")
    @ApiOperationSupport(order = 6)
    @SaCheckPermission("/biz/car/access/detail")
    @GetMapping("/biz/car/access/detail")
    public CommonResult<CarAccessRecord> detail(@Valid CarAccessRecordIdParam carAccessRecordIdParam) {
        return CommonResult.data(carAccessRecordService.detail(carAccessRecordIdParam));
    }
}
