/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package com.znhb.biz.modular.ledConfig.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import com.znhb.common.annotation.CommonLog;
import com.znhb.common.pojo.CommonResult;
import com.znhb.common.pojo.CommonValidList;
import com.znhb.biz.modular.ledConfig.entity.LedConfig;
import com.znhb.biz.modular.ledConfig.param.LedConfigAddParam;
import com.znhb.biz.modular.ledConfig.param.LedConfigEditParam;
import com.znhb.biz.modular.ledConfig.param.LedConfigIdParam;
import com.znhb.biz.modular.ledConfig.param.LedConfigPageParam;
import com.znhb.biz.modular.ledConfig.service.LedConfigService;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;

/**
 * LED配置信息表控制器
 *
 * <AUTHOR>
 * @date  2025/05/12 16:23
 */
@Api(tags = "LED配置信息表控制器")
@ApiSupport(author = "SNOWY_TEAM", order = 1)
@RestController
@Validated
public class LedConfigController {

    @Resource
    private LedConfigService ledConfigService;

    /**
     * 获取LED配置信息表分页
     *
     * <AUTHOR>
     * @date  2025/05/12 16:23
     */
    @ApiOperationSupport(order = 1)
    @ApiOperation("获取LED配置信息表分页")
    @SaCheckPermission("/biz/ledConfig/page")
    @GetMapping("/biz/ledConfig/page")
    public CommonResult<Page<LedConfig>> page(LedConfigPageParam ledConfigPageParam) {
        return CommonResult.data(ledConfigService.page(ledConfigPageParam));
    }

    /**
     * 添加LED配置信息表
     *
     * <AUTHOR>
     * @date  2025/05/12 16:23
     */
    @ApiOperationSupport(order = 2)
    @ApiOperation("添加LED配置信息表")
    @CommonLog("添加LED配置信息表")
    @SaCheckPermission("/biz/ledConfig/add")
    @PostMapping("/biz/ledConfig/add")
    public CommonResult<String> add(@RequestBody @Valid LedConfigAddParam ledConfigAddParam) {
        ledConfigService.add(ledConfigAddParam);
        return CommonResult.ok();
    }

    /**
     * 编辑LED配置信息表
     *
     * <AUTHOR>
     * @date  2025/05/12 16:23
     */
    @ApiOperationSupport(order = 3)
    @ApiOperation("编辑LED配置信息表")
    @CommonLog("编辑LED配置信息表")
    @SaCheckPermission("/biz/ledConfig/edit")
    @PostMapping("/biz/ledConfig/edit")
    public CommonResult<String> edit(@RequestBody @Valid LedConfigEditParam ledConfigEditParam) {
        ledConfigService.edit(ledConfigEditParam);
        return CommonResult.ok();
    }

    /**
     * 删除LED配置信息表
     *
     * <AUTHOR>
     * @date  2025/05/12 16:23
     */
    @ApiOperationSupport(order = 4)
    @ApiOperation("删除LED配置信息表")
    @CommonLog("删除LED配置信息表")
    @SaCheckPermission("/biz/ledConfig/delete")
    @PostMapping("/biz/ledConfig/delete")
    public CommonResult<String> delete(@RequestBody @Valid @NotEmpty(message = "集合不能为空")
                                                   CommonValidList<LedConfigIdParam> ledConfigIdParamList) {
        ledConfigService.delete(ledConfigIdParamList);
        return CommonResult.ok();
    }

    /**
     * 获取LED配置信息表详情
     *
     * <AUTHOR>
     * @date  2025/05/12 16:23
     */
    @ApiOperationSupport(order = 5)
    @ApiOperation("获取LED配置信息表详情")
    @SaCheckPermission("/biz/ledConfig/detail")
    @GetMapping("/biz/ledConfig/detail")
    public CommonResult<LedConfig> detail(@Valid LedConfigIdParam ledConfigIdParam) {
        return CommonResult.data(ledConfigService.detail(ledConfigIdParam));
    }
}
