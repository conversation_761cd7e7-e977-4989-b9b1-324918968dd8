/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package com.znhb.biz.modular.nonRoadRecord.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import com.znhb.common.annotation.CommonLog;
import com.znhb.common.pojo.CommonResult;
import com.znhb.common.pojo.CommonValidList;
import com.znhb.biz.modular.nonRoadRecord.entity.NonRoadRecord;
import com.znhb.biz.modular.nonRoadRecord.param.NonRoadRecordAddParam;
import com.znhb.biz.modular.nonRoadRecord.param.NonRoadRecordEditParam;
import com.znhb.biz.modular.nonRoadRecord.param.NonRoadRecordIdParam;
import com.znhb.biz.modular.nonRoadRecord.param.NonRoadRecordPageParam;
import com.znhb.biz.modular.nonRoadRecord.service.NonRoadRecordService;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;

/**
 * 非移机械台账控制器
 *
 * <AUTHOR>
 * @date  2025/04/03 11:32
 */
@Api(tags = "非移机械台账控制器")
@ApiSupport(author = "SNOWY_TEAM", order = 1)
@RestController
@Validated
public class NonRoadRecordController {

    @Resource
    private NonRoadRecordService nonRoadRecordService;

    /**
     * 获取非移机械台账分页
     *
     * <AUTHOR>
     * @date  2025/04/03 11:32
     */
    @ApiOperationSupport(order = 1)
    @ApiOperation("获取非移机械台账分页")
    @SaCheckPermission("/biz/nonRoadRecord/page")
    @GetMapping("/biz/nonRoadRecord/page")
    public CommonResult<Page<NonRoadRecord>> page(NonRoadRecordPageParam nonRoadRecordPageParam) {
        return CommonResult.data(nonRoadRecordService.page(nonRoadRecordPageParam));
    }

    /**
     * 添加非移机械台账
     *
     * <AUTHOR>
     * @date  2025/04/03 11:32
     */
    @ApiOperationSupport(order = 2)
    @ApiOperation("添加非移机械台账")
    @CommonLog("添加非移机械台账")
    @SaCheckPermission("/biz/nonRoadRecord/add")
    @PostMapping("/biz/nonRoadRecord/add")
    public CommonResult<String> add(@RequestBody @Valid NonRoadRecordAddParam nonRoadRecordAddParam) {
        nonRoadRecordService.add(nonRoadRecordAddParam);
        return CommonResult.ok();
    }

    /**
     * 编辑非移机械台账
     *
     * <AUTHOR>
     * @date  2025/04/03 11:32
     */
    @ApiOperationSupport(order = 3)
    @ApiOperation("编辑非移机械台账")
    @CommonLog("编辑非移机械台账")
    @SaCheckPermission("/biz/nonRoadRecord/edit")
    @PostMapping("/biz/nonRoadRecord/edit")
    public CommonResult<String> edit(@RequestBody @Valid NonRoadRecordEditParam nonRoadRecordEditParam) {
        nonRoadRecordService.edit(nonRoadRecordEditParam);
        return CommonResult.ok();
    }

    /**
     * 删除非移机械台账
     *
     * <AUTHOR>
     * @date  2025/04/03 11:32
     */
    @ApiOperationSupport(order = 4)
    @ApiOperation("删除非移机械台账")
    @CommonLog("删除非移机械台账")
    @SaCheckPermission("/biz/nonRoadRecord/delete")
    @PostMapping("/biz/nonRoadRecord/delete")
    public CommonResult<String> delete(@RequestBody @Valid @NotEmpty(message = "集合不能为空")
                                                   CommonValidList<NonRoadRecordIdParam> nonRoadRecordIdParamList) {
        nonRoadRecordService.delete(nonRoadRecordIdParamList);
        return CommonResult.ok();
    }

    /**
     * 获取非移机械台账详情
     *
     * <AUTHOR>
     * @date  2025/04/03 11:32
     */
    @ApiOperationSupport(order = 5)
    @ApiOperation("获取非移机械台账详情")
    @SaCheckPermission("/biz/nonRoadRecord/detail")
    @GetMapping("/biz/nonRoadRecord/detail")
    public CommonResult<NonRoadRecord> detail(@Valid NonRoadRecordIdParam nonRoadRecordIdParam) {
        return CommonResult.data(nonRoadRecordService.detail(nonRoadRecordIdParam));
    }
}
