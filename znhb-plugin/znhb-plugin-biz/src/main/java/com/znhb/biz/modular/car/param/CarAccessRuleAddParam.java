/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package com.znhb.biz.modular.car.param;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import java.sql.Time;

/**
 * 小车进出规则添加参数
 *
 * <AUTHOR>
 * @date  2025/04/10 15:00
 **/
@Getter
@Setter
public class CarAccessRuleAddParam {

    /** 规则名称 */
    @ApiModelProperty(value = "规则名称", required = true, position = 1)
    @NotBlank(message = "规则名称不能为空")
    private String ruleName;

    /** 允许进入开始时间 */
    @ApiModelProperty(value = "允许进入开始时间", position = 3)
    private Time startTime;

    /** 允许进入结束时间 */
    @ApiModelProperty(value = "允许进入结束时间", position = 4)
    private Time endTime;

    /** 每日最大进入次数 */
    @ApiModelProperty(value = "每日最大进入次数", position = 5)
    private Integer maxCount;

    /** 允许入道闸 */
    @ApiModelProperty(value = "允许入道闸", position = 6)
    private String allowedInGates;

    /** 允许出道闸 */
    @ApiModelProperty(value = "允许出道闸", position = 6)
    private String allowedOutGates;

    /** 最大停留时长(分钟) */
    @ApiModelProperty(value = "最大停留时长(分钟)", position = 7)
    private Integer maxDuration;

    /** 适用日期 */
    @ApiModelProperty(value = "适用日期", position = 8)
    private String applyDays;

    /** 规则优先级 */
    @ApiModelProperty(value = "规则优先级", position = 9)
    private Integer priority;

    /** 状态 */
    @ApiModelProperty(value = "状态", position = 10)
    private String status;

    /** 备注 */
    @ApiModelProperty(value = "备注", position = 11)
    private String remark;

    /** 启用时间限制 */
    @ApiModelProperty(value = "启用时间限制")
    private boolean enableTimeRule;

    /** 启用次数限制 */
    @ApiModelProperty(value = "启用次数限制")
    private boolean enableCountRule;

    /** 启用区域限制 */
    @ApiModelProperty(value = "启用入厂道闸限制")
    private boolean enableInGateRule;

    /** 启用区域限制 */
    @ApiModelProperty(value = "启用出厂道闸限制")
    private boolean enableOutGateRule;

    /** 启用时长限制 */
    @ApiModelProperty(value = "启用时长限制")
    private boolean enableDurationRule;

    /** 入开闸方式 */
    @ApiModelProperty(value = "入开闸方式")
    private boolean entryGateOpenType;

    /** 出开闸方式 */
    @ApiModelProperty(value = "出开闸方式")
    private boolean exitGateOpenType;

    /** 最小停留时长(分钟) */
    @ApiModelProperty(value = "最小停留时长(分钟)")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Integer minDuration;

}
