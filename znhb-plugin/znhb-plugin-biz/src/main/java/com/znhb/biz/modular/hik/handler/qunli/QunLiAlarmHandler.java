package com.znhb.biz.modular.hik.handler.qunli;


import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.znhb.biz.core.enums.*;
import com.znhb.biz.modular.barrierGate.entity.BarrierGate;
import com.znhb.biz.modular.car.entity.CarAccessRecord;
import com.znhb.biz.modular.car.entity.CarInfo;
import com.znhb.biz.modular.car.param.CarAccessRecordAddParam;
import com.znhb.biz.modular.car.param.CarAccessRecordExitParam;
import com.znhb.biz.modular.car.service.CarAccessRecordService;
import com.znhb.biz.modular.car.service.CarInfoService;
import com.znhb.biz.modular.carRecordDetail.entity.CarRecordDetail;
import com.znhb.biz.modular.carRecordDetail.service.CarRecordDetailService;
import com.znhb.biz.modular.hik.core.CaptureAsyncService;
import com.znhb.biz.modular.hik.core.CommonUtil;
import com.znhb.biz.modular.hik.driver.DeviceDriver;
import com.znhb.biz.modular.hik.driver.DriverManager;
import com.znhb.biz.modular.hik.driver.barrierGate.BarrierGateDriver;
import com.znhb.biz.modular.hik.driver.camera.HikBaseCameraDriver;
import com.znhb.biz.modular.hik.driver.camera.HikGateBodyCameraDriver;
import com.znhb.biz.modular.hik.driver.camera.HikGatePlateCameraDriver;
import com.znhb.biz.modular.hik.driver.camera.HikGateTailCameraDriver;
import com.znhb.biz.modular.hik.entity.CheckParam;
import com.znhb.biz.modular.hik.entity.GoodsMeterInfo;
import com.znhb.biz.modular.hik.entity.MeterParam;
import com.znhb.biz.modular.hik.handler.AlarmHandler;
import com.znhb.biz.modular.hik.handler.CommonApi;
import com.znhb.biz.modular.internalVehicle.entity.InternalVehicle;
import com.znhb.biz.modular.internalVehicle.service.InternalVehicleService;
import com.znhb.biz.modular.internalVehicleRecord.entity.InternalVehicleRecord;
import com.znhb.biz.modular.internalVehicleRecord.service.InternalVehicleRecordService;
import com.znhb.biz.modular.materialdetail.entity.MaterialDetail;
import com.znhb.biz.modular.materialdetail.service.MaterialDetailService;
import com.znhb.biz.modular.order.entity.Order;
import com.znhb.biz.modular.vehicle.entity.Vehicle;
import com.znhb.biz.modular.vehicle.service.VehicleService;
import com.znhb.biz.modular.vehicleRecord.entity.CarRecord;
import com.znhb.biz.modular.vehicleRecord.service.CarRecordService;
import com.znhb.common.util.DictCacheUtil;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.util.Date;

/**
 * 车牌识别逻辑处理
 */
@Component("qunLiAlarmHandler")
@Slf4j
public class QunLiAlarmHandler implements AlarmHandler {

    private final CommonApi commonApi = SpringUtil.getBean(SpringUtil.getProperty("plate.api-name"), CommonApi.class);

    @Resource
    private VehicleService vehicleService;

    @Resource
    private InternalVehicleService internalVehicleService;

    @Resource
    private InternalVehicleRecordService internalVehicleRecordService;

    @Resource
    private CarRecordDetailService carRecordDetailService;

    @Resource
    private CarRecordService carRecordService;

    @Resource
    private MaterialDetailService materialDetailService;

    @Resource
    private DictCacheUtil dictCacheUtil;

    @Resource
    private CarInfoService carInfoService;

    @Resource
    private CarAccessRecordService carAccessRecordService;

    @Resource
    private CaptureAsyncService captureAsyncService;

    /**
     * 识别逻辑处理
     */
    public void handle(String ip, String sLicense, String plateImg, String carImg) {
        CarRecordDetail carRecordDetail = null;
        try {
            DeviceDriver driver = DriverManager.getInstance().getDriverByIp(ip);
            if (driver == null) {
                log.info("未找到摄像头设备驱动：{}", ip);
                return;
            }

            String plateNumber = CommonUtil.parsePlateNumber(sLicense);
            String plateColor = CommonUtil.parsePlateColor(sLicense);
            //车牌抓拍
            if (driver instanceof HikGatePlateCameraDriver) {
                //抓拍摄像头
                HikBaseCameraDriver cameraDriver = (HikBaseCameraDriver) driver;
                //道闸驱动
                BarrierGateDriver barrierDriver = DriverManager.getInstance().getGateDriverByPlateCamera(cameraDriver);

                if (barrierDriver == null) {
                    log.info("未找到道闸驱动：{},{},{}", plateNumber, cameraDriver.getDeviceName(), cameraDriver.getDeviceIp());
                    return;
                }
                BarrierGate barrierGate = barrierDriver.getBarrierGate();
                if (StrUtil.equals(CommonEnum.GATE_STATUS_CLOSE.getCodeMessage(), barrierGate.getGateStatus())) {
                    log.info("道闸未启用：{},{},{}", plateNumber, cameraDriver.getDeviceName(), cameraDriver.getDeviceIp());
                    barrierDriver.displayLedMessage(CommonEnum.GATE_STATUS_NOT_OPEN.getCodeMessage(), CommonEnum.LED_FORBID.getCodeMessage());
                    return;
                }

                //车牌重复校验 30s内重复识别不处理
                if (barrierDriver.plateDuplicateCheck(plateNumber)) {
                    return;
                }

                //插入识别记录
                carRecordDetail = getCarRecordDetail(plateImg, carImg, plateNumber, plateColor, barrierGate, cameraDriver, driver);
                carRecordDetailService.save(carRecordDetail);

                //小车管理
                if (carManage(barrierDriver, carRecordDetail)) return;

//                //外部车辆
//                if (externalVehicleManage(barrierDriver, carRecordDetail)) return;
//
//                //内部车辆
//                if (internalVehicleManage(barrierDriver, carRecordDetail)) return;

                this.handleErrorMessage(barrierDriver, carRecordDetail.getId(), CommonEnum.TEMPORARY_CAR.getCodeMessage());
            }

            //车身抓拍
            if (driver instanceof HikGateBodyCameraDriver) {
                //抓拍摄像头
                HikGateBodyCameraDriver bodyCameraDriver = (HikGateBodyCameraDriver) driver;
                //道闸驱动
                BarrierGateDriver gateDriver = DriverManager.getInstance().getGateDriverByBodyCamera(bodyCameraDriver);
                if (gateDriver == null) {
                    log.info("未找到道闸驱动：{},{},{}", plateNumber, bodyCameraDriver.getDeviceName(), bodyCameraDriver.getDeviceIp());
                    return;
                }
                BarrierGate barrierGate = gateDriver.getBarrierGate();
                if (StrUtil.equals(CommonEnum.GATE_STATUS_CLOSE.getCodeMessage(), barrierGate.getGateStatus())) {
                    log.info("道闸未启用：{},{},{}", plateNumber, bodyCameraDriver.getDeviceName(), bodyCameraDriver.getDeviceIp());
                    gateDriver.displayLedMessage(CommonEnum.GATE_STATUS_NOT_OPEN.getCodeMessage(), CommonEnum.LED_FORBID.getCodeMessage());
                    return;
                }

                //插入识别记录
                carRecordDetail = getCarRecordDetail(plateImg, carImg, plateNumber, plateColor, barrierGate, bodyCameraDriver, driver);
                carRecordDetailService.save(carRecordDetail);
            }

            //车尾抓拍
            if (driver instanceof HikGateTailCameraDriver) {
                //抓拍摄像头
                HikGateTailCameraDriver tailCameraDriver = (HikGateTailCameraDriver) driver;
                //道闸驱动
                BarrierGateDriver gateDriver = DriverManager.getInstance().getGateDriverByTailCamera(tailCameraDriver);
                if (gateDriver == null) {
                    log.info("未找到道闸驱动：{},{},{}", plateNumber, tailCameraDriver.getDeviceName(), tailCameraDriver.getDeviceIp());
                    return;
                }

                BarrierGate barrierGate = gateDriver.getBarrierGate();
                if (StrUtil.equals(CommonEnum.GATE_STATUS_CLOSE.getCodeMessage(), barrierGate.getGateStatus())) {
                    log.info("道闸未启用：{},{},{}", plateNumber, tailCameraDriver.getDeviceName(), tailCameraDriver.getDeviceIp());
                    gateDriver.displayLedMessage(CommonEnum.GATE_STATUS_NOT_OPEN.getCodeMessage(), CommonEnum.LED_FORBID.getCodeMessage());
                    return;
                }

                //插入识别记录
                carRecordDetail = getCarRecordDetail(plateImg, carImg, plateNumber, plateColor, barrierGate, tailCameraDriver, driver);
                carRecordDetailService.save(carRecordDetail);
            }

        } catch (Exception e) {
            log.info("车牌识别处理失败：{}{}", ip, e.getMessage(), e);
            if (carRecordDetail != null) {
                carRecordDetailService.updateDetailResult(carRecordDetail.getId(), e.getMessage());
            }
        }
    }

    //小车管理
    private boolean carManage(BarrierGateDriver barrierDriver, CarRecordDetail carRecordDetail) {
        //车牌号码
        String plateNumber = carRecordDetail.getPlateNumber();
        //进出道闸
        BarrierGate barrierGate = barrierDriver.getBarrierGate();
        //进出方向
        String direct = barrierGate.getDirect();
        CarInfo carInfo = carInfoService.queryByPlateNumber(carRecordDetail.getPlateNumber());
        if (ObjectUtil.isNotEmpty(carInfo)) {
            barrierDriver.setLedDisplayInfo(carInfo.getOrgName(), carInfo.getRuleName(), carInfo.getOwnerName(), null);
            // 检查车辆状态
            if (!CommonEnum.STATUS_OPEN.getCodeMessage().equals(carInfo.getStatus())) {
                log.info("小车状态不可用，车牌号：{}", plateNumber);
                this.handleErrorMessage(barrierDriver, carRecordDetail.getId(), CommonEnum.CAR_STATUS_NOT_ENABLE.getCodeMessage());
                return true;
            }

            // 检查车辆有效期
            if (StrUtil.isNotBlank(carInfo.getValidityPeriodTimeRange())) {
                String[] timeRange = carInfo.getValidityPeriodTimeRange().split(" ~ ");
                if (timeRange.length == 2) {
                    Date startDate = DateUtil.parse(timeRange[0]);
                    Date endDate = DateUtil.parse(timeRange[1]);
                    Date now = new Date();
                    if (now.before(startDate) || now.after(endDate)) {
                        log.info("小车有效期已过期，车牌号：{}", plateNumber);
                        this.handleErrorMessage(barrierDriver, carRecordDetail.getId(), CommonEnum.CAR_EXPIRED.getCodeMessage());
                        return true;
                    }
                }
            }


            // 入厂
            if (StrUtil.equals(direct, CommonEnum.DIRECT_IN.getCodeMessage())) {
                // 检查规则
                CheckParam ruleCheck = commonApi.checkInCarAccessRule(carInfo, barrierGate);
                if (!ruleCheck.getResult()) {
                    log.info("小车规则验证失败，车牌号：{}，原因：{}", plateNumber, ruleCheck.getMessage());
                    this.handleErrorMessage(barrierDriver, carRecordDetail.getId(), ruleCheck.getMessage());
                    return true;
                }

                // 添加入场记录
                CarAccessRecordAddParam recordParam = new CarAccessRecordAddParam();
                recordParam.setCarId(carInfo.getId());
                recordParam.setPlateNumber(plateNumber);
                recordParam.setEntryTime(new Date());
                recordParam.setEntryGateId(barrierGate.getId());
                recordParam.setEntryImageUrl(carRecordDetail.getCarImageUrl());
                recordParam.setInOpenGateType(barrierGate.isGateAutoOpen() ? GateCtrlTypeEnum.AUTO.getCode() : GateCtrlTypeEnum.MANUAL.getCode());
                recordParam.setInRecordType(RecordTypeEnum.AUTO.getCode());
                try {
                    carAccessRecordService.addEntry(recordParam);
                } catch (Exception e) {
                    log.info("添加小车入场记录失败，车牌号：{}，原因：{}", plateNumber, e.getMessage());
                    this.handleErrorMessage(barrierDriver, carRecordDetail.getId(), e.getMessage());
                    if (e.getMessage().equals(CommonEnum.CAR_HAS_ENTRY.getCodeMessage())) {
                        //已在场内 直接开闸
                        barrierDriver.openGate();
                    }
                    return true;
                }

                // 控制开闸
                this.openGate(barrierDriver, carRecordDetail.getId(), recordParam.getId(), ruleCheck.getAutoOpen());

            }

            // 出厂
            if (StrUtil.equals(direct, CommonEnum.DIRECT_OUT.getCodeMessage())) {
                // 查询最近一次的进厂记录
                CarAccessRecord record = carAccessRecordService.queryLastInRecord(plateNumber);
                if (record == null) {
                    log.info("小车无入场记录，车牌号为：{}", plateNumber);
                    this.handleErrorMessage(barrierDriver, carRecordDetail.getId(), CommonEnum.NO_IN_RECORD.getCodeMessage());
                    return true;
                }

                // 检查规则
                CheckParam ruleCheck = commonApi.checkOutCarAccessRule(carInfo, barrierGate, record.getEntryTime());
                if (!ruleCheck.getResult()) {
                    log.info("小车规则验证失败，车牌号：{}，原因：{}", plateNumber, ruleCheck.getMessage());
                    this.handleErrorMessage(barrierDriver, carRecordDetail.getId(), ruleCheck.getMessage());
                    return true;
                }

                // 更新出厂记录
                CarAccessRecordExitParam exitParam = getCarAccessRecordExitParam(carRecordDetail, barrierGate, record);
                try {
                    carAccessRecordService.registerExit(exitParam);
                } catch (Exception e) {
                    log.info("更新小车出厂记录失败，车牌号：{}，原因：{}", plateNumber, e.getMessage());
                    this.handleErrorMessage(barrierDriver, carRecordDetail.getId(), e.getMessage());
                    return true;
                }

                // 控制开闸
                this.openGate(barrierDriver, carRecordDetail.getId(), exitParam.getId(), ruleCheck.getAutoOpen());
            }

            return true;
        }
        return false;
    }

    /**
     * 内部车辆管理
     */
    private boolean internalVehicleManage(BarrierGateDriver barrierDriver, CarRecordDetail carRecordDetail) throws UnsupportedEncodingException {
        String emissionStage;
        String emissionStageName;
        //车牌号码
        String plateNumber = carRecordDetail.getPlateNumber();
        //进出道闸
        BarrierGate barrierGate = barrierDriver.getBarrierGate();
        //进出方向
        String direct = barrierGate.getDirect();
        InternalVehicle internalVehicle = internalVehicleService.queryNumberEntity(plateNumber);
        if (ObjectUtil.isNotEmpty(internalVehicle)) {
            barrierDriver.setLedDisplayInfo(null, null, internalVehicle.getOwnerInfo(), internalVehicle.getVehicleModel());
            emissionStage = internalVehicle.getEmissionStandard();
            emissionStageName = dictCacheUtil.getDictLabel(EmissionStageEnum.EMISSION_STAGE, emissionStage);
            barrierDriver.setEmissionName(emissionStageName);
            //排放阶段验证
            CheckParam stageCheck = commonApi.emissionCheck(barrierDriver, emissionStage);
            //排放阶段验证不通过
            if (!stageCheck.getResult()) {
                log.info("内部车辆排放阶段验证不通过，车牌号：{}，原因：{}", plateNumber, stageCheck.getMessage());
                this.handleErrorMessage(barrierDriver, carRecordDetail.getId(), stageCheck.getMessage());
                return true;
            }
            //入厂
            if (StrUtil.equals(direct, CommonEnum.DIRECT_IN.getCodeMessage())) {
                //入场验证
                CheckParam check = commonApi.checkInAction(plateNumber, VehicleCategoryEnum.INTERNAL.getCode());
                if (!check.getResult()) {
                    log.info("内部车辆入场验证失败，车牌号：{}，原因：{}", plateNumber, check.getMessage());
                    this.handleErrorMessage(barrierDriver, carRecordDetail.getId(), check.getMessage());
                    return true;
                }
                //插入入场台账
                InternalVehicleRecord internalVehicleRecord = getInternalVehicleRecord(carRecordDetail, plateNumber, internalVehicle, check.getOrder());
                internalVehicleRecordService.save(internalVehicleRecord);

                //控制开闸
                this.openGate(barrierDriver, carRecordDetail.getId(), internalVehicleRecord.getId(), barrierGate.isGateAutoOpen());
            }
            //出厂
            if (StrUtil.equals(direct, CommonEnum.DIRECT_OUT.getCodeMessage())) {
                //查询最近一次的入场记录
                InternalVehicleRecord internalVehicleRecord = internalVehicleRecordService.queryLastInRecord(plateNumber);
                if (internalVehicleRecord == null) {
                    log.info("内部车辆无入场记录，车牌号为：{}", plateNumber);
                    this.handleErrorMessage(barrierDriver, carRecordDetail.getId(), CommonEnum.NO_IN_RECORD.getCodeMessage());
                    return true;
                }
                //出厂验证
                CheckParam check = commonApi.checkOutAction(plateNumber, VehicleCategoryEnum.INTERNAL.getCode());
                if (!check.getResult()) {
                    log.info("内部车辆出厂验证失败，车牌号：{}，原因：{}", plateNumber, check.getMessage());
                    this.handleErrorMessage(barrierDriver, carRecordDetail.getId(), check.getMessage());
                    return true;
                }
                //更新出场台账
                internalVehicleRecord.setOutCarImageUrl(carRecordDetail.getCarImageUrl());
                internalVehicleRecordService.updateForOut(internalVehicleRecord);

                //控制开闸
                this.openGate(barrierDriver, carRecordDetail.getId(), internalVehicleRecord.getId(), barrierGate.isGateAutoOpen());
            }

            return true;
        }
        return false;
    }

    /**
     * 外部车辆管理
     */
    private boolean externalVehicleManage(BarrierGateDriver barrierDriver, CarRecordDetail carRecordDetail) throws UnsupportedEncodingException {
        String emissionStageName;
        String emissionStage;
        //车牌号码
        String plateNumber = carRecordDetail.getPlateNumber();
        //进出道闸
        BarrierGate barrierGate = barrierDriver.getBarrierGate();
        //进出方向
        String direct = barrierGate.getDirect();
        Vehicle vehicle = vehicleService.queryNumberEntity(plateNumber);
        if (ObjectUtil.isNotEmpty(vehicle)) {
            barrierDriver.setLedDisplayInfo(vehicle.getOrgName(), null, vehicle.getName(), vehicle.getModel());
            emissionStage = vehicle.getEmissionStage();
            emissionStageName = dictCacheUtil.getDictLabel(EmissionStageEnum.EMISSION_STAGE, emissionStage);
            barrierDriver.setEmissionName(emissionStageName);
            //排放阶段验证
            CheckParam stageCheck = commonApi.emissionCheck(barrierDriver, emissionStage);

            //排放阶段验证不通过
            if (!stageCheck.getResult()) {
                log.info("外部车辆排放阶段验证不通过，车牌号：{}，原因：{}", plateNumber, stageCheck.getMessage());
                this.handleErrorMessage(barrierDriver, carRecordDetail.getId(), stageCheck.getMessage());
                return true;
            }

            //入厂
            if (StrUtil.equals(direct, CommonEnum.DIRECT_IN.getCodeMessage())) {
                //入场验证
                CheckParam check = commonApi.checkInAction(plateNumber, VehicleCategoryEnum.EXTERNAL.getCode());
                if (!check.getResult()) {
                    log.info("外部车辆入场验证失败，车牌号：{}，原因：{}", plateNumber, check.getMessage());
                    this.handleErrorMessage(barrierDriver, carRecordDetail.getId(), check.getMessage());
                    return true;
                }

                //查询车辆是否已入厂
                CarRecord record = carRecordService.queryLastInRecord(plateNumber);
                if (ObjectUtil.isNotEmpty(record)) {
                    //更新相关入厂台账信息
                    setUpdateCarRecord(carRecordDetail, check, barrierGate, record);
                    carRecordService.updateById(record);

                } else {
                    record = getAddCarRecord(carRecordDetail, plateNumber, vehicle, check, barrierGate);
                    carRecordService.save(record);
                }

                //异步延时执行抓拍
                captureAsyncService.delayCapture(barrierDriver, plateNumber, record.getId());

                //控制开闸
                this.openGate(barrierDriver, carRecordDetail.getId(), record.getId(), barrierGate.isGateAutoOpen());
            }

            //出厂
            if (StrUtil.equals(direct, CommonEnum.DIRECT_OUT.getCodeMessage())) {
                //查询最近一次的进厂记录
                CarRecord record = carRecordService.queryLastInRecord(plateNumber);
                if (record == null) {
                    log.info("外部车辆无入场记录，车牌号为：{}", plateNumber);
                    this.handleErrorMessage(barrierDriver, carRecordDetail.getId(), CommonEnum.NO_IN_RECORD.getCodeMessage());
                    return true;
                }
                //出厂验证
                CheckParam check = commonApi.checkOutAction(plateNumber, VehicleCategoryEnum.EXTERNAL.getCode());
                if (!check.getResult()) {
                    log.info("外部车辆出厂验证失败，车牌号：{}，原因：{}", plateNumber, check.getMessage());
                    this.handleErrorMessage(barrierDriver, carRecordDetail.getId(), check.getMessage());
                    return true;
                }


                //获取计量数据
                MeterParam meterParam = commonApi.searchMeterData(plateNumber, DateUtil.formatDateTime(record.getInCreateTime()), DateUtil.now(), check.getOrder());

                if (!meterParam.getResult()) {
                    log.info("获取计量数据失败，车牌号为：{},{}", plateNumber, meterParam.getMessage());
                    this.handleErrorMessage(barrierDriver, carRecordDetail.getId(), meterParam.getMessage());
                    return true;
                }
                GoodsMeterInfo meter = meterParam.getGoodsMeterInfo();

                /*洗车管控  根据物料进行管控*/
                MaterialDetail inMaterialDetail = materialDetailService.saveForGate(meter.getInGoodsName(), meter.getInCategoryName());
                MaterialDetail outMaterialDetail = materialDetailService.saveForGate(meter.getOutGoodsName(), meter.getOutCategoryName());
                //任一物料需要清洗 则进行管控
                if (inMaterialDetail != null) {
                    if (inMaterialDetail.isNeedCarWash()) {
                        if (commonApi.judgeNotWash(plateNumber, DateUtil.formatDateTime(record.getInCreateTime()), DateUtil.now())) {
                            log.info("外部车辆未清洗，车牌号为：{}，道闸名称为：{}", plateNumber, barrierGate.getName());
                            this.handleErrorMessage(barrierDriver, carRecordDetail.getId(), CommonEnum.NOT_CAR_WASH.getCodeMessage());
                            return true;
                        } else {
                            record.setCarWashStatus(CarWashStatusEnum.WASHED.getCode());
                        }
                    }
                }

                if (outMaterialDetail != null) {
                    if (outMaterialDetail.isNeedCarWash()) {
                        if (commonApi.judgeNotWash(plateNumber, DateUtil.formatDateTime(record.getInCreateTime()), DateUtil.now())) {
                            log.info("外部车辆未清洗，车牌号为：{}，道闸名称为：{}", plateNumber, barrierGate.getName());
                            this.handleErrorMessage(barrierDriver, carRecordDetail.getId(), CommonEnum.NOT_CAR_WASH.getCodeMessage());
                            return true;
                        }
                    } else {
                        record.setCarWashStatus(CarWashStatusEnum.WASHED.getCode());
                    }
                }

                setExitRecord(carRecordDetail, record, meter, barrierGate);
                //更新车辆出场记录
                carRecordService.updateById(record);

                //异步延时执行抓拍
                captureAsyncService.delayCapture(barrierDriver, plateNumber, record.getId());

                //控制开闸
                this.openGate(barrierDriver, carRecordDetail.getId(), record.getId(), barrierGate.isGateAutoOpen());
            }

            return true;
        }
        return false;
    }

    /**
     * LED显示 更新识别记录  异常通行
     */
    private void handleErrorMessage(BarrierGateDriver barrierDriver, String detailId, String message) {
        barrierDriver.displayLedMessage(message, CommonEnum.LED_FORBID.getCodeMessage());
        carRecordDetailService.updateDetailResult(detailId, message);
    }

    /**
     * LED显示 更新识别记录  正常通行
     */
    private void handleNormalMessage(BarrierGateDriver barrierDriver, String detailId, String message, String recordId) {
        barrierDriver.displayLedMessage(message, CommonEnum.LED_NORMAL.getCodeMessage());
        carRecordDetailService.updateDetailResult(detailId, recordId, message);
    }

    @NotNull
    private static CarAccessRecordExitParam getCarAccessRecordExitParam(CarRecordDetail carRecordDetail, BarrierGate barrierGate, CarAccessRecord record) {
        CarAccessRecordExitParam exitParam = new CarAccessRecordExitParam();
        exitParam.setId(record.getId());
        exitParam.setExitTime(new Date());
        exitParam.setExitGate(barrierGate.getName());
        exitParam.setExitImageUrl(carRecordDetail.getCarImageUrl());
        exitParam.setExitGateId(barrierGate.getId());
        exitParam.setOutOpenGateType(barrierGate.isGateAutoOpen() ? GateCtrlTypeEnum.AUTO.getCode() : GateCtrlTypeEnum.MANUAL.getCode());
        exitParam.setOutRecordType(RecordTypeEnum.AUTO.getCode());
        return exitParam;
    }

    private static void setExitRecord(CarRecordDetail recordDetail, CarRecord carRecord, GoodsMeterInfo meter, BarrierGate gate) {
        carRecord.setInPlateImgUrl(recordDetail.getPlateImageUrl());
        carRecord.setInCarImgUrl(recordDetail.getCarImageUrl());
        carRecord.setInOrderNumber(meter.getInOrderNumber());
        carRecord.setInGoodsName(meter.getInGoodsName());
        carRecord.setInCategoryName(meter.getInCategoryName());
        carRecord.setInGoodsUnit(meter.getInGoodsUnit());
        carRecord.setInWeight(meter.getInWeight());
        carRecord.setOutOrderNumber(meter.getOutOrderNumber());
        carRecord.setOutGoodsName(meter.getOutGoodsName());
        carRecord.setOutCategoryName(meter.getOutCategoryName());
        carRecord.setOutGoodsUnit(meter.getOutGoodsUnit());
        carRecord.setOutWeight(meter.getOutWeight());
        carRecord.setOutStatus(OutStatusEnum.OUTED.getCode());
        carRecord.setOutAreaId(gate.getAreaId());
        carRecord.setOutAreaName(gate.getAreaName());
        carRecord.setOutGateId(gate.getId());
        carRecord.setOutGateName(gate.getName());
        carRecord.setOutGateCtrlType(gate.isGateAutoOpen() ? GateCtrlTypeEnum.AUTO.getCode() : GateCtrlTypeEnum.MANUAL.getCode());
        carRecord.setOutCreateTime(DateUtil.parseDateTime(DateUtil.now()));
        carRecord.setExitCaptureDeviceId(gate.getBodyCaptureCameraId());
        carRecord.setOutRecordType(RecordTypeEnum.AUTO.getCode());
    }

    @NotNull
    private static CarRecord getAddCarRecord(CarRecordDetail recordDetail, String plateNumber, Vehicle vehicle, CheckParam checkParam, BarrierGate gate) {
        CarRecord carRecord = new CarRecord();
        carRecord.setPlateNumber(plateNumber);
        carRecord.setPlateColor(vehicle.getPlateColor());
        carRecord.setVehicleId(vehicle.getId());
        carRecord.setVehicleCategory(VehicleCategoryEnum.EXTERNAL.getCode());
        carRecord.setInPlateImgUrl(recordDetail.getPlateImageUrl());
        carRecord.setInCarImgUrl(recordDetail.getCarImageUrl());
        carRecord.setInCreateTime(DateUtil.parseDateTime(DateUtil.now()));
        carRecord.setInOrderNumber(checkParam.getEntryOrderNumber());
        carRecord.setInGoodsName(checkParam.getEntryGoodsName());
        carRecord.setInGoodsId(checkParam.getEntryGoodsId());
        carRecord.setInCategoryName(checkParam.getEntryGoodsCategoryName());
        carRecord.setInCategoryId(checkParam.getEntryGoodsCategoryId());
        carRecord.setInAreaId(gate.getAreaId());
        carRecord.setInAreaName(gate.getAreaName());
        carRecord.setInGateId(gate.getId());
        carRecord.setInGateName(gate.getName());
        carRecord.setInGateCtrlType(gate.isGateAutoOpen() ? GateCtrlTypeEnum.AUTO.getCode() : GateCtrlTypeEnum.MANUAL.getCode());
        carRecord.setEntryCaptureDeviceId(gate.getBodyCaptureCameraId());
        carRecord.setInRecordType(RecordTypeEnum.AUTO.getCode());

        carRecord.setOutStatus(OutStatusEnum.NOT_OUT.getCode());
        carRecord.setCarWashStatus(CarWashStatusEnum.NOT_WASH.getCode());

        return carRecord;
    }

    private static void setUpdateCarRecord(CarRecordDetail recordDetail, CheckParam checkParam, BarrierGate gate, CarRecord carRecord) {
        carRecord.setInPlateImgUrl(recordDetail.getPlateImageUrl());
        carRecord.setInCarImgUrl(recordDetail.getCarImageUrl());
        carRecord.setInCreateTime(DateUtil.parseDateTime(DateUtil.now()));
        carRecord.setInOrderNumber(checkParam.getEntryOrderNumber());
        carRecord.setInGoodsName(checkParam.getEntryGoodsName());
        carRecord.setInGoodsId(checkParam.getEntryGoodsId());
        carRecord.setInCategoryName(checkParam.getEntryGoodsCategoryName());
        carRecord.setInCategoryId(checkParam.getEntryGoodsCategoryId());
        carRecord.setInAreaId(gate.getAreaId());
        carRecord.setInAreaName(gate.getAreaName());
        carRecord.setInGateId(gate.getId());
        carRecord.setInGateName(gate.getName());
        carRecord.setInGateCtrlType(gate.isGateAutoOpen() ? GateCtrlTypeEnum.AUTO.getCode() : GateCtrlTypeEnum.MANUAL.getCode());
        carRecord.setEntryCaptureDeviceId(gate.getBodyCaptureCameraId());
    }

    @NotNull
    private static CarRecordDetail getCarRecordDetail(String plateImg, String carImg, String plateNumber, String plateColor, BarrierGate barrierGate, HikBaseCameraDriver cameraDriver, DeviceDriver driver) {
        CarRecordDetail carRecordDetail = new CarRecordDetail();
        carRecordDetail.setPlateNumber(plateNumber);
        carRecordDetail.setPlateColor(plateColor);
        carRecordDetail.setBarrierGateId(barrierGate.getId());
        carRecordDetail.setBarrierGateName(barrierGate.getName());
        carRecordDetail.setDirect(barrierGate.getDirect());
        carRecordDetail.setCaptureCameraType(cameraDriver.type());
        carRecordDetail.setCaptureCameraId(driver.getDeviceId());
        carRecordDetail.setCaptureCameraName(driver.getDeviceName());
        carRecordDetail.setPlateImageUrl(plateImg);
        carRecordDetail.setCarImageUrl(carImg);
        return carRecordDetail;
    }

    @NotNull
    private static InternalVehicleRecord getInternalVehicleRecord(CarRecordDetail carRecordDetail, String plateNumber, InternalVehicle internalVehicle, Order order) {
        InternalVehicleRecord internalVehicleRecord = new InternalVehicleRecord();
        internalVehicleRecord.setPlateNumber(plateNumber);
        internalVehicleRecord.setVehicleId(internalVehicle.getId());
        internalVehicleRecord.setEmissionStandard(internalVehicle.getEmissionStandard());
        internalVehicleRecord.setDrivingLicense(internalVehicle.getDrivingLicenseImage());
        internalVehicleRecord.setEnvRegCode(internalVehicle.getEnvRegCode());
        internalVehicleRecord.setFuelType(internalVehicle.getFuelType());
        internalVehicleRecord.setInCarImageUrl(carRecordDetail.getCarImageUrl());
        internalVehicleRecord.setInCreateTime(DateUtil.parseDateTime(DateUtil.now()));
        internalVehicleRecord.setVinCode(internalVehicle.getVin());
        internalVehicleRecord.setVehicleModel(internalVehicle.getVehicleModel());
        internalVehicleRecord.setVehicleInventory(internalVehicle.getVehicleListImage());
        internalVehicleRecord.setRegistrationDate(internalVehicle.getRegistrationDate());
        internalVehicleRecord.setProductionDate(internalVehicle.getProductionDate());
        internalVehicleRecord.setOwnerInfo(internalVehicle.getOwnerInfo());
        internalVehicleRecord.setNetworkStatus(internalVehicle.getNetworkStatus());
        internalVehicleRecord.setOutStatus(OutStatusEnum.NOT_OUT.getCode());
        internalVehicleRecord.setRemark(order.getMaterial());
        return internalVehicleRecord;
    }

    private void openGate(BarrierGateDriver barrierGateDriver, String detailId, String recordId, boolean autoOpen) {
        //控制开闸
        if (autoOpen) {
            boolean open = barrierGateDriver.openGate();
            if (open) {
                log.info("车辆入厂，车牌号为：{}，道闸名称为：{}", barrierGateDriver.getPlateNumber(), barrierGateDriver.getBarrierGate().getName());
                this.handleNormalMessage(barrierGateDriver, detailId, CommonEnum.NORMAL_PASS.getCodeMessage(), recordId);
            } else {
                log.info("开闸失败，车牌号为：{}，道闸名称为：{}", barrierGateDriver.getPlateNumber(), barrierGateDriver.getBarrierGate().getName());
                this.handleErrorMessage(barrierGateDriver, detailId, CommonEnum.OPEN_GATE_FAILED.getCodeMessage());
            }
        } else {
            //手动开闸只需要显示屏提示
            log.info("车辆入厂，车牌号为：{}，道闸名称为：{}", barrierGateDriver.getPlateNumber(), barrierGateDriver.getBarrierGate().getName());
            this.handleNormalMessage(barrierGateDriver, detailId, CommonEnum.CHECK_VEHICLE.getCodeMessage(), recordId);
        }
    }

}

