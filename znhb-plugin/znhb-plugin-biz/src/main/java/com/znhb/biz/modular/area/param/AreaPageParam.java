package com.znhb.biz.modular.area.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 区域分页参数
 *
 * <AUTHOR>
 * @date 2025-04-11
 */
@Getter
@Setter
public class AreaPageParam {

    /** 区域名称 */
    @ApiModelProperty(value = "区域名称")
    private String areaName;

    /** 区域类型 */
    @ApiModelProperty(value = "区域类型")
    private String areaType;

    /** 上级区域ID */
    @ApiModelProperty(value = "上级区域ID")
    private String parentId;

    /** 当前页 */
    @ApiModelProperty(value = "当前页码")
    private Integer current;

    /** 每页条数 */
    @ApiModelProperty(value = "每页条数")
    private Integer size;

    /** 排序字段 */
    @ApiModelProperty(value = "排序字段，字段驼峰名称，如：userName")
    private String sortField;

    /** 排序方式 */
    @ApiModelProperty(value = "排序方式，升序：ASCEND；降序：DESCEND")
    private String sortOrder;

    /** 姓名关键词 */
    @ApiModelProperty(value = "账号、姓名、手机号关键词")
    private String searchKey;
}
