/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package com.znhb.biz.modular.order.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.znhb.biz.core.enums.OrderStatusEnum;
import com.znhb.biz.core.enums.OrderTypeEnum;
import com.znhb.common.enums.CommonSortOrderEnum;
import com.znhb.common.exception.CommonException;
import com.znhb.common.page.CommonPageRequest;
import com.znhb.biz.modular.order.entity.Order;
import com.znhb.biz.modular.order.mapper.OrderMapper;
import com.znhb.biz.modular.order.param.OrderAddParam;
import com.znhb.biz.modular.order.param.OrderEditParam;
import com.znhb.biz.modular.order.param.OrderIdParam;
import com.znhb.biz.modular.order.param.OrderPageParam;
import com.znhb.biz.modular.order.service.OrderService;

import java.util.List;

/**
 * 车辆派单表Service接口实现类
 *
 * <AUTHOR>
 * @date 2025/03/20 10:29
 **/
@Service
public class OrderServiceImpl extends ServiceImpl<OrderMapper, Order> implements OrderService {

    @Override
    public Page<Order> page(OrderPageParam orderPageParam) {
        QueryWrapper<Order> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotEmpty(orderPageParam.getSearchKey())) {
            queryWrapper.lambda().like(Order::getOrderNumber, orderPageParam.getSearchKey()).or().like(Order::getPlateNumber, orderPageParam.getSearchKey());
        }
        if (ObjectUtil.isNotEmpty(orderPageParam.getPlateNumber())) {
            queryWrapper.lambda().like(Order::getPlateNumber, orderPageParam.getPlateNumber());
        }
        if (ObjectUtil.isNotEmpty(orderPageParam.getMaterial())) {
            queryWrapper.lambda().like(Order::getMaterial, orderPageParam.getMaterial());
        }
        if (ObjectUtil.isNotEmpty(orderPageParam.getMaterialCategory())) {
            queryWrapper.lambda().like(Order::getMaterialCategory, orderPageParam.getMaterialCategory());
        }
        if (ObjectUtil.isNotEmpty(orderPageParam.getStatus())) {
            queryWrapper.lambda().eq(Order::getStatus, orderPageParam.getStatus());
        }
        if (ObjectUtil.isNotEmpty(orderPageParam.getOrderType())) {
            queryWrapper.lambda().eq(Order::getOrderType, orderPageParam.getOrderType());
        }
        if (ObjectUtil.isNotEmpty(orderPageParam.getVehicleCategory())) {
            queryWrapper.lambda().eq(Order::getVehicleCategory, orderPageParam.getVehicleCategory());
        }
        if (ObjectUtil.isNotEmpty(orderPageParam.getStartTime())) {
            if (ObjectUtil.isNotEmpty(orderPageParam.getEndTime())) {
                queryWrapper.lambda().between(Order::getCreateTime, orderPageParam.getStartTime(), orderPageParam.getEndTime());
            } else {
                queryWrapper.lambda().ge(Order::getCreateTime, orderPageParam.getStartTime());
            }
        }

        if (ObjectUtil.isAllNotEmpty(orderPageParam.getSortField(), orderPageParam.getSortOrder())) {
            CommonSortOrderEnum.validate(orderPageParam.getSortOrder());
            queryWrapper.orderBy(true, orderPageParam.getSortOrder().equals(CommonSortOrderEnum.ASC.getValue()),
                    StrUtil.toUnderlineCase(orderPageParam.getSortField()));
        } else {
            queryWrapper.lambda().orderByDesc(Order::getCreateTime);
        }
        return this.page(CommonPageRequest.defaultPage(), queryWrapper);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void add(OrderAddParam orderAddParam) {
        Order order = BeanUtil.toBean(orderAddParam, Order.class);
        // 生成派车单号
        order.setOrderNumber("PD" + DateUtil.format(DateUtil.date(), "yyyyMMddHHmmssSSS"));
        this.save(order);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(OrderEditParam orderEditParam) {
        Order order = this.queryEntity(orderEditParam.getId());
        BeanUtil.copyProperties(orderEditParam, order);
        this.updateById(order);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<OrderIdParam> orderIdParamList) {
        // 执行删除
        this.removeByIds(CollStreamUtil.toList(orderIdParamList, OrderIdParam::getId));
    }

    @Override
    public Order detail(OrderIdParam orderIdParam) {
        return this.queryEntity(orderIdParam.getId());
    }

    @Override
    public Order queryEntity(String id) {
        Order order = this.getById(id);
        if (ObjectUtil.isEmpty(order)) {
            throw new CommonException("车辆派单表不存在，id值为：{}", id);
        }
        return order;
    }

    /**
     * 车辆入场查询是否有派单
     */
    @Override
    public Order queryGateOrder(String plateNumber, String orderStatus) {
        QueryWrapper<Order> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(Order::getPlateNumber, plateNumber);
        queryWrapper.lambda().eq(Order::getOrderType, OrderTypeEnum.LONG_TERM.getCode());
        queryWrapper.lambda().last("limit 1");
        Order order = this.getOne(queryWrapper);
        if (ObjectUtil.isEmpty(order)) {
            queryWrapper.clear();
            queryWrapper.lambda().eq(Order::getPlateNumber, plateNumber);
            queryWrapper.lambda().eq(Order::getStatus, orderStatus);
            queryWrapper.lambda().le(Order::getStartTime, DateUtil.parseDateTime(DateUtil.now())).ge(Order::getEndTime, DateUtil.parseDateTime(DateUtil.now()));
            queryWrapper.lambda().orderByDesc(Order::getCreateTime);
            queryWrapper.lambda().last("limit 1");
            order = this.getOne(queryWrapper);
        }

        return order;
    }

    @Override
    public void updateOrderStatus(String orderId, String orderStatus) {
        UpdateWrapper<Order> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda().eq(Order::getId, orderId);
        updateWrapper.lambda().set(Order::getStatus, orderStatus);
        this.update(updateWrapper);
    }
}
