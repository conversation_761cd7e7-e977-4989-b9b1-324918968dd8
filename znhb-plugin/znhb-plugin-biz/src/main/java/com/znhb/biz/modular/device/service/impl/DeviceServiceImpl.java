/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package com.znhb.biz.modular.device.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.util.ClassUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.znhb.biz.core.enums.DeviceTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.znhb.biz.core.enums.DefendStatusEnum;
import com.znhb.biz.modular.hik.driver.DeviceDriver;
import com.znhb.biz.modular.hik.driver.DriverManager;
import com.znhb.biz.modular.hik.driver.camera.CameraDriver;
import com.znhb.common.enums.CommonSortOrderEnum;
import com.znhb.common.exception.CommonException;
import com.znhb.common.page.CommonPageRequest;
import com.znhb.biz.modular.device.entity.Device;
import com.znhb.biz.modular.device.mapper.DeviceMapper;
import com.znhb.biz.modular.device.param.DeviceAddParam;
import com.znhb.biz.modular.device.param.DeviceEditParam;
import com.znhb.biz.modular.device.param.DeviceIdParam;
import com.znhb.biz.modular.device.param.DevicePageParam;
import com.znhb.biz.modular.device.service.DeviceService;
import com.znhb.common.util.ClassScanUtil;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * 设备信息Service接口实现类
 *
 * <AUTHOR>
 **/
@Service
@Slf4j
public class DeviceServiceImpl extends ServiceImpl<DeviceMapper, Device> implements DeviceService {

    @Override
    public Page<Device> page(DevicePageParam devicePageParam) {
        QueryWrapper<Device> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotEmpty(devicePageParam.getName())) {
            queryWrapper.lambda().like(Device::getName, devicePageParam.getName());
        }
        if (ObjectUtil.isNotEmpty(devicePageParam.getDeviceType())) {
            queryWrapper.lambda().eq(Device::getDeviceType, devicePageParam.getDeviceType());
        }
        if (ObjectUtil.isAllNotEmpty(devicePageParam.getSortField(), devicePageParam.getSortOrder())) {
            CommonSortOrderEnum.validate(devicePageParam.getSortOrder());
            queryWrapper.orderBy(true, devicePageParam.getSortOrder().equals(CommonSortOrderEnum.ASC.getValue()),
                    StrUtil.toUnderlineCase(devicePageParam.getSortField()));
        } else {
            queryWrapper.lambda().orderByAsc(Device::getId);
        }

        Page<Device> page = this.page(CommonPageRequest.defaultPage(), queryWrapper);
        for (Device record : page.getRecords()) {
            DeviceDriver driver = DriverManager.getInstance().getDriverById(record.getId());
            if (driver != null ) {
                if (driver instanceof CameraDriver){
                    CameraDriver cameraDriver = (CameraDriver) driver;
                    record.setDefendStatus(cameraDriver.getDefendStatus());
                }else{
                    record.setDefendStatus(DefendStatusEnum.NOT_NEED_DEFEND.getCode());
                }
            }else {
                log.info("驱动不存在：{}",record.getName());
            }
        }
        return page;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void add(DeviceAddParam deviceAddParam) {
        Device device = BeanUtil.toBean(deviceAddParam, Device.class);
        this.save(device);
        Class<?> deviceClass = ClassUtil.loadClass(device.getDriverClassPath());
        DeviceDriver driver = (DeviceDriver) ReflectUtil.newInstance(deviceClass);
        driver.setDevice(device);
        //驱动缓存
        DriverManager.getInstance().putDriver(driver);
        log.info("新增驱动加载成功：{},{}", device.getName(), device.getIp());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(DeviceEditParam deviceEditParam) {
        Device device = this.queryEntity(deviceEditParam.getId());
        BeanUtil.copyProperties(deviceEditParam, device);
        this.updateById(device);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<DeviceIdParam> deviceIdParamList) {
        // 执行删除
        this.removeByIds(CollStreamUtil.toList(deviceIdParamList, DeviceIdParam::getId));
    }

    @Override
    public Device detail(DeviceIdParam deviceIdParam) {
        return this.queryEntity(deviceIdParam.getId());
    }

    @Override
    public Device queryEntity(String id) {
        Device device = this.getById(id);
        if (ObjectUtil.isEmpty(device)) {
            throw new CommonException("设备信息不存在，id值为：{}", id);
        }
        return device;
    }

    @Override
    public boolean login(DeviceIdParam deviceIdParam) {
        DeviceDriver driver = DriverManager.getInstance().getDriverById(deviceIdParam.getId());
        if (driver instanceof CameraDriver) {
            return ((CameraDriver) driver).login();
        }
        return false;
    }

    @Override
    public boolean defend(DeviceIdParam deviceIdParam) {
        DeviceDriver driver = DriverManager.getInstance().getDriverById(deviceIdParam.getId());
        if (driver == null) {
            //加载驱动
            Device device = this.detail(deviceIdParam);
            Class<?> deviceClass = ClassUtil.loadClass(device.getDriverClassPath());
            driver = (DeviceDriver) ReflectUtil.newInstance(deviceClass);
            driver.setDevice(device);
            //缓存
            DriverManager.getInstance().putDriver(driver);
            //驱动缓存
            log.info("手动驱动加载成功：{},{}", device.getName(), device.getIp());
        }
        if (driver instanceof CameraDriver) {
            CameraDriver cameraDriver = (CameraDriver) driver;
            //登录
            boolean login = cameraDriver.login();
            if (login) {
                //摄像头布防
                return cameraDriver.defend();
            }
        }

        return false;
    }

    @Override
    public boolean closeDefend(DeviceIdParam deviceIdParam) {
        DeviceDriver driver = DriverManager.getInstance().getDriverById(deviceIdParam.getId());
        if (driver instanceof CameraDriver) {
            CameraDriver cameraDriver = (CameraDriver) driver;
            return cameraDriver.closeDefend();
        }
        return false;
    }

    @Override
    public List<Map<String, Object>> getDriverClass() {
        List<Map<String, Object>> list = new ArrayList<>();
        List<String> classes = ClassScanUtil.scanSubClasses(DeviceDriver.class, "com.znhb.biz");
        for (String cls : classes) {
            Map<String, Object> map = new HashMap<>();
            try {
                Class<?> clazz = Class.forName(cls);
                Field name = clazz.getDeclaredField("NAME");
                name.setAccessible(true);
                Object o = name.get(null);
                map.put("classpath", cls);
                map.put("classname", o);
                list.add(map);
            } catch (ClassNotFoundException | NoSuchFieldException | IllegalAccessException e) {
                throw new CommonException("获取驱动类路径失败，{}",e.getMessage());
            }
        }
        return list;
    }

    @Override
    public List<Device> getPlateCameraList() {
        QueryWrapper<Device> deviceQueryWrapper = new QueryWrapper<>();
        deviceQueryWrapper.lambda().eq(Device::getDeviceType, DeviceTypeEnum.HIK_PLATE_CAMERA.getCode());
        return this.list(deviceQueryWrapper);
    }

    @Override
    public List<Device> getBodyCameraList() {
        QueryWrapper<Device> deviceQueryWrapper = new QueryWrapper<>();
        deviceQueryWrapper.lambda().eq(Device::getDeviceType, DeviceTypeEnum.HIK_BODY_CAMERA.getCode());
        return this.list(deviceQueryWrapper);
    }

    @Override
    public List<Device> getTailCameraList() {
        QueryWrapper<Device> deviceQueryWrapper = new QueryWrapper<>();
        deviceQueryWrapper.lambda().eq(Device::getDeviceType, DeviceTypeEnum.TAIL_CAMERA.getCode());
        return this.list(deviceQueryWrapper);
    }

    @Override
    public List<Device> getLedList() {
        QueryWrapper<Device> deviceQueryWrapper = new QueryWrapper<>();
        deviceQueryWrapper.lambda().eq(Device::getDeviceType, DeviceTypeEnum.LED_SCREEN.getCode());
        return this.list(deviceQueryWrapper);
    }

    @Override
    public List<Device> getIscCameraList() {
        QueryWrapper<Device> deviceQueryWrapper = new QueryWrapper<>();
        deviceQueryWrapper.lambda().isNotNull(Device::getCameraIndexCode);
        return this.list(deviceQueryWrapper);
    }
}
