package com.znhb.biz.modular.ruigang.tasker;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.znhb.biz.api.SuperLowerApi;
import com.znhb.biz.core.enums.GateCtrlTypeEnum;
import com.znhb.biz.modular.car.entity.CarAccessRecord;
import com.znhb.biz.modular.car.entity.CarInfo;
import com.znhb.biz.modular.car.service.CarAccessRecordService;
import com.znhb.biz.modular.car.service.CarInfoService;
import com.znhb.biz.modular.device.entity.Device;
import com.znhb.biz.modular.device.service.DeviceService;
import com.znhb.common.timer.CommonTimerTaskRunner;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 上报小车台账至超低平台
 */
@Slf4j
@Component
public class CarAccessRecordTasker implements CommonTimerTaskRunner {

    @Resource
    private SuperLowerApi superLowerApi;

    @Resource
    private CarAccessRecordService carAccessRecordService;

    @Resource
    private CarInfoService carInfoService;

    @Resource
    private DeviceService deviceService;


    @Override
    public void action() {
        List<CarAccessRecord> carAccessRecords = carAccessRecordService.queryCarAccessRecordForSync();

        for (CarAccessRecord carAccessRecord : carAccessRecords) {
            Map<String, Object> inMap = new HashMap<>();
            inMap.put("accessId", carAccessRecord.getId() + "_1");
            inMap.put("carNo", carAccessRecord.getPlateNumber());
            CarInfo carInfo = carInfoService.getById(carAccessRecord.getCarId());
            if (ObjectUtil.isEmpty(carInfo)){
                continue;
            }
            inMap.put("carColor", carInfo.getPlateColor());
            inMap.put("openTime", DateUtil.formatDateTime(carAccessRecord.getEntryTime()));
            inMap.put("door", carAccessRecord.getEntryGate());
            inMap.put("doorType", StrUtil.equals(GateCtrlTypeEnum.AUTO.getCode(),carAccessRecord.getInOpenGateType()) ? "0" : "1");
            Device entryDevice = deviceService.getById(carAccessRecord.getEntryCaptureDeviceId());
            if (ObjectUtil.isEmpty(entryDevice)){
                inMap.put("cameraIndexCode", "");
            }else{
                inMap.put("cameraIndexCode", entryDevice.getCameraIndexCode());
            }
            inMap.put("cameraIndexCode", carAccessRecord.getEntryCaptureDeviceId());
            log.info("小车台账同步数据：{}", JSONUtil.toJsonStr(inMap));


            Map<String, Object> outMap = new HashMap<>();
            outMap.put("accessId", carAccessRecord.getId() + "_2");
            outMap.put("carNo", carAccessRecord.getPlateNumber());
            outMap.put("carColor", carInfo.getPlateColor());
            outMap.put("openTime", DateUtil.formatDateTime(carAccessRecord.getExitTime()));
            outMap.put("door", carAccessRecord.getExitGate());
            outMap.put("doorType", StrUtil.equals(GateCtrlTypeEnum.AUTO.getCode(),carAccessRecord.getOutOpenGateType()) ? "0" : "1");
            Device exitDevice = deviceService.getById(carAccessRecord.getExitCaptureDeviceId());
            if (ObjectUtil.isEmpty(exitDevice)){
                outMap.put("cameraIndexCode", "");
            }else{
                outMap.put("cameraIndexCode", exitDevice.getCameraIndexCode());
            }
            log.info("小车台账同步数据：{}", JSONUtil.toJsonStr(outMap));

            boolean result = superLowerApi.pushCarAccessRecord(inMap);
            boolean res = superLowerApi.pushCarAccessRecord(outMap);
            if (result && res) {
                carAccessRecordService.updateForSync(carAccessRecord.getId());
            }

        }

    }
}
