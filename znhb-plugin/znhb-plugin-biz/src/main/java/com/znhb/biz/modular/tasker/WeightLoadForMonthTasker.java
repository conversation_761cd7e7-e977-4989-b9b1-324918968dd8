package com.znhb.biz.modular.tasker;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.znhb.biz.modular.cyft.mapper.CyftCarRecordMapper;
import com.znhb.biz.modular.cyft.mapper.GoodsMapper;
import com.znhb.biz.modular.vehicleRecord.service.CarRecordService;
import com.znhb.common.timer.CommonTimerTaskRunner;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * 更新计量数据
 */
@Slf4j
@Component
public class WeightLoadForMonthTasker implements CommonTimerTaskRunner {

    @Resource
    private GoodsMapper goodsMapper;

    @Resource
    private CyftCarRecordMapper cyftCarRecordMapper;


    @Override
    public void action() {
        List<Map<String, Object>> maps = cyftCarRecordMapper.cyftQueryForSync();
        if(ObjectUtil.isNotEmpty(maps)){
            for (Map<String, Object> record : maps) {
                try {
                    String order_number = (String) record.get("orderno");
                    String car_number = (String) record.get("car_number");
                    String id = (String) record.get("id");

                    List<String> orderNumberList = new ArrayList<>();
                    orderNumberList.add(order_number);

                    log.info("自动更新计量数据入参{}", JSONUtil.toJsonStr(orderNumberList));
                    String r = HttpUtil.post("http://192.168.1.24:8811/cdpfjk/third/getTranSportLedgerInfo", JSONUtil.toJsonStr(MapUtil.of("orderNumberList", orderNumberList)));
                    JSONObject transportInfo = JSONUtil.parseObj(r);
                    log.info("自动更新计量数据响应:{}车牌：{},订单号：{}", JSONUtil.toJsonStr(transportInfo), car_number, order_number);
                    Integer code = transportInfo.getInt("code");
                    if (code == 200) {
                        JSONArray data = transportInfo.getJSONArray("data");
                        JSONArray materialList = new JSONArray(data);
                        record.put("materialList", materialList);
                        if (ObjectUtil.isNotEmpty(data)) {
                            for (Object d : data) {
                                JSONObject obj = (JSONObject) d;
                                String wayBillNumber = obj.getStr("wayBillNumber");
                                if (StrUtil.equals(order_number, wayBillNumber)) {
                                    record.put("target", JSONUtil.isNull(obj.get("receivingCustomerName")) ? "" : obj.get("receivingCustomerName"));
                                    record.put("source", JSONUtil.isNull(obj.get("supplierName")) ? "" : obj.get("supplierName"));
                                    Object actualUnloadWeight = obj.get("actualUnloadWeight");
                                    if (!JSONUtil.isNull(actualUnloadWeight)) {
                                        double number = Double.parseDouble(actualUnloadWeight.toString());
                                        record.put("weight", String.valueOf(number));
                                        record.put("suttle", String.valueOf(number));
                                    } else {
                                        record.put("weight", "");
                                        record.put("suttle", "");
                                    }
                                    Object unloadSkinWeight = obj.get("unloadSkinWeight");
                                    if (!JSONUtil.isNull(unloadSkinWeight)) {
                                        double number = Double.parseDouble(unloadSkinWeight.toString());
                                        record.put("tare", String.valueOf(number));
                                    } else {
                                        record.put("tare", "");
                                    }
                                    Object unloadHairWeight = obj.get("unloadHairWeight");
                                    if (!JSONUtil.isNull(unloadHairWeight)) {
                                        double number = Double.parseDouble(unloadHairWeight.toString());
                                        record.put("gross", String.valueOf(number));
                                    } else {
                                        record.put("gross", "");
                                    }
                                    String goods_name = JSONUtil.isNull(obj.get("goodsName")) ? "" : obj.getStr("goodsName");
                                    record.put("goods", goods_name);
                                    String category = JSONUtil.isNull(obj.get("category")) ? "" : obj.getStr("category");
                                    record.put("category", category);


                                    if (StrUtil.isNotBlank(goods_name)) {
                                        if (!goods_name.contains(",")) {
                                            record.put("remark", goods_name);
                                        } else {
                                            record.put("remark", goods_name.split(",")[0]);
                                        }

                                        Map<String, Object> goodsMap = this.insertGoodsDetailReturnDetail(goods_name, category);
                                        record.put("material_id", goodsMap.get("id"));
                                        if (!goodsMap.isEmpty() && StringUtils.isBlank(category)) {
                                            String matter_type = (String) goodsMap.get("category");
                                            record.put("category", matter_type);
                                        }
                                    }
                                    cyftCarRecordMapper.carRecordUpdateOut(record);
                                    this.updateNew(record);
                                    break;
                                }
                            }
                        }else{
                            cyftCarRecordMapper.deleteCarReport(id);
                        }
                    }
                }catch (Exception e){
                    log.info("更新计量数据异常:{}", e.getMessage(), e);
                }
            }
        }

    }

    public Map<String, Object> insertGoodsDetailReturnDetail(String materialName, String materialType)  {
        Map<String, Object> goodsClass = this.insertGoodsClass(materialType);
        Map<String, Object> goodsDetails = new HashMap<>();
        List<String> split = StrUtil.split(materialName, ",");
        for (String material : split) {
            goodsDetails = goodsMapper.getGoodsDetailByName(material);
            if (goodsDetails.isEmpty()) {
                String id = IdUtil.fastSimpleUUID();
                goodsDetails.put("id", id);
                goodsDetails.put("material_name", material);
                goodsDetails.put("is_qjys", goodsClass.get("is_qjys"));
                goodsDetails.put("can_clean", goodsClass.get("can_clean"));
                goodsDetails.put("goods_id", goodsClass.get("id"));
                goodsDetails.put("type", "自动导入");
                goodsMapper.insertGoodsDetail(id, material, (String) goodsClass.get("is_qjys"), (String) goodsClass.get("can_clean"), (String) goodsClass.get("id"));
            }
        }
        goodsDetails.put("category", goodsClass.get("matter_type"));
        return goodsDetails;
    }

    public Map<String, Object> insertGoodsClass(String materialType) {
        if (StrUtil.isBlank(materialType)){
            materialType = "新增物料";
        }
        Map<String, Object> goods = goodsMapper.getGoodsClassByName(materialType);
        if (ObjectUtil.isEmpty(goods)) {
            String id = IdUtil.fastSimpleUUID();
            goodsMapper.insertGoodsClass(id, materialType);
            goods.put("id", id);
            goods.put("matter_type", materialType);
            goods.put("is_qjys", "1");
            goods.put("can_clean", "1");
        }
        return goods;
    }

    void updateNew(Map<String, Object> record){
        //更新运输报表
        try {
            Object materialListObj = record.get("materialList");
            if (!JSONUtil.isNull(materialListObj)) {
                JSONObject inObj = null;
                JSONObject outObj = null;
                JSONArray materialList = (JSONArray) materialListObj;
                JSONObject jsonObject = materialList.getJSONObject(0);
                String inGoodsName = jsonObject.getStr("inGoodsName");
                if (StrUtil.isNotEmpty(inGoodsName)) {
                    //进厂物料
                    inObj = jsonObject;
                } else {
                    //出厂物料
                    outObj = jsonObject;
                }

                if (materialList.size() > 1) {
                    jsonObject = materialList.getJSONObject(1);
                    String outGoodsName = jsonObject.getStr("outGoodsName");
                    if (StrUtil.isNotEmpty(outGoodsName)) {
                        //出厂物料
                        outObj = jsonObject;
                    } else {
                        //进厂物料
                        inObj = jsonObject;
                    }
                }

                updateNewCarRecord(inObj, outObj, record);
            }
        } catch (Exception ex) {
            log.info(ex.getMessage());
        }
    }

    public void updateNewCarRecord(JSONObject inObj, JSONObject outObj, Map<String, Object> record) {
        HashMap<String, Object> map = new HashMap<>();
        try {
            JSONObject obj = new JSONObject();
            if (ObjectUtil.isNull(inObj) && ObjectUtil.isNotNull(outObj)) {
                obj = outObj;
            }
            if (ObjectUtil.isNotNull(inObj) && ObjectUtil.isNull(outObj)) {
                obj = inObj;
            }
            if (ObjectUtil.isNotNull(inObj) && ObjectUtil.isNotNull(outObj)) {
                obj = inObj;
                obj.set("outGoodsName", outObj.getStr("outGoodsName"));
                obj.set("businessType", inObj.getStr("businessType") + "/" + outObj.getStr("businessType"));
                obj.set("deliveryGrossMeasureScale", outObj.getStr("deliveryGrossMeasureScale"));
                obj.set("deliveryGrossMeasurePoint", outObj.getStr("deliveryGrossMeasurePoint"));
                obj.set("deliveryGrossTime", outObj.getStr("deliveryGrossTime"));
                obj.set("deliveryTare", outObj.getStr("deliveryTare"));
                obj.set("deliveryTareMeasureScale", outObj.getStr("deliveryTareMeasureScale"));
                obj.set("deliveryTareTime", outObj.getStr("deliveryTareTime"));
                obj.set("deliveryTareMeasurePoint", outObj.getStr("deliveryTareMeasurePoint"));
                obj.set("deliverySuttle", outObj.getStr("deliverySuttle"));
                obj.set("deliverySuttleMeasureScale", outObj.getStr("deliverySuttleMeasureScale"));
                obj.set("deliverySuttleTime", outObj.getStr("deliverySuttleTime"));
                obj.set("deliverySuttleMeasurePoint", outObj.getStr("deliverySuttleMeasurePoint"));
            }

            map.put("in_area_code", record.get("in_gate_name"));
            map.put("out_area_code", record.get("out_gate_name"));
            map.put("in_gate_code", record.get("in_way_code"));
            map.put("out_gate_code", record.get("out_way_code"));
            map.put("in_lift_rod_type", "1");
            map.put("out_lift_rod_type", "1");
            map.put("in_time", record.get("in_create_time"));
            map.put("out_time", record.get("out_create_time"));
            map.put("in_car_img_url", record.get("in_car_image_url"));
            map.put("out_car_img_url", record.get("out_car_image_url"));
            map.put("in_lift_rod_img_url", record.get("in_trailer_car_image_url"));
            map.put("out_lift_rod_img_url", record.get("out_trailer_car_image_url"));

            map.put("material_id", record.get("material_id"));
            map.put("goods_name", record.get("goods"));
            map.put("record_id", record.get("id"));

            map.put("car_number", obj.getStr("vehicleNumber"));
            map.put("team_name", obj.getStr("teamName"));
            String inGoodsName = obj.getStr("inGoodsName");
            String outGoodsName = obj.getStr("outGoodsName");
            map.put("in_goods_name", inGoodsName);
            map.put("out_goods_name", outGoodsName);
            map.put("contract_number", obj.getStr("wayBillNumber"));
            map.put("business_type", obj.getStr("businessType"));
            map.put("unloading_location", obj.getStr("unloadingLocation"));
            map.put("source", obj.getStr("source"));
            map.put("target", obj.getStr("target"));
            map.put("supplier", obj.getStr("supplier"));
            map.put("customer", obj.getStr("customer"));
            map.put("receiver", obj.getStr("receiver"));
            map.put("receive_time", StrUtil.isNotEmpty(obj.getStr("receiveTime")) ? obj.getStr("receiveTime") : null);
            map.put("receive_gross", StrUtil.isNotEmpty(obj.getStr("receiveGross")) ? String.valueOf(Double.parseDouble(obj.getStr("receiveGross"))) : "");
            map.put("receive_gross_measure_scale", obj.getStr("receiveGrossMeasureScale"));
            map.put("receive_gross_measure_point", obj.getStr("receiveGrossMeasurePoint"));
            map.put("receive_gross_time", StrUtil.isNotEmpty(obj.getStr("receiveGrossTime")) ? obj.getStr("receiveGrossTime") : null);
            map.put("receive_tare", StrUtil.isNotEmpty(obj.getStr("receiveTare")) ? String.valueOf(Double.parseDouble(obj.getStr("receiveTare"))) : "");
            map.put("receive_tare_measure_scale", obj.getStr("receiveTareMeasureScale"));
            map.put("receive_tare_measure_point", obj.getStr("receiveTareMeasurePoint"));
            map.put("receive_tare_time", StrUtil.isNotEmpty(obj.getStr("receiveTareTime")) ? obj.getStr("receiveTareTime") : null);
            map.put("receive_suttle", StrUtil.isNotEmpty(obj.getStr("receiveSuttle")) ? String.valueOf(Double.parseDouble(obj.getStr("receiveSuttle"))) : "");
            map.put("receive_suttle_measure_scale", obj.getStr("receiveSuttleMeasureScale"));
            map.put("receive_suttle_time", StrUtil.isNotEmpty(obj.getStr("receiveSuttleTime")) ? obj.getStr("receiveSuttleTime") : null);
            map.put("receive_suttle_measure_point", obj.getStr("receiveSuttleMeasurePoint"));
            map.put("delivery_gross", StrUtil.isNotEmpty(obj.getStr("deliveryGross")) ? String.valueOf(Double.parseDouble(obj.getStr("deliveryGross"))) : "");
            map.put("delivery_gross_measure_scale", obj.getStr("deliveryGrossMeasureScale"));
            map.put("delivery_gross_measure_point", obj.getStr("deliveryGrossMeasurePoint"));
            map.put("delivery_gross_time", StrUtil.isNotEmpty(obj.getStr("deliveryGrossTime")) ? obj.getStr("deliveryGrossTime") : null);
            map.put("delivery_tare", StrUtil.isNotEmpty(obj.getStr("deliveryTare")) ? String.valueOf(Double.parseDouble(obj.getStr("deliveryTare"))) : "");
            map.put("delivery_tare_measure_scale", obj.getStr("deliveryTareMeasureScale"));
            map.put("delivery_tare_time", StrUtil.isNotEmpty(obj.getStr("deliveryTareTime")) ? obj.getStr("deliveryTareTime") : null);
            map.put("delivery_tare_measure_point", obj.getStr("deliveryTareMeasurePoint"));
            map.put("delivery_suttle", StrUtil.isNotEmpty(obj.getStr("deliverysuttle")) ? String.valueOf(Double.parseDouble(obj.getStr("deliverysuttle"))) : "");
            map.put("delivery_suttle_measure_scale", obj.getStr("deliverySuttleMeasureScale"));
            map.put("delivery_suttle_time", StrUtil.isNotEmpty(obj.getStr("deliverySuttleTime")) ? obj.getStr("deliverySuttleTime") : null);
            map.put("delivery_suttle_measure_point", obj.getStr("deliverySuttleMeasurePoint"));
            map.put("settlement_amount", StrUtil.isNotEmpty(obj.getStr("settlementAmount")) ? String.valueOf(Double.parseDouble(obj.getStr("settlementAmount"))) : "");
            map.put("sync", "0");
            cyftCarRecordMapper.updateCarRecordCyft(map);
        } catch (Exception e) {
            log.error("更新运输报表失败：{}", map, e);

        }
    }



}
