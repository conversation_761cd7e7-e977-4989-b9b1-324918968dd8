package com.znhb.biz.modular.area.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.znhb.common.pojo.CommonEntity;
import lombok.Getter;
import lombok.Setter;

/**
 * 区域实体类
 *
 * <AUTHOR>
 */
@Getter
@Setter
@TableName("t_area")
public class Area extends CommonEntity {

    /** 区域ID */
    @TableId
    private String id;

    /** 区域类型 */
    @TableField("AREA_TYPE")
    private String areaType;

    /** 区域名称 */
    @TableField("AREA_NAME")
    private String areaName;

    /** 区域编码 */
    @TableField("AREA_CODE")
    private String areaCode;

    /** 区域负责人ID */
    @TableField("MANAGER_ID")
    private String managerId;

    /** 区域负责人姓名 */
    @TableField("MANAGER_NAME")
    private String managerName;

    /** 上级区域ID */
    @TableField("PARENT_ID")
    private String parentId;

    /** 排序码 */
    @TableField("SORT_CODE")
    private Integer sortCode;

    /** 备注 */
    @TableField("REMARK")
    private String remark;
}