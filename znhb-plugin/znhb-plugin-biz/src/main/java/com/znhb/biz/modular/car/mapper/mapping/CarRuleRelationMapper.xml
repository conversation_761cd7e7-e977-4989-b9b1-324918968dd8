<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.znhb.biz.modular.car.mapper.CarRuleRelationMapper">

    <!-- 获取车辆关联的规则列表 -->
    <select id="getRelationListByCarId" resultType="com.znhb.biz.modular.car.entity.CarRuleRelation">
        SELECT r.*,
               rule.rule_name as ruleName,
               rule.priority

        FROM t_car_rule_relation r
        LEFT JOIN t_car_access_rule rule ON r.rule_id = rule.id
        WHERE r.car_id = #{carId}
        AND r.DELETE_FLAG = 'NOT_DELETE'
        ORDER BY rule.priority ASC
    </select>

</mapper>
