/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package com.znhb.biz.modular.barrierGate.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import com.znhb.common.annotation.CommonLog;
import com.znhb.common.pojo.CommonResult;
import com.znhb.common.pojo.CommonValidList;
import com.znhb.biz.modular.barrierGate.entity.BarrierGate;
import com.znhb.biz.modular.barrierGate.param.BarrierGateAddParam;
import com.znhb.biz.modular.barrierGate.param.BarrierGateEditParam;
import com.znhb.biz.modular.barrierGate.param.BarrierGateIdParam;
import com.znhb.biz.modular.barrierGate.param.BarrierGatePageParam;
import com.znhb.biz.modular.barrierGate.service.BarrierGateService;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;

/**
 * t_barrier_gate控制器
 *
 * <AUTHOR>
 * @date  2025/03/21 10:05
 */
@Api(tags = "t_barrier_gate控制器")
@ApiSupport(author = "SNOWY_TEAM", order = 1)
@RestController
@Validated
public class BarrierGateController {

    @Resource
    private BarrierGateService barrierGateService;

    /**
     * 获取t_barrier_gate分页
     *
     * <AUTHOR>
     * @date  2025/03/21 10:05
     */
    @ApiOperationSupport(order = 1)
    @ApiOperation("获取t_barrier_gate分页")
    @SaCheckPermission("/biz/barrierGate/page")
    @GetMapping("/biz/barrierGate/page")
    public CommonResult<Page<BarrierGate>> page(BarrierGatePageParam barrierGatePageParam) {
        return CommonResult.data(barrierGateService.page(barrierGatePageParam));
    }

    /**
     * 添加t_barrier_gate
     *
     * <AUTHOR>
     * @date  2025/03/21 10:05
     */
    @ApiOperationSupport(order = 2)
    @ApiOperation("添加t_barrier_gate")
    @CommonLog("添加t_barrier_gate")
    @SaCheckPermission("/biz/barrierGate/add")
    @PostMapping("/biz/barrierGate/add")
    public CommonResult<String> add(@RequestBody @Valid BarrierGateAddParam barrierGateAddParam) {
        barrierGateService.add(barrierGateAddParam);
        return CommonResult.ok();
    }

    /**
     * 编辑t_barrier_gate
     *
     * <AUTHOR>
     * @date  2025/03/21 10:05
     */
    @ApiOperationSupport(order = 3)
    @ApiOperation("编辑t_barrier_gate")
    @CommonLog("编辑t_barrier_gate")
    @SaCheckPermission("/biz/barrierGate/edit")
    @PostMapping("/biz/barrierGate/edit")
    public CommonResult<String> edit(@RequestBody @Valid BarrierGateEditParam barrierGateEditParam) {
        barrierGateService.edit(barrierGateEditParam);
        return CommonResult.ok();
    }

    /**
     * 删除t_barrier_gate
     *
     * <AUTHOR>
     * @date  2025/03/21 10:05
     */
    @ApiOperationSupport(order = 4)
    @ApiOperation("删除t_barrier_gate")
    @CommonLog("删除t_barrier_gate")
    @SaCheckPermission("/biz/barrierGate/delete")
    @PostMapping("/biz/barrierGate/delete")
    public CommonResult<String> delete(@RequestBody @Valid @NotEmpty(message = "集合不能为空")
                                                   CommonValidList<BarrierGateIdParam> barrierGateIdParamList) {
        barrierGateService.delete(barrierGateIdParamList);
        return CommonResult.ok();
    }

    /**
     * 获取t_barrier_gate详情
     *
     * <AUTHOR>
     * @date  2025/03/21 10:05
     */
    @ApiOperationSupport(order = 5)
    @ApiOperation("获取t_barrier_gate详情")
    @SaCheckPermission("/biz/barrierGate/detail")
    @GetMapping("/biz/barrierGate/detail")
    public CommonResult<BarrierGate> detail(@Valid BarrierGateIdParam barrierGateIdParam) {
        return CommonResult.data(barrierGateService.detail(barrierGateIdParam));
    }
}
