package com.znhb.biz.core.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

@Configuration
public class ThreadPoolConfig {

    @Bean
    public ThreadPoolExecutor threadPoolExecutor() {
        // 获取CPU核心数
        int cpuCores = Runtime.getRuntime().availableProcessors();
        
        // 核心线程数 = CPU核心数

        // 最大线程数 = CPU核心数 * 2
        int maxPoolSize = cpuCores * 2;
        
        // 队列大小 = CPU核心数 * 10
        int queueCapacity = cpuCores * 10;
        
        return new ThreadPoolExecutor(
                cpuCores,
            maxPoolSize,
            60L,
            TimeUnit.SECONDS,
            new ArrayBlockingQueue<>(queueCapacity),
            new ThreadPoolExecutor.CallerRunsPolicy()
        );
    }
} 