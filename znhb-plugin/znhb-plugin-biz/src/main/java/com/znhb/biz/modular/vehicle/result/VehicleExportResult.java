package com.znhb.biz.modular.vehicle.result;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * 车辆导出结果集
 *
 * <AUTHOR>
 * @date 2025/03/19
 **/
@Getter
@Setter
@HeadRowHeight(20)
@ContentRowHeight(15)
public class VehicleExportResult {

    /** 主键 */
    @ExcelIgnore
    private String id;

    /** 车牌号码 */
    @ColumnWidth(15)
    @ExcelProperty(value = "车牌号码", index = 0)
    private String plateNumber;

    /** 车牌颜色 */
    @ExcelIgnore
    private String plateColor;
    
    /** 车牌颜色名称 */
    @ColumnWidth(10)
    @ExcelProperty(value = "车牌颜色", index = 1)
    private String plateColorName;

    /** 车辆类型 */
    @ColumnWidth(15)
    @ExcelProperty(value = "车辆类型", index = 2)
    private String vehicleType;
    
    /** 所有人 */
    @ColumnWidth(20)
    @ExcelProperty(value = "所有人", index = 3)
    private String name;
    
    /** 使用性质 */
    @ColumnWidth(10)
    @ExcelProperty(value = "使用性质", index = 4)
    private String useCharacter;
    
    /** 发动机号码 */
    @ColumnWidth(20)
    @ExcelProperty(value = "发动机号码", index = 5)
    private String engineNo;

    /** 车辆识别代号 */
    @ColumnWidth(25)
    @ExcelProperty(value = "车辆识别代号", index = 6)
    private String vin;

    /** 能源类型 */
    @ColumnWidth(10)
    @ExcelProperty(value = "能源类型", index = 7)
    private String energyType;
    
    /** 外廓尺寸 */
    @ColumnWidth(15)
    @ExcelProperty(value = "外廓尺寸", index = 8)
    private String dimension;
    
    /** 总质量 */
    @ColumnWidth(15)
    @ExcelProperty(value = "总质量(kg)", index = 9)
    private String grossMass;
    
    /** 整备质量 */
    @ColumnWidth(15)
    @ExcelProperty(value = "整备质量(kg)", index = 10)
    private String unladenMass;
    
    /** 核定载质量 */
    @ColumnWidth(15)
    @ExcelProperty(value = "核定载质量(kg)", index = 11)
    private String approvedLoad;

    /** 联网状态 */
    @ExcelIgnore
    private String netStatus;
    
    /** 联网状态名称 */
    @ColumnWidth(10)
    @ExcelProperty(value = "联网状态", index = 12)
    private String netStatusName;
    
    /** 排放标准 */
    @ExcelIgnore
    private String emissionStage;
    
    /** 排放标准名称 */
    @ColumnWidth(10)
    @ExcelProperty(value = "排放标准", index = 13)
    private String emissionStageName;

    /** 注册日期 */
    @ColumnWidth(15)
    @ExcelProperty(value = "注册日期", index = 14)
    @DateTimeFormat("yyyy-MM-dd")
    private Date registerDate;
    
    /** 发证日期 */
    @ColumnWidth(15)
    @ExcelProperty(value = "发证日期", index = 15)
    @DateTimeFormat("yyyy-MM-dd")
    private Date issueDate;
    
    /** 发证机关 */
    @ColumnWidth(20)
    @ExcelProperty(value = "发证机关", index = 16)
    private String issuingAuthority;

    /** 审核状态 */
    @ExcelIgnore
    private String verifyStatus;
    
    /** 审核状态名称 */
    @ColumnWidth(10)
    @ExcelProperty(value = "审核状态", index = 17)
    private String verifyStatusName;

    /** 审核时间 */
    @ColumnWidth(20)
    @ExcelProperty(value = "审核时间", index = 18)
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    private Date verifyTime;

    /** 审核人名称 */
    @ColumnWidth(10)
    @ExcelProperty(value = "审核人", index = 19)
    private String verifyUserName;

    /** 审核备注 */
    @ColumnWidth(25)
    @ExcelProperty(value = "审核备注", index = 20)
    private String verifyRemark;
    
    /** 创建时间 */
    @ColumnWidth(20)
    @ExcelProperty(value = "创建时间", index = 21)
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    private Date createTime;
} 