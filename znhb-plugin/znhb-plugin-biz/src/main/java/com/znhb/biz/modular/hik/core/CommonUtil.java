package com.znhb.biz.modular.hik.core;

import cn.hutool.core.util.StrUtil;

public class CommonUtil {
    //SDK时间解析
    public static String parseTime(int time) {
        int year = (time >> 26) + 2000;
        int month = (time >> 22) & 15;
        int day = (time >> 17) & 31;
        int hour = (time >> 12) & 31;
        int min = (time >> 6) & 63;
        int second = (time) & 63;
        return year + "-" + month + "-" + day + "-" + hour + ":" + min + ":" + second;
    }

    public static String parsePlateNumber(String str) {
        str = str.trim();
        if(StrUtil.equals(str, "无车牌")) return str;
        String car_number;
        if (str.startsWith("黄绿")) {
            car_number = str.substring(2);
        } else if (str.startsWith("临")) {
            car_number = str;
        } else {
            car_number = str.substring(1);
        }
        return car_number;
    }

    public static String parsePlateColor(String str) {
        str = str.trim();
        String car_number_color;
        if (str.startsWith("黄绿")) {
            car_number_color = "黄绿";
        } else if (str.startsWith("临")) {
            car_number_color = "蓝";
        } else {
            car_number_color = str.substring(0, 1);
        }
        return car_number_color;
    }

    public static String parseLevel(String level) {
        if (StrUtil.isNotEmpty(level)) {
            switch (level) {
                case "1":
                    level = "国一";
                    break;
                case "2":
                    level = "国二";
                    break;
                case "3":
                    level = "国三";
                    break;
                case "4":
                    level = "国四";
                    break;
                case "5":
                    level = "国五";
                    break;
                case "6":
                    level = "国六";
                    break;
                case "D":
                    level = "电动";
                    break;
            }

        }
        return level;
    }

}
