package com.znhb.biz.modular.hik.driver;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import com.znhb.biz.modular.device.entity.Device;

@Data
public class DeviceDriver {

    public Device device;

    public String getDeviceIp(){
        return device.getIp();
    }

    public String getDeviceId(){
        return device.getId();
    }

    public String getDeviceName(){
        return device.getName();
    }

    public String getDriverId(){
        return device.getDriverId();
    }

    public String getDriverName(){
        return device.getDriverName();
    }
}
