package com.znhb.biz.modular.ruigang.tasker;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.znhb.biz.api.SuperLowerApi;
import com.znhb.biz.modular.nonRoadMachinery.entity.NonRoadMachinery;
import com.znhb.biz.modular.nonRoadMachinery.service.NonRoadMachineryService;
import com.znhb.common.timer.CommonTimerTaskRunner;
import lombok.extern.slf4j.Slf4j;
import org.checkerframework.checker.units.qual.C;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * @description: 非道路移动机械上传到环保平台
 */
@Slf4j
@Component
public class NonRoadMachineryTasker implements CommonTimerTaskRunner {

    @Resource
    private SuperLowerApi superLowerApi;


    @Resource
    private NonRoadMachineryService nonRoadMachineryService;


    @Override
    public void action() {
        List<NonRoadMachinery> nonRoadMachineryList = nonRoadMachineryService.queryNonRoadMachineryForSync();
        for (NonRoadMachinery nonRoadMachinery : nonRoadMachineryList) {
            Map<String, Object> map = new HashMap<>();
            map.put("carNo", nonRoadMachinery.getPlateNumber());
            map.put("code", nonRoadMachinery.getEnvRegCode());
            map.put("level", nonRoadMachinery.getEmissionStandard());
            map.put("date", nonRoadMachinery.getProductionDate());
            map.put("machineType", nonRoadMachinery.getMachineryType());
            map.put("fuelType", nonRoadMachinery.getFuelType());
            map.put("pin", nonRoadMachinery.getPinCode());
            map.put("mechanicalType", nonRoadMachinery.getMachineryModel());
            map.put("engineType", nonRoadMachinery.getEngineModel());
            map.put("engineBusiness", nonRoadMachinery.getEngineManufacturer());
            map.put("engineNo", nonRoadMachinery.getEngineSerialNumber());
            map.put("completeCarUrl", nonRoadMachinery.getMachineryNameplateImage());
            map.put("nameplateUrl", nonRoadMachinery.getEngineNameplateImage());
            map.put("markUrl", nonRoadMachinery.getEnvInfoLabelImage());
            map.put("owner", nonRoadMachinery.getOwnerInfo());
            map.put("qrCodeUrl", nonRoadMachinery.getEnvQrCodeImage());
            log.info("非道路移动机械台账数据推送：{}", JSONUtil.toJsonStr(map));
            boolean result = superLowerApi.pushNonRoadMachinery(map);
            if (result) {
                nonRoadMachineryService.updateForSync(nonRoadMachinery.getId());
            }
        }
    }

}
