/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package com.znhb.biz.modular.nonRoadMachinery.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.znhb.biz.modular.nonRoadMachinery.entity.NonRoadMachinery;
import com.znhb.biz.modular.nonRoadMachinery.param.NonRoadMachineryAddParam;
import com.znhb.biz.modular.nonRoadMachinery.param.NonRoadMachineryEditParam;
import com.znhb.biz.modular.nonRoadMachinery.param.NonRoadMachineryIdParam;
import com.znhb.biz.modular.nonRoadMachinery.param.NonRoadMachineryPageParam;

import java.util.List;

/**
 * 非道路移动机械信息表Service接口
 *
 * <AUTHOR>
 * @date 2025/04/03 10:31
 **/
public interface NonRoadMachineryService extends IService<NonRoadMachinery> {

    /**
     * 获取非道路移动机械信息表分页
     *
     * <AUTHOR>
     * @date 2025/04/03 10:31
     */
    Page<NonRoadMachinery> page(NonRoadMachineryPageParam nonRoadMachineryPageParam);

    /**
     * 添加非道路移动机械信息表
     *
     * <AUTHOR>
     * @date 2025/04/03 10:31
     */
    void add(NonRoadMachineryAddParam nonRoadMachineryAddParam);

    /**
     * 编辑非道路移动机械信息表
     *
     * <AUTHOR>
     * @date 2025/04/03 10:31
     */
    void edit(NonRoadMachineryEditParam nonRoadMachineryEditParam);

    /**
     * 删除非道路移动机械信息表
     *
     * <AUTHOR>
     * @date 2025/04/03 10:31
     */
    void delete(List<NonRoadMachineryIdParam> nonRoadMachineryIdParamList);

    /**
     * 获取非道路移动机械信息表详情
     *
     * <AUTHOR>
     * @date 2025/04/03 10:31
     */
    NonRoadMachinery detail(NonRoadMachineryIdParam nonRoadMachineryIdParam);

    /**
     * 获取非道路移动机械信息表详情
     *
     * <AUTHOR>
     * @date 2025/04/03 10:31
     **/
    NonRoadMachinery queryEntity(String id);

    /**
     * 根据车牌号获取非移机械
     *
     * @param plateNumber
     * @return
     */
    NonRoadMachinery queryNumberEntity(String plateNumber);

    /**
     * 同步非道路移动机械信息表
     *
     * <AUTHOR>
     * @date 2025/04/03 10:31
     */
    List<NonRoadMachinery> queryNonRoadMachineryForSync();

    /**
     * 同步状态更新
     *
     * <AUTHOR>
     * @date 2025/04/03 10:31
     */
    void updateForSync(String id);

}
