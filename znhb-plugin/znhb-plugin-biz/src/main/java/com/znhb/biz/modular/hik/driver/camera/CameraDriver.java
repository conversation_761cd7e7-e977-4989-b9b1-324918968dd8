package com.znhb.biz.modular.hik.driver.camera;



import com.znhb.biz.modular.hik.driver.DeviceDriver;


public abstract class CameraDriver extends DeviceDriver {

    public boolean defendJudge;

    public abstract boolean login();

    public abstract boolean logout();

    public abstract boolean defend();

    public abstract boolean closeDefend();

    public abstract boolean onlineInspect();

    public abstract boolean previewVideo();

    public abstract String type();

    public abstract String getDefendStatus();
}
