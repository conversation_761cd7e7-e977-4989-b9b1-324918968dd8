package com.znhb.biz.modular.hik.core;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.znhb.biz.core.enums.CommonEnum;
import com.znhb.biz.modular.hik.driver.barrierGate.BarrierGateDriver;
import com.znhb.biz.modular.vehicleRecord.service.CarRecordService;
import com.znhb.dev.api.DevConfigApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
@Slf4j
public class CaptureAsyncService {

    @Resource
    private CarRecordService carRecordService;

    @Resource
    private DevConfigApi devConfigApi;

    @Async
    public void delayCapture(BarrierGateDriver gateDriver, String plateNumber, String recordId) {
        try {
            String duration = devConfigApi.getCamaraCaptureDelayDuration();
            if (StrUtil.isBlank(duration) || !NumberUtil.isNumber(duration)) {
                Thread.sleep(2000); // 默认2秒延时，可配置
            }else{
                // 延时执行
                Thread.sleep(Long.parseLong(duration));
            }

            // 执行抓拍
            String bodyImgUrl = gateDriver.bodyCapture(plateNumber);
            String tailImgUrl = gateDriver.tailCapture(plateNumber);

            // 更新台账记录
            if (StrUtil.isNotBlank(bodyImgUrl) || StrUtil.isNotBlank(tailImgUrl)) {
                if (gateDriver.getBarrierGate().getDirect().equals(CommonEnum.DIRECT_IN.getCodeMessage())){
                    carRecordService.updateEntryCaptureImgUrl(recordId, bodyImgUrl, tailImgUrl);
                }else{
                    carRecordService.updateExitCaptureImgUrl(recordId, bodyImgUrl, tailImgUrl);
                }
            }
        } catch (Exception e) {
            log.error("内部车辆延时抓拍失败：{}", e.getMessage(), e);
        }
    }
}