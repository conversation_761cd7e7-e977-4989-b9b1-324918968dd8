package com.znhb.biz.modular.vehicle.param;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import java.util.Date;

/**
 * 车辆审核参数
 **/
@Getter
@Setter
public class VehicleAuditParam {

    /** 主键 */
    @ApiModelProperty(value = "主键", required = true, position = 1)
    @NotBlank(message = "id不能为空")
    private String id;

    /** 审核状态 */
    @ApiModelProperty(value = "审核状态", required = true, position = 2)
    @NotBlank(message = "审核状态不能为空")
    private String verifyStatus;

    /** 审核备注 */
    @ApiModelProperty(value = "审核备注", position = 3)
    private String verifyRemark;

    /** 审核时间 */
    @ApiModelProperty(value = "审核时间", position = 4)
    private Date verifyTime;
} 