package com.znhb.biz.modular.ocr.param;

import lombok.Data;

@Data
public class OcrParam {

    public static String FRONT_SIDE = "front";

    public static String BACK_SIDE = "back";

    public static String DOUBLE_SIDE = "double_side";


    /**
     * 图片参数传的照片  url和image参数二选一  imgae传Base64编码的图片数据 不需要加任何前缀
     */
    private String image;

    /**
     * 首页urlimage
     */
    private String frontImage;

    /**
     * 副页image
     */
    private String backImage;

    /**
     * url参数传的照片 需要是首页和副页都需要在一张照片中
     */
    private String url;

    /**
     * 首页url
     */
    private String frontUrl;

    /**
     * 副页url
     */
    private String backUrl;

    /**
     * 是否返回发证机关
     */
    private Boolean returnIssuingAuthority = true;

    /**
     * 是否返回文本位置
     */
    private Boolean returnTextLocation;





}
