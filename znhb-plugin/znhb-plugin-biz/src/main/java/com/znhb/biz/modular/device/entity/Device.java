/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package com.znhb.biz.modular.device.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * 设备信息实体
 *
 * <AUTHOR>
 * @date  2025/03/20 16:10
 **/
@Getter
@Setter
@TableName("t_device")
public class Device {

    /** id */
    @TableId
    @ApiModelProperty(value = "id", position = 1)
    private String id;

    /** 设备名称 */
    @ApiModelProperty(value = "设备名称", position = 2)
    private String name;

    /** 设备ip */
    @ApiModelProperty(value = "设备ip", position = 3)
    private String ip;

    /** 设备端口 */
    @ApiModelProperty(value = "设备端口", position = 4)
    private String port;

    /** 账号 */
    @ApiModelProperty(value = "账号", position = 5)
    private String username;

    /** 密码 */
    @ApiModelProperty(value = "密码", position = 6)
    private String password;

    /** 设备类型 */
    @ApiModelProperty(value = "设备类型", position = 7)
    private String deviceType;

    /** 驱动id */
    @ApiModelProperty(value = "驱动id", position = 8)
    private String driverId;

    /** 驱动类路径 */
    @ApiModelProperty(value = "驱动类路径", position = 9)
    private String driverClassPath;

    /** 驱动名称*/
    @ApiModelProperty(value = "驱动名称", position = 9)
    private String driverName;

    /** 设备状态 */
    @ApiModelProperty(value = "设备状态", position = 10)
    private String status;

    /** 删除标志 */
    @ApiModelProperty(value = "删除标志", position = 11)
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    private String deleteFlag;

    /** 创建时间 */
    @ApiModelProperty(value = "创建时间", position = 12)
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /** 创建用户 */
    @ApiModelProperty(value = "创建用户", position = 13)
    @TableField(fill = FieldFill.INSERT)
    private String createUser;

    /** 修改时间 */
    @ApiModelProperty(value = "修改时间", position = 14)
    @TableField(fill = FieldFill.UPDATE)
    private Date updateTime;

    /** 修改用户 */
    @ApiModelProperty(value = "修改用户", position = 15)
    @TableField(fill = FieldFill.UPDATE)
    private String updateUser;

    /**
     * 布防状态
     */
    @ApiModelProperty(value = "布防状态", position = 16)
    @TableField(exist = false)
    private String defendStatus;

    /** ISC摄像机编码 */
    @ApiModelProperty(value = "ISC摄像机编码", position = 17)
    private String cameraIndexCode;

    /** areaId */
    @ApiModelProperty(value = "areaId")
    private String areaId;

    /** areaName */
    @ApiModelProperty(value = "areaName")
    private String areaName;


}
