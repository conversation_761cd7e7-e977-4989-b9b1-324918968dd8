/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package com.znhb.biz.modular.nonRoadRecord.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.znhb.biz.modular.nonRoadRecord.entity.NonRoadRecord;
import com.znhb.biz.modular.nonRoadRecord.param.NonRoadRecordAddParam;
import com.znhb.biz.modular.nonRoadRecord.param.NonRoadRecordEditParam;
import com.znhb.biz.modular.nonRoadRecord.param.NonRoadRecordIdParam;
import com.znhb.biz.modular.nonRoadRecord.param.NonRoadRecordPageParam;

import java.util.List;

/**
 * 非移机械台账Service接口
 *
 * <AUTHOR>
 * @date  2025/04/03 11:32
 **/
public interface NonRoadRecordService extends IService<NonRoadRecord> {

    /**
     * 获取非移机械台账分页
     *
     * <AUTHOR>
     * @date  2025/04/03 11:32
     */
    Page<NonRoadRecord> page(NonRoadRecordPageParam nonRoadRecordPageParam);

    /**
     * 添加非移机械台账
     *
     * <AUTHOR>
     * @date  2025/04/03 11:32
     */
    void add(NonRoadRecordAddParam nonRoadRecordAddParam);

    /**
     * 编辑非移机械台账
     *
     * <AUTHOR>
     * @date  2025/04/03 11:32
     */
    void edit(NonRoadRecordEditParam nonRoadRecordEditParam);

    /**
     * 删除非移机械台账
     *
     * <AUTHOR>
     * @date  2025/04/03 11:32
     */
    void delete(List<NonRoadRecordIdParam> nonRoadRecordIdParamList);

    /**
     * 获取非移机械台账详情
     *
     * <AUTHOR>
     * @date  2025/04/03 11:32
     */
    NonRoadRecord detail(NonRoadRecordIdParam nonRoadRecordIdParam);

    /**
     * 获取非移机械台账详情
     *
     * <AUTHOR>
     * @date  2025/04/03 11:32
     **/
    NonRoadRecord queryEntity(String id);

    /**
     * 更新出厂信息
     * @param nonRoadRecord
     */
    void updateForOut(NonRoadRecord nonRoadRecord);

    /**
     * 查询最近一次进厂信息
     *
     * <AUTHOR>
     * @date  2025/04/03 11:32
     **/
    NonRoadRecord queryLastInRecord(String plateNumber);

    /**
     * 查询未同步数据
     *
     * <AUTHOR>
     * @date  2025/04/03 11:32
     **/
    List<NonRoadRecord> queryNonRoadAccessRecordForSync();

    /**更新同步状态
     * @param id
     */
    void updateForSync(String id);
}
