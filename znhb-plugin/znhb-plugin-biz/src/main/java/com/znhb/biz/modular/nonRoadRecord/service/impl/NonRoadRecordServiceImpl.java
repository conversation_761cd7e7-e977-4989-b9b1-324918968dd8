/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package com.znhb.biz.modular.nonRoadRecord.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.znhb.biz.core.enums.OutStatusEnum;
import com.znhb.biz.core.enums.SyncStatusEnum;
import com.znhb.biz.modular.internalVehicleRecord.entity.InternalVehicleRecord;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.znhb.common.enums.CommonSortOrderEnum;
import com.znhb.common.exception.CommonException;
import com.znhb.common.page.CommonPageRequest;
import com.znhb.biz.modular.nonRoadRecord.entity.NonRoadRecord;
import com.znhb.biz.modular.nonRoadRecord.mapper.NonRoadRecordMapper;
import com.znhb.biz.modular.nonRoadRecord.param.NonRoadRecordAddParam;
import com.znhb.biz.modular.nonRoadRecord.param.NonRoadRecordEditParam;
import com.znhb.biz.modular.nonRoadRecord.param.NonRoadRecordIdParam;
import com.znhb.biz.modular.nonRoadRecord.param.NonRoadRecordPageParam;
import com.znhb.biz.modular.nonRoadRecord.service.NonRoadRecordService;

import java.util.Collections;
import java.util.List;

/**
 * 非移机械台账Service接口实现类
 *
 * <AUTHOR>
 * @date  2025/04/03 11:32
 **/
@Service
public class NonRoadRecordServiceImpl extends ServiceImpl<NonRoadRecordMapper, NonRoadRecord> implements NonRoadRecordService {

    @Override
    public Page<NonRoadRecord> page(NonRoadRecordPageParam nonRoadRecordPageParam) {
        QueryWrapper<NonRoadRecord> queryWrapper = new QueryWrapper<>();
        if(ObjectUtil.isNotEmpty(nonRoadRecordPageParam.getEnvRegCode())) {
            queryWrapper.lambda().like(NonRoadRecord::getEnvRegCode, nonRoadRecordPageParam.getEnvRegCode());
        }
        if(ObjectUtil.isNotEmpty(nonRoadRecordPageParam.getPlateNumber())) {
            queryWrapper.lambda().like(NonRoadRecord::getPlateNumber, nonRoadRecordPageParam.getPlateNumber());
        }
        if(ObjectUtil.isNotEmpty(nonRoadRecordPageParam.getEmissionStandard())) {
            queryWrapper.lambda().eq(NonRoadRecord::getEmissionStandard, nonRoadRecordPageParam.getEmissionStandard());
        }
        if(ObjectUtil.isNotEmpty(nonRoadRecordPageParam.getFuelType())) {
            queryWrapper.lambda().eq(NonRoadRecord::getFuelType, nonRoadRecordPageParam.getFuelType());
        }
        if(ObjectUtil.isNotEmpty(nonRoadRecordPageParam.getMachineryType())) {
            queryWrapper.lambda().eq(NonRoadRecord::getMachineryType, nonRoadRecordPageParam.getMachineryType());
        }
        if(ObjectUtil.isNotEmpty(nonRoadRecordPageParam.getPinCode())) {
            queryWrapper.lambda().like(NonRoadRecord::getPinCode, nonRoadRecordPageParam.getPinCode());
        }
        if(ObjectUtil.isAllNotEmpty(nonRoadRecordPageParam.getSortField(), nonRoadRecordPageParam.getSortOrder())) {
            CommonSortOrderEnum.validate(nonRoadRecordPageParam.getSortOrder());
            queryWrapper.orderBy(true, nonRoadRecordPageParam.getSortOrder().equals(CommonSortOrderEnum.ASC.getValue()),
                    StrUtil.toUnderlineCase(nonRoadRecordPageParam.getSortField()));
        } else {
            queryWrapper.lambda().orderByAsc(NonRoadRecord::getId);
        }
        return this.page(CommonPageRequest.defaultPage(), queryWrapper);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void add(NonRoadRecordAddParam nonRoadRecordAddParam) {
        NonRoadRecord nonRoadRecord = BeanUtil.toBean(nonRoadRecordAddParam, NonRoadRecord.class);
        this.save(nonRoadRecord);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(NonRoadRecordEditParam nonRoadRecordEditParam) {
        NonRoadRecord nonRoadRecord = this.queryEntity(nonRoadRecordEditParam.getId());
        BeanUtil.copyProperties(nonRoadRecordEditParam, nonRoadRecord);
        this.updateById(nonRoadRecord);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<NonRoadRecordIdParam> nonRoadRecordIdParamList) {
        // 执行删除
        this.removeByIds(CollStreamUtil.toList(nonRoadRecordIdParamList, NonRoadRecordIdParam::getId));
    }

    @Override
    public NonRoadRecord detail(NonRoadRecordIdParam nonRoadRecordIdParam) {
        return this.queryEntity(nonRoadRecordIdParam.getId());
    }

    @Override
    public NonRoadRecord queryEntity(String id) {
        NonRoadRecord nonRoadRecord = this.getById(id);
        if(ObjectUtil.isEmpty(nonRoadRecord)) {
            throw new CommonException("非移机械台账不存在，id值为：{}", id);
        }
        return nonRoadRecord;
    }

    @Override
    public void updateForOut(NonRoadRecord nonRoadRecord) {
        UpdateWrapper<NonRoadRecord> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda().eq(NonRoadRecord::getId, nonRoadRecord.getId());
        updateWrapper.lambda().set(NonRoadRecord::getOutStatus, OutStatusEnum.OUTED.getCode());
        updateWrapper.lambda().set(NonRoadRecord::getOutCarImageUrl, nonRoadRecord.getOutCarImageUrl());
        updateWrapper.lambda().set(NonRoadRecord::getOutCreateTime, DateUtil.parseDateTime(DateUtil.now()));
        this.update(updateWrapper);
    }

    @Override
    public NonRoadRecord queryLastInRecord(String plateNumber) {
        QueryWrapper<NonRoadRecord> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(NonRoadRecord::getPlateNumber, plateNumber);
        queryWrapper.lambda().eq(NonRoadRecord::getOutStatus, OutStatusEnum.NOT_OUT.getCode());
        queryWrapper.lambda().orderByDesc(NonRoadRecord::getInCreateTime);
        queryWrapper.last("limit 1");
        return this.getOne(queryWrapper);
    }

    @Override
    public List<NonRoadRecord> queryNonRoadAccessRecordForSync() {
        QueryWrapper<NonRoadRecord> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(NonRoadRecord::getOutStatus, OutStatusEnum.OUTED.getCode());
        queryWrapper.lambda().eq(NonRoadRecord::getSyncStatus, SyncStatusEnum.NOT_SYNC.getCode());
        queryWrapper.lambda().between(NonRoadRecord::getUpdateTime, DateUtil.beginOfDay(DateUtil.parseDateTime(DateUtil.now())), DateUtil.endOfDay(DateUtil.parseDateTime(DateUtil.now())));
        return this.list(queryWrapper);
    }

    @Override
    public void updateForSync(String id) {
        UpdateWrapper<NonRoadRecord> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda().eq(NonRoadRecord::getId, id);
        updateWrapper.lambda().set(NonRoadRecord::getSyncStatus, SyncStatusEnum.SYNCED.getCode());
        this.update(updateWrapper);
    }
}
