package com.znhb.biz.modular.hik.entity;


import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * LED屏幕显示配置通用实体
 */
@Data
public class LedCommonConfig {

    private static final LedCommonConfig INSTANCE = new LedCommonConfig();

    // 第一行显示参数
    private Integer line1X1 = 0;   // 左上角X坐标 0
    private Integer line1Y1 = 0;   // 左上角Y坐标 0
    private Integer line1X2 = 63;   // 右下角X坐标 63
    private Integer line1Y2 = 15;   // 右下角Y坐标 15
    private String line1Color = "0x02";   // 颜色值(16进制字节) 0x02
    private String line1Size = "0x10";    // 字体大小 0x10
    private String line1Action = "0x01";  // 显示动作 0x01
    private String line1Stay = "1";  // 停留时间 1
    private String line1Speed = "1";  // 滚动速度 1

    // 第二行显示参数（结构同第一行）
    private Integer line2X1 = 0;    //0
    private Integer line2Y1 = 16;    //16
    private Integer line2X2 = 63;    //63
    private Integer line2Y2 = 31;    //31
    private String line2Color = "0x02";  //0x02
    private String line2Size = "0x10";   //0x10
    private String line2Action = "0x01"; //0x01
    private String line2Stay = "1";   //1
    private String line2Speed = "1";  //1

    // 第三行显示参数（结构同第一行）
    private Integer line3X1 = 0; //0
    private Integer line3Y1 = 32; //32
    private Integer line3X2 = 63; //63
    private Integer line3Y2 = 47; //47
    private String line3Color = "0x02"; // 0x02
    private String line3Size = "0x10"; // 0x10
    private String line3Action = "0x01"; //0x01
    private String line3Stay = "1"; //1
    private String line3Speed = "1"; //1

    // 第四行显示参数（结构同第一行）
    private Integer line4X1 = 0; //0
    private Integer line4Y1 = 48; //48
    private Integer line4X2 = 63; //63
    private Integer line4Y2 = 63; //47
    private String line4Color = "0x02"; //0x02
    private String line4Size = "0x10"; //0x10
    private String line4Action = "0x01"; //0x01
    private String line4Stay = "1"; //1
    private String line4Speed = "1"; //1

    private LedCommonConfig() {}

    public static LedCommonConfig getInstance() {
        return INSTANCE;
    }

}