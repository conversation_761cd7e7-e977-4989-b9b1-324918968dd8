/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package com.znhb.biz.modular.materialcategory.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import com.znhb.common.annotation.CommonLog;
import com.znhb.common.pojo.CommonResult;
import com.znhb.common.pojo.CommonValidList;
import com.znhb.biz.modular.materialcategory.entity.MaterialCategory;
import com.znhb.biz.modular.materialcategory.param.MaterialCategoryAddParam;
import com.znhb.biz.modular.materialcategory.param.MaterialCategoryEditParam;
import com.znhb.biz.modular.materialcategory.param.MaterialCategoryIdParam;
import com.znhb.biz.modular.materialcategory.param.MaterialCategoryPageParam;
import com.znhb.biz.modular.materialcategory.service.MaterialCategoryService;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;

/**
 * 物料分类控制器
 *
 * <AUTHOR>
 * @date  2025/03/20 11:29
 */
@Api(tags = "物料分类控制器")
@ApiSupport(author = "SNOWY_TEAM", order = 1)
@RestController
@Validated
public class MaterialCategoryController {

    @Resource
    private MaterialCategoryService materialCategoryService;

    /**
     * 获取物料分类分页
     *
     * <AUTHOR>
     * @date  2025/03/20 11:29
     */
    @ApiOperationSupport(order = 1)
    @ApiOperation("获取物料分类分页")
    @SaCheckPermission("/biz/materialcategory/page")
    @GetMapping("/biz/materialcategory/page")
    public CommonResult<Page<MaterialCategory>> page(MaterialCategoryPageParam materialCategoryPageParam) {
        return CommonResult.data(materialCategoryService.page(materialCategoryPageParam));
    }

    /**
     * 添加物料分类
     *
     * <AUTHOR>
     * @date  2025/03/20 11:29
     */
    @ApiOperationSupport(order = 2)
    @ApiOperation("添加物料分类")
    @CommonLog("添加物料分类")
    @SaCheckPermission("/biz/materialcategory/add")
    @PostMapping("/biz/materialcategory/add")
    public CommonResult<String> add(@RequestBody @Valid MaterialCategoryAddParam materialCategoryAddParam) {
        materialCategoryService.add(materialCategoryAddParam);
        return CommonResult.ok();
    }

    /**
     * 编辑物料分类
     *
     * <AUTHOR>
     * @date  2025/03/20 11:29
     */
    @ApiOperationSupport(order = 3)
    @ApiOperation("编辑物料分类")
    @CommonLog("编辑物料分类")
    @SaCheckPermission("/biz/materialcategory/edit")
    @PostMapping("/biz/materialcategory/edit")
    public CommonResult<String> edit(@RequestBody @Valid MaterialCategoryEditParam materialCategoryEditParam) {
        materialCategoryService.edit(materialCategoryEditParam);
        return CommonResult.ok();
    }

    /**
     * 删除物料分类
     *
     * <AUTHOR>
     * @date  2025/03/20 11:29
     */
    @ApiOperationSupport(order = 4)
    @ApiOperation("删除物料分类")
    @CommonLog("删除物料分类")
    @SaCheckPermission("/biz/materialcategory/delete")
    @PostMapping("/biz/materialcategory/delete")
    public CommonResult<String> delete(@RequestBody @Valid @NotEmpty(message = "集合不能为空")
                                                   CommonValidList<MaterialCategoryIdParam> materialCategoryIdParamList) {
        materialCategoryService.delete(materialCategoryIdParamList);
        return CommonResult.ok();
    }

    /**
     * 获取物料分类详情
     *
     * <AUTHOR>
     * @date  2025/03/20 11:29
     */
    @ApiOperationSupport(order = 5)
    @ApiOperation("获取物料分类详情")
    @SaCheckPermission("/biz/materialcategory/detail")
    @GetMapping("/biz/materialcategory/detail")
    public CommonResult<MaterialCategory> detail(@Valid MaterialCategoryIdParam materialCategoryIdParam) {
        return CommonResult.data(materialCategoryService.detail(materialCategoryIdParam));
    }
}
