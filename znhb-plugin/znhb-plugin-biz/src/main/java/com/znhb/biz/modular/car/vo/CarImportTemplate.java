package com.znhb.biz.modular.car.vo;

/**
 * 导入模板数据对象
 */

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentFontStyle;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@HeadRowHeight(30)  //表头行高
@ContentRowHeight(15)  //内容行高
@ColumnWidth(18)  //列宽
@ContentFontStyle(fontHeightInPoints = (short) 12)  //字体大小
public class CarImportTemplate {
    @ExcelProperty(value = "车牌号码*")
    private String plateNumber;

    @ExcelProperty(value = "车牌颜色*")
    private String plateColor;

    @ExcelProperty(value = "所在部门ID")
    private String orgId;

    @ExcelProperty(value = "身份证号码")
    private String ownerIdCard;

    @ExcelProperty(value = "通行规则ID")
    private String ruleId;

    @ExcelProperty(value = "有效期")
    private String validityPeriod;

    @ExcelProperty(value = "车主姓名")
    private String ownerName;

    @ExcelProperty(value = "车主电话")
    private String ownerPhone;
}