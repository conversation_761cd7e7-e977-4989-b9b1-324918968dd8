package com.znhb.biz.modular.area.result;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 区域树形结果
 *
 * <AUTHOR>
 * @date 2025-04-11
 */
@Getter
@Setter
@ApiModel("区域树形结果")
public class AreaTreeResult {

    /** 主键ID */
    @ApiModelProperty(value = "主键ID")
    private String id;

    /** 区域类型 */
    @ApiModelProperty(value = "区域类型")
    private String areaType;

    /** 区域名称 */
    @ApiModelProperty(value = "区域名称")
    private String areaName;

    /** 区域编码 */
    @ApiModelProperty(value = "区域编码")
    private String areaCode;

    /** 区域负责人ID */
    @ApiModelProperty(value = "区域负责人ID")
    private String managerId;

    /** 区域负责人姓名 */
    @ApiModelProperty(value = "区域负责人姓名")
    private String managerName;

    /** 上级区域ID */
    @ApiModelProperty(value = "上级区域ID")
    private String parentId;

    /** 排序码 */
    @ApiModelProperty(value = "排序码")
    private Integer sortCode;

    /** 备注 */
    @ApiModelProperty(value = "备注")
    private String remark;

    /** 子区域列表 */
    @ApiModelProperty(value = "子区域列表")
    private List<AreaTreeResult> children;
}
