package com.znhb.biz.modular.ruigang.tasker;

import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.znhb.biz.api.SuperLowerApi;
import com.znhb.biz.modular.internalVehicleRecord.entity.InternalVehicleRecord;
import com.znhb.biz.modular.internalVehicleRecord.service.InternalVehicleRecordService;
import com.znhb.common.timer.CommonTimerTaskRunner;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * @description: 内部运输车辆台账同步至环保平台
 */
@Slf4j
@Component
public class InternalVehicleAccessRecordTasker implements CommonTimerTaskRunner {

    @Resource
    private SuperLowerApi superLowerApi;

    @Resource
    private InternalVehicleRecordService internalVehicleRecordService;

    @Override
    public void action() {

        List<InternalVehicleRecord> internalVehicleRecords = internalVehicleRecordService.queryInternalVehicleAccessRecord();
        for (InternalVehicleRecord internalVehicleRecord : internalVehicleRecords) {
            Map<String, Object> map = new HashMap<>();
            map.put("accessId", internalVehicleRecord.getId());
            map.put("vin", internalVehicleRecord.getVinCode());
            map.put("inTime", DateUtil.formatDateTime(internalVehicleRecord.getCreateTime()));
            map.put("outTime", DateUtil.formatDateTime(internalVehicleRecord.getOutCreateTime()));
            boolean result = superLowerApi.pushInternalVehicleAccessRecord(map);
            log.info("内部车辆运输台账数据推送：{}", JSONUtil.toJsonStr(map));
            if (result) {
                internalVehicleRecordService.updateForSync(internalVehicleRecord.getId());
            }
        }
    }
}
