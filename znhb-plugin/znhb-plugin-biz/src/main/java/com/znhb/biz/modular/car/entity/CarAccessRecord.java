/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package com.znhb.biz.modular.car.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import com.znhb.common.pojo.CommonEntity;

import java.util.Date;

/**
 * 小车进出记录实体
 *
 * <AUTHOR>
 * @date  2025/04/10 15:00
 **/
@Getter
@Setter
@TableName("t_car_access_record")
public class CarAccessRecord extends CommonEntity {

    /** id */
    @TableId
    @ApiModelProperty(value = "id", position = 1)
    private String id;

    /** 小车ID */
    @ApiModelProperty(value = "小车ID", position = 2)
    private String carId;

    /** 车牌号码 */
    @ApiModelProperty(value = "车牌号码", position = 3)
    private String plateNumber;

    /** 进厂时间 */
    @ApiModelProperty(value = "进厂时间", position = 4)
    private Date entryTime;

    /** 进厂道闸 */
    @ApiModelProperty(value = "进厂道闸", position = 5)
    private String entryGate;

    /** 进厂抓拍图片URL */
    @ApiModelProperty(value = "进厂抓拍图片URL", position = 6)
    private String entryImageUrl;

    /** 出厂时间 */
    @ApiModelProperty(value = "出厂时间", position = 7)
    private Date exitTime;

    /** 出厂道闸 */
    @ApiModelProperty(value = "出厂道闸", position = 8)
    private String exitGate;

    /** 出厂抓拍图片URL */
    @ApiModelProperty(value = "出厂抓拍图片URL", position = 9)
    private String exitImageUrl;

    /** 状态(0-未出厂,1-已出厂) */
    @ApiModelProperty(value = "状态(0-未出厂,1-已出厂)", position = 10)
    private String status;

    /** 停留时长(分钟) */
    @ApiModelProperty(value = "停留时长(分钟)", position = 11)
    private Integer stayDuration;

    /** 同步状态(0-未同步,1-已同步) */
    @ApiModelProperty(value = "同步状态(0-未同步,1-已同步)", position = 12)
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String syncStatus;

    /** 开闸类型 */
    @ApiModelProperty(value = "入厂开闸类型", position = 13)
    private String inOpenGateType;

    /** 开闸类型 */
    @ApiModelProperty(value = "出厂开闸类型", position = 14)
    private String outOpenGateType;


    /** 入厂抓拍设备ID */
    @ApiModelProperty(value = "入厂抓拍设备ID", position = 15)
    private String entryCaptureDeviceId;

    /** 出厂抓拍设备ID */
    @ApiModelProperty(value = "出厂抓拍设备ID", position = 16)
    private String exitCaptureDeviceId;


    /** 进厂道闸 */
    @ApiModelProperty(value = "进厂道闸", position = 17)
    private String entryGateId;

    /** 出厂道闸 */
    @ApiModelProperty(value = "出厂道闸", position = 18)
    private String exitGateId;

    /** 入厂数据录入类型 */
    @ApiModelProperty(value = "入厂数据录入类型", position = 19)
    private String inRecordType;

    /** 出厂数据录入类型 */
    @ApiModelProperty(value = "出厂数据录入类型", position = 20)
    private String outRecordType;

    /** 备注 */
    private String remark;

}
