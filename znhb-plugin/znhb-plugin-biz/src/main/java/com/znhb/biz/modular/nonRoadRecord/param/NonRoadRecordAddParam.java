/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package com.znhb.biz.modular.nonRoadRecord.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 非移机械台账添加参数
 *
 * <AUTHOR>
 * @date  2025/04/03 11:32
 **/
@Getter
@Setter
public class NonRoadRecordAddParam {

    /** 非移机械备案id */
    @ApiModelProperty(value = "非移机械备案id", position = 2)
    private String vehicleId;

    /** 环保登记编码 */
    @ApiModelProperty(value = "环保登记编码", position = 3)
    private String envRegCode;

    /** 机械生产日期 */
    @ApiModelProperty(value = "机械生产日期", position = 4)
    private String productionDate;

    /** 车牌号码 */
    @ApiModelProperty(value = "车牌号码", position = 5)
    private String plateNumber;

    /** 排放标准 */
    @ApiModelProperty(value = "排放标准", position = 6)
    private String emissionStandard;

    /** 燃料类型 */
    @ApiModelProperty(value = "燃料类型", position = 7)
    private String fuelType;

    /** 机械种类 */
    @ApiModelProperty(value = "机械种类", position = 8)
    private String machineryType;

    /** 机械环保代码/产品识别码(PIN) */
    @ApiModelProperty(value = "机械环保代码/产品识别码(PIN)", position = 9)
    private String pinCode;

    /** 机械型号 */
    @ApiModelProperty(value = "机械型号", position = 10)
    private String machineryModel;

    /** 发动机型号 */
    @ApiModelProperty(value = "发动机型号", position = 11)
    private String engineModel;

    /** 发动机生产厂 */
    @ApiModelProperty(value = "发动机生产厂", position = 12)
    private String engineManufacturer;

    /** 发动机编号 */
    @ApiModelProperty(value = "发动机编号", position = 13)
    private String engineSerialNumber;

    /** 整车(机)铭牌照片 */
    @ApiModelProperty(value = "整车(机)铭牌照片", position = 14)
    private String machineryNameplateImage;

    /** 发动机铭牌照片 */
    @ApiModelProperty(value = "发动机铭牌照片", position = 15)
    private String engineNameplateImage;

    /** 机械环保信息标签照片 */
    @ApiModelProperty(value = "机械环保信息标签照片", position = 16)
    private String envInfoLabelImage;

    /** 所属人(单位) */
    @ApiModelProperty(value = "所属人(单位)", position = 17)
    private String ownerInfo;

    /** 同步状态 */
    @ApiModelProperty(value = "同步状态", position = 18)
    private String syncStatus;

    /** 进厂日期 */
    @ApiModelProperty(value = "进厂日期", position = 19)
    private String inCreateTime;

    /** 出厂日期 */
    @ApiModelProperty(value = "出厂日期", position = 20)
    private String outCreateTime;

    /** 进厂抓拍 */
    @ApiModelProperty(value = "进厂抓拍", position = 21)
    private String inCarImageUrl;

    /** 出厂抓拍 */
    @ApiModelProperty(value = "出厂抓拍", position = 22)
    private String outCarImageUrl;

    /** 出厂状态 */
    @ApiModelProperty(value = "出厂状态", position = 24)
    private String outStatus;

    /** 环保二维码图片 */
    @ApiModelProperty(value = "环保二维码图片", position = 25)
    private String envQrCodeImage;

}
