package com.znhb.biz.modular.cyft.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@DS("slave")
public interface  CyftCarRecordMapper {

    @Select("select * from t_eg_car_record where push = '1' limit 1000")
    List<Map<String, Object>>  cyftQueryForSync();

    @Update("update t_eg_car_record set category = #{map.category}, weight = #{map.weight} ,\n" +
            "            tare = #{map.tare} , gross = #{map.gross}, goods = #{map.goods} , suttle = #{map.suttle},\n" +
            "            orderno = #{map.orderno} ,source_unit = #{map.source_unit},\n" +
            "            target_unit = #{map.target_unit} , sync = '0'," +
            "            push = '2'\n" +
            "            where id = #{map.id}")
    void carRecordUpdateOut(@Param("map") Map<String,Object> map);




    @Update("update t_eg_car_record_cyft set\n" +
            "            in_goods_name = #{map.in_goods_name},\n" +
            "            team_name = #{map.team_name},\n" +
            "            out_goods_name = #{map.out_goods_name},\n" +
            "            contract_number = #{map.contract_number},\n" +
            "            business_type = #{map.business_type},\n" +
            "            unloading_location = #{map.unloading_location},\n" +
            "            source = #{map.source},\n" +
            "            target = #{map.target},\n" +
            "            supplier = #{map.supplier},\n" +
            "            customer = #{map.customer},\n" +
            "            receiver = #{map.receiver},\n" +
            "            receive_time = #{map.receive_time},\n" +
            "            receive_gross = #{map.receive_gross},\n" +
            "            receive_gross_measure_scale = #{map.receive_gross_measure_scale},\n" +
            "            receive_gross_measure_point = #{map.receive_gross_measure_point},\n" +
            "            receive_gross_time = #{map.receive_gross_time},\n" +
            "            receive_tare = #{map.receive_tare},\n" +
            "            receive_tare_measure_scale = #{map.receive_tare_measure_scale},\n" +
            "            receive_tare_measure_point = #{map.receive_tare_measure_point},\n" +
            "            receive_tare_time = #{map.receive_tare_time},\n" +
            "            receive_suttle = #{map.receive_suttle},\n" +
            "            receive_suttle_measure_scale = #{map.receive_suttle_measure_scale},\n" +
            "            receive_suttle_time = #{map.receive_suttle_time},\n" +
            "            receive_suttle_measure_point = #{map.receive_suttle_measure_point},\n" +
            "            delivery_gross = #{map.delivery_gross},\n" +
            "            delivery_gross_measure_scale = #{map.delivery_gross_measure_scale},\n" +
            "            delivery_gross_measure_point = #{map.delivery_gross_measure_point},\n" +
            "            delivery_gross_time = #{map.delivery_gross_time},\n" +
            "            delivery_tare = #{map.delivery_tare},\n" +
            "            delivery_tare_measure_scale = #{map.delivery_tare_measure_scale},\n" +
            "            delivery_tare_time = #{map.delivery_tare_time},\n" +
            "            delivery_tare_measure_point = #{map.delivery_tare_measure_point},\n" +
            "            delivery_suttle = #{map.delivery_suttle},\n" +
            "            delivery_suttle_measure_scale = #{map.delivery_suttle_measure_scale},\n" +
            "            delivery_suttle_time = #{map.delivery_suttle_time},\n" +
            "            delivery_suttle_measure_point = #{map.delivery_suttle_measure_point},\n" +
            "            settlement_amount = #{map.settlement_amount},\n" +
            "            sync = #{map.sync},\n" +
            "            material_id = #{map.material_id},\n" +
            "            goods_name = #{map.goods_name}\n" +
            "            where record_id = #{map.record_id}")
    void updateCarRecordCyft(@Param("map")Map<String,Object> map);

    @Update("update t_eg_car_record_cyft set is_delete = '1' where id = #{id}")
    void deleteCarReport(@Param("id") String id);

}
