<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.znhb</groupId>
        <artifactId>znhb-plugin</artifactId>
        <version>2.0.0</version>
    </parent>

    <artifactId>znhb-plugin-sys</artifactId>
    <packaging>jar</packaging>
    <description>系统功能插件</description>

    <dependencies>
        <!-- 每个插件都要引入自己的对外接口 -->
        <dependency>
            <groupId>com.znhb</groupId>
            <artifactId>znhb-plugin-sys-api</artifactId>
        </dependency>

        <!-- 引入登录鉴权接口，用于实现其登录所需逻辑 -->
        <dependency>
            <groupId>com.znhb</groupId>
            <artifactId>znhb-plugin-auth-api</artifactId>
        </dependency>

        <!-- 引入开发工具接口，用于配置信息 -->
        <dependency>
            <groupId>com.znhb</groupId>
            <artifactId>znhb-plugin-dev-api</artifactId>
        </dependency>

        <!-- 引入移动端接口，用于移动端菜单信息 -->
        <dependency>
            <groupId>com.znhb</groupId>
            <artifactId>znhb-plugin-mobile-api</artifactId>
        </dependency>
    </dependencies>
</project>