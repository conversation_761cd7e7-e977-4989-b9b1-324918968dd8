/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package com.znhb.wechat.modular.user.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.znhb.common.annotation.CommonLog;
import com.znhb.common.pojo.CommonResult;
import com.znhb.wechat.modular.user.entity.WechatUser;
import com.znhb.wechat.modular.user.param.WechatUserPageParam;
import com.znhb.wechat.modular.user.service.WechatUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;

/**
 * 微信用户控制器
 *
 * <AUTHOR>
 * @date 2024/01/01 10:00
 **/
@Api(tags = "微信用户管理")
@RestController
@RequestMapping("/wechat/user")
@Validated
public class WechatUserController {

    @Resource
    private WechatUserService wechatUserService;

    /**
     * 分页查询微信用户
     *
     * @param wechatUserPageParam 查询参数
     * @return 分页结果
     * <AUTHOR>
     * @date 2024/01/01 10:00
     **/
    @ApiOperation("分页查询微信用户")
    @SaCheckPermission("/wechat/user/page")
    @GetMapping("/page")
    public CommonResult<Page<WechatUser>> page(@Valid WechatUserPageParam wechatUserPageParam) {
        return CommonResult.ok(wechatUserService.page(wechatUserPageParam));
    }

    /**
     * 获取微信用户详情
     *
     * @param id 用户ID
     * @return 用户详情
     * <AUTHOR>
     * @date 2024/01/01 10:00
     **/
    @ApiOperation("获取微信用户详情")
    @SaCheckPermission("/wechat/user/detail")
    @GetMapping("/detail")
    public CommonResult<WechatUser> detail(@RequestParam @NotBlank(message = "id不能为空") String id) {
        return CommonResult.ok(wechatUserService.getById(id));
    }

    /**
     * 绑定系统用户
     *
     * @param openId 微信openId
     * @param userId 系统用户ID
     * @return 操作结果
     * <AUTHOR>
     * @date 2024/01/01 10:00
     **/
    @ApiOperation("绑定系统用户")
    @SaCheckPermission("/wechat/user/bind")
    @CommonLog("绑定系统用户")
    @PostMapping("/bind")
    public CommonResult<String> bindUser(@RequestParam @NotBlank(message = "openId不能为空") String openId,
                                        @RequestParam @NotBlank(message = "userId不能为空") String userId) {
        wechatUserService.bindUser(openId, userId);
        return CommonResult.ok();
    }

    /**
     * 解绑系统用户
     *
     * @param openId 微信openId
     * @return 操作结果
     * <AUTHOR>
     * @date 2024/01/01 10:00
     **/
    @ApiOperation("解绑系统用户")
    @SaCheckPermission("/wechat/user/unbind")
    @CommonLog("解绑系统用户")
    @PostMapping("/unbind")
    public CommonResult<String> unbindUser(@RequestParam @NotBlank(message = "openId不能为空") String openId) {
        wechatUserService.unbindUser(openId);
        return CommonResult.ok();
    }

    /**
     * 删除微信用户
     *
     * @param id 用户ID
     * @return 操作结果
     * <AUTHOR>
     * @date 2024/01/01 10:00
     **/
    @ApiOperation("删除微信用户")
    @SaCheckPermission("/wechat/user/delete")
    @CommonLog("删除微信用户")
    @PostMapping("/delete")
    public CommonResult<String> delete(@RequestParam @NotBlank(message = "id不能为空") String id) {
        wechatUserService.delete(id);
        return CommonResult.ok();
    }
}
