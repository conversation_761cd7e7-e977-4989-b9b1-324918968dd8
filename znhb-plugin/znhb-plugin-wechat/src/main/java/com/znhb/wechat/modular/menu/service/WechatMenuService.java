/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package com.znhb.wechat.modular.menu.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.znhb.wechat.modular.menu.entity.WechatMenu;
import com.znhb.wechat.modular.menu.param.WechatMenuAddParam;
import com.znhb.wechat.modular.menu.param.WechatMenuEditParam;

import java.util.List;

/**
 * 微信菜单服务接口
 *
 * <AUTHOR>
 * @date 2024/01/01 10:00
 **/
public interface WechatMenuService extends IService<WechatMenu> {

    /**
     * 获取菜单树
     *
     * @return 菜单树
     * <AUTHOR>
     * @date 2024/01/01 10:00
     **/
    List<WechatMenu> tree();

    /**
     * 添加菜单
     *
     * @param wechatMenuAddParam 添加参数
     * <AUTHOR>
     * @date 2024/01/01 10:00
     **/
    void add(WechatMenuAddParam wechatMenuAddParam);

    /**
     * 编辑菜单
     *
     * @param wechatMenuEditParam 编辑参数
     * <AUTHOR>
     * @date 2024/01/01 10:00
     **/
    void edit(WechatMenuEditParam wechatMenuEditParam);

    /**
     * 删除菜单
     *
     * @param id 菜单ID
     * <AUTHOR>
     * @date 2024/01/01 10:00
     **/
    void delete(String id);

    /**
     * 发布菜单到微信
     *
     * <AUTHOR>
     * @date 2024/01/01 10:00
     **/
    void publishMenu();

    /**
     * 删除微信菜单
     *
     * <AUTHOR>
     * @date 2024/01/01 10:00
     **/
    void deleteWechatMenu();
}
