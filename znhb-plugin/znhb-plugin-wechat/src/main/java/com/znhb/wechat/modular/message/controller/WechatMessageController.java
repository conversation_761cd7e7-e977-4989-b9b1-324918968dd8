/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package com.znhb.wechat.modular.message.controller;

import com.znhb.common.annotation.CommonLog;
import com.znhb.wechat.modular.message.service.WechatMessageService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.bean.message.WxMpXmlMessage;
import me.chanjar.weixin.mp.bean.message.WxMpXmlOutMessage;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 微信消息处理控制器
 *
 * <AUTHOR>
 * @date 2024/01/01 10:00
 **/
@Api(tags = "微信消息处理")
@RestController
@RequestMapping("/wechat/message")
@Slf4j
public class WechatMessageController {

    @Resource
    private WxMpService wxMpService;

    @Resource
    private WechatMessageService wechatMessageService;

    /**
     * 微信公众号消息接收处理
     *
     * @param requestBody 微信推送的消息
     * @param signature 微信签名
     * @param timestamp 时间戳
     * @param nonce 随机数
     * @param echostr 随机字符串
     * @return 处理结果
     * <AUTHOR>
     * @date 2024/01/01 10:00
     **/
    @PostMapping("/receive")
    @ApiOperation("微信消息接收")
    @CommonLog("微信消息接收")
    public String receiveMessage(@RequestBody String requestBody,
                                @RequestParam("signature") String signature,
                                @RequestParam("timestamp") String timestamp,
                                @RequestParam("nonce") String nonce,
                                @RequestParam(value = "echostr", required = false) String echostr) {
        try {
            log.info("接收到微信消息: {}", requestBody);
            
            // 验证签名
            if (!wxMpService.checkSignature(timestamp, nonce, signature)) {
                log.error("微信消息签名验证失败");
                return "error";
            }

            // 如果是验证请求，直接返回echostr
            if (echostr != null) {
                log.info("微信公众号验证成功");
                return echostr;
            }

            // 解析消息
            WxMpXmlMessage inMessage = WxMpXmlMessage.fromXml(requestBody);
            
            // 处理消息
            WxMpXmlOutMessage outMessage = wechatMessageService.handleMessage(inMessage);
            
            if (outMessage != null) {
                return outMessage.toXml();
            }
            
            return "success";
        } catch (Exception e) {
            log.error("处理微信消息异常", e);
            return "error";
        }
    }

    /**
     * 微信公众号验证
     *
     * @param signature 微信签名
     * @param timestamp 时间戳
     * @param nonce 随机数
     * @param echostr 随机字符串
     * @return 验证结果
     * <AUTHOR>
     * @date 2024/01/01 10:00
     **/
    @GetMapping("/receive")
    @ApiOperation("微信公众号验证")
    public String verifyWechat(@RequestParam("signature") String signature,
                              @RequestParam("timestamp") String timestamp,
                              @RequestParam("nonce") String nonce,
                              @RequestParam("echostr") String echostr) {
        try {
            if (wxMpService.checkSignature(timestamp, nonce, signature)) {
                log.info("微信公众号验证成功");
                return echostr;
            } else {
                log.error("微信公众号验证失败");
                return "error";
            }
        } catch (Exception e) {
            log.error("微信公众号验证异常", e);
            return "error";
        }
    }
}
