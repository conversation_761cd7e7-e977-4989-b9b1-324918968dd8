/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package com.znhb.wechat.modular.template.service;

import me.chanjar.weixin.mp.bean.template.WxMpTemplateMessage;

/**
 * 微信模板消息服务接口
 *
 * <AUTHOR>
 * @date 2024/01/01 10:00
 **/
public interface WechatTemplateService {

    /**
     * 发送模板消息
     *
     * @param templateMessage 模板消息
     * @return 消息ID
     * <AUTHOR>
     * @date 2024/01/01 10:00
     **/
    String sendTemplateMessage(WxMpTemplateMessage templateMessage);

    /**
     * 发送车辆通行通知
     *
     * @param openId 用户openId
     * @param plateNumber 车牌号
     * @param passTime 通行时间
     * @param location 通行地点
     * @return 消息ID
     * <AUTHOR>
     * @date 2024/01/01 10:00
     **/
    String sendVehiclePassNotice(String openId, String plateNumber, String passTime, String location);

    /**
     * 发送系统通知
     *
     * @param openId 用户openId
     * @param title 通知标题
     * @param content 通知内容
     * @param url 跳转链接
     * @return 消息ID
     * <AUTHOR>
     * @date 2024/01/01 10:00
     **/
    String sendSystemNotice(String openId, String title, String content, String url);

    /**
     * 发送绑定成功通知
     *
     * @param openId 用户openId
     * @param userName 用户名
     * @param bindTime 绑定时间
     * @return 消息ID
     * <AUTHOR>
     * @date 2024/01/01 10:00
     **/
    String sendBindSuccessNotice(String openId, String userName, String bindTime);
}
