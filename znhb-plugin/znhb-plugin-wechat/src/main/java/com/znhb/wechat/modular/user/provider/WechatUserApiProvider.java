/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package com.znhb.wechat.modular.user.provider;

import com.znhb.common.pojo.CommonResult;
import com.znhb.wechat.api.WechatUserApi;
import com.znhb.wechat.modular.user.service.WechatUserService;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.bean.result.WxMpUser;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 微信用户API实现类
 *
 * <AUTHOR>
 * @date 2024/01/01 10:00
 **/
@Slf4j
@Service
public class WechatUserApiProvider implements WechatUserApi {

    @Resource
    private WxMpService wxMpService;

    @Resource
    private WechatUserService wechatUserService;

    @Override
    public WxMpUser getUserInfo(String openId) {
        try {
            return wxMpService.getUserService().userInfo(openId);
        } catch (Exception e) {
            log.error("获取微信用户信息失败", e);
            return null;
        }
    }

    @Override
    public CommonResult<String> syncUserInfo(String openId) {
        try {
            WxMpUser wxMpUser = getUserInfo(openId);
            if (wxMpUser != null) {
                wechatUserService.syncUserInfo(wxMpUser);
                return CommonResult.ok("同步成功");
            } else {
                return CommonResult.error("获取用户信息失败");
            }
        } catch (Exception e) {
            log.error("同步微信用户信息失败", e);
            return CommonResult.error("同步失败：" + e.getMessage());
        }
    }

    @Override
    public CommonResult<String> bindUser(String openId, String userId) {
        try {
            wechatUserService.bindUser(openId, userId);
            return CommonResult.ok("绑定成功");
        } catch (Exception e) {
            log.error("绑定用户失败", e);
            return CommonResult.error("绑定失败：" + e.getMessage());
        }
    }

    @Override
    public CommonResult<String> unbindUser(String openId) {
        try {
            wechatUserService.unbindUser(openId);
            return CommonResult.ok("解绑成功");
        } catch (Exception e) {
            log.error("解绑用户失败", e);
            return CommonResult.error("解绑失败：" + e.getMessage());
        }
    }
}
