/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package com.znhb.wechat.modular.message.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.znhb.wechat.modular.message.service.WechatMessageService;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.api.WxConsts;
import me.chanjar.weixin.mp.bean.message.WxMpXmlMessage;
import me.chanjar.weixin.mp.bean.message.WxMpXmlOutMessage;
import me.chanjar.weixin.mp.bean.message.WxMpXmlOutTextMessage;
import org.springframework.stereotype.Service;

/**
 * 微信消息服务实现类
 *
 * <AUTHOR>
 * @date 2024/01/01 10:00
 **/
@Slf4j
@Service
public class WechatMessageServiceImpl implements WechatMessageService {

    @Override
    public WxMpXmlOutMessage handleMessage(WxMpXmlMessage inMessage) {
        try {
            String msgType = inMessage.getMsgType();
            log.info("处理微信消息，消息类型: {}, 发送者: {}", msgType, inMessage.getFromUser());

            switch (msgType) {
                case WxConsts.XmlMsgType.TEXT:
                    return handleTextMessage(inMessage);
                case WxConsts.XmlMsgType.EVENT:
                    return handleEventMessage(inMessage);
                case WxConsts.XmlMsgType.IMAGE:
                    return WxMpXmlOutMessage.TEXT()
                            .content("收到图片消息")
                            .fromUser(inMessage.getToUser())
                            .toUser(inMessage.getFromUser())
                            .build();
                case WxConsts.XmlMsgType.VOICE:
                    return WxMpXmlOutMessage.TEXT()
                            .content("收到语音消息")
                            .fromUser(inMessage.getToUser())
                            .toUser(inMessage.getFromUser())
                            .build();
                default:
                    return WxMpXmlOutMessage.TEXT()
                            .content("感谢您的消息，我们会尽快回复")
                            .fromUser(inMessage.getToUser())
                            .toUser(inMessage.getFromUser())
                            .build();
            }
        } catch (Exception e) {
            log.error("处理微信消息异常", e);
            return null;
        }
    }

    @Override
    public WxMpXmlOutMessage handleTextMessage(WxMpXmlMessage inMessage) {
        String content = inMessage.getContent();
        log.info("收到文本消息: {}", content);

        String replyContent;
        if ("帮助".equals(content) || "help".equalsIgnoreCase(content)) {
            replyContent = "欢迎使用清洁运输门禁管理平台！\n\n" +
                    "您可以发送以下关键词获取帮助：\n" +
                    "• 帮助 - 查看帮助信息\n" +
                    "• 绑定 - 绑定系统账号\n" +
                    "• 状态 - 查看绑定状态\n" +
                    "• 通知 - 查看最新通知";
        } else if ("绑定".equals(content)) {
            replyContent = "请联系管理员进行账号绑定，或访问系统进行自助绑定。";
        } else if ("状态".equals(content)) {
            replyContent = "您的账号绑定状态：未绑定\n请先进行账号绑定。";
        } else if ("通知".equals(content)) {
            replyContent = "暂无新通知";
        } else {
            replyContent = "感谢您的消息！\n发送"帮助"获取更多信息。";
        }

        return WxMpXmlOutMessage.TEXT()
                .content(replyContent)
                .fromUser(inMessage.getToUser())
                .toUser(inMessage.getFromUser())
                .build();
    }

    @Override
    public WxMpXmlOutMessage handleEventMessage(WxMpXmlMessage inMessage) {
        String event = inMessage.getEvent();
        log.info("收到事件消息: {}", event);

        switch (event) {
            case WxConsts.EventType.SUBSCRIBE:
                return handleSubscribeEvent(inMessage);
            case WxConsts.EventType.UNSUBSCRIBE:
                return handleUnsubscribeEvent(inMessage);
            case WxConsts.EventType.CLICK:
                return handleClickEvent(inMessage);
            default:
                return null;
        }
    }

    @Override
    public WxMpXmlOutMessage handleSubscribeEvent(WxMpXmlMessage inMessage) {
        log.info("用户关注公众号: {}", inMessage.getFromUser());
        
        String welcomeMsg = "🎉 欢迎关注清洁运输门禁管理平台！\n\n" +
                "我们为您提供：\n" +
                "• 车辆通行记录查询\n" +
                "• 实时通行状态\n" +
                "• 系统通知推送\n" +
                "• 便民服务功能\n\n" +
                "发送"帮助"获取更多功能介绍。";

        return WxMpXmlOutMessage.TEXT()
                .content(welcomeMsg)
                .fromUser(inMessage.getToUser())
                .toUser(inMessage.getFromUser())
                .build();
    }

    @Override
    public WxMpXmlOutMessage handleUnsubscribeEvent(WxMpXmlMessage inMessage) {
        log.info("用户取消关注公众号: {}", inMessage.getFromUser());
        // 取消关注事件不需要回复消息
        return null;
    }

    @Override
    public WxMpXmlOutMessage handleClickEvent(WxMpXmlMessage inMessage) {
        String eventKey = inMessage.getEventKey();
        log.info("用户点击菜单: {}", eventKey);

        String replyContent;
        switch (eventKey) {
            case "MENU_HELP":
                replyContent = "帮助中心\n\n如需帮助，请联系客服或查看使用说明。";
                break;
            case "MENU_BIND":
                replyContent = "账号绑定\n\n请访问系统进行账号绑定操作。";
                break;
            case "MENU_STATUS":
                replyContent = "通行状态\n\n您可以查询最新的车辆通行记录。";
                break;
            default:
                replyContent = "感谢您的操作！";
                break;
        }

        return WxMpXmlOutMessage.TEXT()
                .content(replyContent)
                .fromUser(inMessage.getToUser())
                .toUser(inMessage.getFromUser())
                .build();
    }
}
