/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package com.znhb.wechat.modular.message.provider;

import com.znhb.common.pojo.CommonResult;
import com.znhb.wechat.api.WechatMessageApi;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.bean.kefu.WxMpKefuMessage;
import me.chanjar.weixin.mp.bean.template.WxMpTemplateMessage;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 微信消息API实现类
 *
 * <AUTHOR>
 * @date 2024/01/01 10:00
 **/
@Slf4j
@Service
public class WechatMessageApiProvider implements WechatMessageApi {

    @Resource
    private WxMpService wxMpService;

    @Override
    public CommonResult<String> sendTextMessage(String openId, String content) {
        try {
            WxMpKefuMessage message = WxMpKefuMessage.TEXT()
                    .toUser(openId)
                    .content(content)
                    .build();
            
            boolean result = wxMpService.getKefuService().sendKefuMessage(message);
            if (result) {
                return CommonResult.ok("发送成功");
            } else {
                return CommonResult.error("发送失败");
            }
        } catch (Exception e) {
            log.error("发送文本消息失败", e);
            return CommonResult.error("发送失败：" + e.getMessage());
        }
    }

    @Override
    public CommonResult<String> sendTemplateMessage(WxMpTemplateMessage templateMessage) {
        try {
            String msgId = wxMpService.getTemplateMsgService().sendTemplateMsg(templateMessage);
            return CommonResult.ok(msgId);
        } catch (Exception e) {
            log.error("发送模板消息失败", e);
            return CommonResult.error("发送失败：" + e.getMessage());
        }
    }

    @Override
    public CommonResult<String> sendImageMessage(String openId, String mediaId) {
        try {
            WxMpKefuMessage message = WxMpKefuMessage.IMAGE()
                    .toUser(openId)
                    .mediaId(mediaId)
                    .build();
            
            boolean result = wxMpService.getKefuService().sendKefuMessage(message);
            if (result) {
                return CommonResult.ok("发送成功");
            } else {
                return CommonResult.error("发送失败");
            }
        } catch (Exception e) {
            log.error("发送图片消息失败", e);
            return CommonResult.error("发送失败：" + e.getMessage());
        }
    }

    @Override
    public CommonResult<String> sendVoiceMessage(String openId, String mediaId) {
        try {
            WxMpKefuMessage message = WxMpKefuMessage.VOICE()
                    .toUser(openId)
                    .mediaId(mediaId)
                    .build();
            
            boolean result = wxMpService.getKefuService().sendKefuMessage(message);
            if (result) {
                return CommonResult.ok("发送成功");
            } else {
                return CommonResult.error("发送失败");
            }
        } catch (Exception e) {
            log.error("发送语音消息失败", e);
            return CommonResult.error("发送失败：" + e.getMessage());
        }
    }

    @Override
    public CommonResult<String> sendVideoMessage(String openId, String mediaId, String title, String description) {
        try {
            WxMpKefuMessage message = WxMpKefuMessage.VIDEO()
                    .toUser(openId)
                    .mediaId(mediaId)
                    .title(title)
                    .description(description)
                    .build();
            
            boolean result = wxMpService.getKefuService().sendKefuMessage(message);
            if (result) {
                return CommonResult.ok("发送成功");
            } else {
                return CommonResult.error("发送失败");
            }
        } catch (Exception e) {
            log.error("发送视频消息失败", e);
            return CommonResult.error("发送失败：" + e.getMessage());
        }
    }
}
