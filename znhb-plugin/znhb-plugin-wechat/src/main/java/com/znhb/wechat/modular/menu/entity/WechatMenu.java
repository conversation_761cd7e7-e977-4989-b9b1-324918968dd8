/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package com.znhb.wechat.modular.menu.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.znhb.common.pojo.CommonEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 微信菜单实体
 *
 * <AUTHOR>
 * @date 2024/01/01 10:00
 **/
@Getter
@Setter
@TableName("wx_menu")
@ApiModel(value = "微信菜单")
public class WechatMenu extends CommonEntity {

    /** id */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id", position = 1)
    private String id;

    /** 父级ID */
    @ApiModelProperty(value = "父级ID", position = 2)
    private String parentId;

    /** 菜单名称 */
    @ApiModelProperty(value = "菜单名称", position = 3)
    private String menuName;

    /** 菜单类型 */
    @ApiModelProperty(value = "菜单类型", position = 4)
    private String menuType;

    /** 菜单KEY */
    @ApiModelProperty(value = "菜单KEY", position = 5)
    private String menuKey;

    /** 菜单链接 */
    @ApiModelProperty(value = "菜单链接", position = 6)
    private String url;

    /** 媒体文件ID */
    @ApiModelProperty(value = "媒体文件ID", position = 7)
    private String mediaId;

    /** 小程序appid */
    @ApiModelProperty(value = "小程序appid", position = 8)
    private String appid;

    /** 小程序页面路径 */
    @ApiModelProperty(value = "小程序页面路径", position = 9)
    private String pagepath;

    /** 排序 */
    @ApiModelProperty(value = "排序", position = 10)
    private Integer sortCode;

    /** 状态 */
    @ApiModelProperty(value = "状态", position = 11)
    private String status;

    /** 子菜单 */
    @TableField(exist = false)
    @ApiModelProperty(value = "子菜单", position = 12)
    private List<WechatMenu> children;
}
