/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package com.znhb.wechat.modular.message.service;

import me.chanjar.weixin.mp.bean.message.WxMpXmlMessage;
import me.chanjar.weixin.mp.bean.message.WxMpXmlOutMessage;

/**
 * 微信消息服务接口
 *
 * <AUTHOR>
 * @date 2024/01/01 10:00
 **/
public interface WechatMessageService {

    /**
     * 处理微信消息
     *
     * @param inMessage 接收到的消息
     * @return 回复消息
     * <AUTHOR>
     * @date 2024/01/01 10:00
     **/
    WxMpXmlOutMessage handleMessage(WxMpXmlMessage inMessage);

    /**
     * 处理文本消息
     *
     * @param inMessage 接收到的消息
     * @return 回复消息
     * <AUTHOR>
     * @date 2024/01/01 10:00
     **/
    WxMpXmlOutMessage handleTextMessage(WxMpXmlMessage inMessage);

    /**
     * 处理事件消息
     *
     * @param inMessage 接收到的消息
     * @return 回复消息
     * <AUTHOR>
     * @date 2024/01/01 10:00
     **/
    WxMpXmlOutMessage handleEventMessage(WxMpXmlMessage inMessage);

    /**
     * 处理关注事件
     *
     * @param inMessage 接收到的消息
     * @return 回复消息
     * <AUTHOR>
     * @date 2024/01/01 10:00
     **/
    WxMpXmlOutMessage handleSubscribeEvent(WxMpXmlMessage inMessage);

    /**
     * 处理取消关注事件
     *
     * @param inMessage 接收到的消息
     * @return 回复消息
     * <AUTHOR>
     * @date 2024/01/01 10:00
     **/
    WxMpXmlOutMessage handleUnsubscribeEvent(WxMpXmlMessage inMessage);

    /**
     * 处理点击菜单事件
     *
     * @param inMessage 接收到的消息
     * @return 回复消息
     * <AUTHOR>
     * @date 2024/01/01 10:00
     **/
    WxMpXmlOutMessage handleClickEvent(WxMpXmlMessage inMessage);
}
