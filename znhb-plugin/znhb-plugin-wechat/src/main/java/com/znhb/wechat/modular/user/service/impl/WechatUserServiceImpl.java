/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package com.znhb.wechat.modular.user.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.znhb.common.exception.CommonException;
import com.znhb.common.page.CommonPageRequest;
import com.znhb.wechat.modular.user.entity.WechatUser;
import com.znhb.wechat.modular.user.mapper.WechatUserMapper;
import com.znhb.wechat.modular.user.param.WechatUserPageParam;
import com.znhb.wechat.modular.user.service.WechatUserService;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.mp.bean.result.WxMpUser;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

/**
 * 微信用户服务实现类
 *
 * <AUTHOR>
 * @date 2024/01/01 10:00
 **/
@Slf4j
@Service
public class WechatUserServiceImpl extends ServiceImpl<WechatUserMapper, WechatUser> implements WechatUserService {

    @Override
    public Page<WechatUser> page(WechatUserPageParam wechatUserPageParam) {
        LambdaQueryWrapper<WechatUser> queryWrapper = new LambdaQueryWrapper<>();
        
        if (StrUtil.isNotBlank(wechatUserPageParam.getOpenId())) {
            queryWrapper.like(WechatUser::getOpenId, wechatUserPageParam.getOpenId());
        }
        if (StrUtil.isNotBlank(wechatUserPageParam.getNickname())) {
            queryWrapper.like(WechatUser::getNickname, wechatUserPageParam.getNickname());
        }
        if (StrUtil.isNotBlank(wechatUserPageParam.getSubscribeStatus())) {
            queryWrapper.eq(WechatUser::getSubscribeStatus, wechatUserPageParam.getSubscribeStatus());
        }
        if (StrUtil.isNotBlank(wechatUserPageParam.getBindStatus())) {
            if ("BIND".equals(wechatUserPageParam.getBindStatus())) {
                queryWrapper.isNotNull(WechatUser::getBindUserId);
            } else {
                queryWrapper.isNull(WechatUser::getBindUserId);
            }
        }
        if (StrUtil.isNotBlank(wechatUserPageParam.getSex())) {
            queryWrapper.eq(WechatUser::getSex, wechatUserPageParam.getSex());
        }
        if (StrUtil.isNotBlank(wechatUserPageParam.getProvince())) {
            queryWrapper.like(WechatUser::getProvince, wechatUserPageParam.getProvince());
        }
        if (StrUtil.isNotBlank(wechatUserPageParam.getCity())) {
            queryWrapper.like(WechatUser::getCity, wechatUserPageParam.getCity());
        }
        
        queryWrapper.orderByDesc(WechatUser::getCreateTime);
        
        return this.page(CommonPageRequest.defaultPage(), queryWrapper);
    }

    @Override
    public WechatUser getByOpenId(String openId) {
        LambdaQueryWrapper<WechatUser> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WechatUser::getOpenId, openId);
        return this.getOne(queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public WechatUser syncUserInfo(WxMpUser wxMpUser) {
        WechatUser existUser = getByOpenId(wxMpUser.getOpenId());
        
        WechatUser wechatUser;
        if (ObjectUtil.isNull(existUser)) {
            // 新用户，创建记录
            wechatUser = new WechatUser();
            wechatUser.setOpenId(wxMpUser.getOpenId());
            wechatUser.setSubscribeTime(new Date(wxMpUser.getSubscribeTime() * 1000));
        } else {
            // 已存在用户，更新信息
            wechatUser = existUser;
        }
        
        // 更新用户信息
        wechatUser.setUnionId(wxMpUser.getUnionId());
        wechatUser.setNickname(wxMpUser.getNickname());
        wechatUser.setSex(getSexString(wxMpUser.getSex()));
        wechatUser.setHeadImgUrl(wxMpUser.getHeadImgUrl());
        wechatUser.setCountry(wxMpUser.getCountry());
        wechatUser.setProvince(wxMpUser.getProvince());
        wechatUser.setCity(wxMpUser.getCity());
        wechatUser.setLanguage(wxMpUser.getLanguage());
        wechatUser.setSubscribeStatus("SUBSCRIBED");
        wechatUser.setRemark(wxMpUser.getRemark());
        
        this.saveOrUpdate(wechatUser);
        return wechatUser;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateSubscribeStatus(String openId, String subscribeStatus) {
        LambdaUpdateWrapper<WechatUser> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(WechatUser::getOpenId, openId);
        updateWrapper.set(WechatUser::getSubscribeStatus, subscribeStatus);
        
        if ("UNSUBSCRIBED".equals(subscribeStatus)) {
            // 取消关注时，解除绑定
            updateWrapper.set(WechatUser::getBindUserId, null);
            updateWrapper.set(WechatUser::getBindTime, null);
        }
        
        this.update(updateWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void bindUser(String openId, String userId) {
        WechatUser wechatUser = getByOpenId(openId);
        if (ObjectUtil.isNull(wechatUser)) {
            throw new CommonException("微信用户不存在");
        }
        
        if (StrUtil.isNotBlank(wechatUser.getBindUserId())) {
            throw new CommonException("该微信用户已绑定系统账号");
        }
        
        LambdaUpdateWrapper<WechatUser> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(WechatUser::getOpenId, openId);
        updateWrapper.set(WechatUser::getBindUserId, userId);
        updateWrapper.set(WechatUser::getBindTime, new Date());
        
        this.update(updateWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void unbindUser(String openId) {
        LambdaUpdateWrapper<WechatUser> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(WechatUser::getOpenId, openId);
        updateWrapper.set(WechatUser::getBindUserId, null);
        updateWrapper.set(WechatUser::getBindTime, null);
        
        this.update(updateWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(String id) {
        WechatUser wechatUser = this.getById(id);
        if (ObjectUtil.isNull(wechatUser)) {
            throw new CommonException("微信用户不存在");
        }
        
        this.removeById(id);
    }

    /**
     * 转换性别字符串
     */
    private String getSexString(Integer sex) {
        if (ObjectUtil.isNull(sex)) {
            return "UNKNOWN";
        }
        switch (sex) {
            case 1:
                return "MALE";
            case 2:
                return "FEMALE";
            default:
                return "UNKNOWN";
        }
    }
}
