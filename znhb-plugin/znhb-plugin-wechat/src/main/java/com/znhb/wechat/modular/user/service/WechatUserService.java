/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package com.znhb.wechat.modular.user.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.znhb.wechat.modular.user.entity.WechatUser;
import com.znhb.wechat.modular.user.param.WechatUserPageParam;
import me.chanjar.weixin.mp.bean.result.WxMpUser;

/**
 * 微信用户服务接口
 *
 * <AUTHOR>
 * @date 2024/01/01 10:00
 **/
public interface WechatUserService extends IService<WechatUser> {

    /**
     * 分页查询微信用户
     *
     * @param wechatUserPageParam 查询参数
     * @return 分页结果
     * <AUTHOR>
     * @date 2024/01/01 10:00
     **/
    Page<WechatUser> page(WechatUserPageParam wechatUserPageParam);

    /**
     * 根据openId获取微信用户
     *
     * @param openId 微信openId
     * @return 微信用户
     * <AUTHOR>
     * @date 2024/01/01 10:00
     **/
    WechatUser getByOpenId(String openId);

    /**
     * 同步微信用户信息
     *
     * @param wxMpUser 微信用户信息
     * @return 本地用户信息
     * <AUTHOR>
     * @date 2024/01/01 10:00
     **/
    WechatUser syncUserInfo(WxMpUser wxMpUser);

    /**
     * 更新用户关注状态
     *
     * @param openId 微信openId
     * @param subscribeStatus 关注状态
     * <AUTHOR>
     * @date 2024/01/01 10:00
     **/
    void updateSubscribeStatus(String openId, String subscribeStatus);

    /**
     * 绑定系统用户
     *
     * @param openId 微信openId
     * @param userId 系统用户ID
     * <AUTHOR>
     * @date 2024/01/01 10:00
     **/
    void bindUser(String openId, String userId);

    /**
     * 解绑系统用户
     *
     * @param openId 微信openId
     * <AUTHOR>
     * @date 2024/01/01 10:00
     **/
    void unbindUser(String openId);

    /**
     * 删除微信用户
     *
     * @param id 用户ID
     * <AUTHOR>
     * @date 2024/01/01 10:00
     **/
    void delete(String id);
}
