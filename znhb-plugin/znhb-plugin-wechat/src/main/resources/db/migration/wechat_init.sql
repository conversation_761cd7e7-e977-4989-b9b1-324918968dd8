-- 微信公众号相关表结构

-- 微信用户表
CREATE TABLE `wx_user` (
  `id` varchar(64) NOT NULL COMMENT '主键ID',
  `open_id` varchar(128) NOT NULL COMMENT '微信openId',
  `union_id` varchar(128) DEFAULT NULL COMMENT '微信unionId',
  `nickname` varchar(255) DEFAULT NULL COMMENT '昵称',
  `sex` varchar(10) DEFAULT NULL COMMENT '性别',
  `head_img_url` varchar(500) DEFAULT NULL COMMENT '头像',
  `country` varchar(100) DEFAULT NULL COMMENT '国家',
  `province` varchar(100) DEFAULT NULL COMMENT '省份',
  `city` varchar(100) DEFAULT NULL COMMENT '城市',
  `language` varchar(50) DEFAULT NULL COMMENT '语言',
  `subscribe_status` varchar(20) DEFAULT 'SUBSCRIBED' COMMENT '关注状态',
  `subscribe_time` datetime DEFAULT NULL COMMENT '关注时间',
  `bind_user_id` varchar(64) DEFAULT NULL COMMENT '绑定的系统用户ID',
  `bind_time` datetime DEFAULT NULL COMMENT '绑定时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `delete_flag` varchar(20) DEFAULT 'NOT_DELETE' COMMENT '删除标志',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_user` varchar(64) DEFAULT NULL COMMENT '创建人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_user` varchar(64) DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_open_id` (`open_id`),
  KEY `idx_bind_user_id` (`bind_user_id`),
  KEY `idx_subscribe_status` (`subscribe_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='微信用户表';

-- 微信消息记录表
CREATE TABLE `wx_message` (
  `id` varchar(64) NOT NULL COMMENT '主键ID',
  `msg_id` varchar(64) DEFAULT NULL COMMENT '微信消息ID',
  `from_user` varchar(128) NOT NULL COMMENT '发送者openId',
  `to_user` varchar(128) NOT NULL COMMENT '接收者openId',
  `msg_type` varchar(50) NOT NULL COMMENT '消息类型',
  `content` text COMMENT '消息内容',
  `media_id` varchar(255) DEFAULT NULL COMMENT '媒体文件ID',
  `pic_url` varchar(500) DEFAULT NULL COMMENT '图片链接',
  `format` varchar(50) DEFAULT NULL COMMENT '语音格式',
  `recognition` text DEFAULT NULL COMMENT '语音识别结果',
  `thumb_media_id` varchar(255) DEFAULT NULL COMMENT '视频缩略图媒体ID',
  `location_x` decimal(10,6) DEFAULT NULL COMMENT '地理位置纬度',
  `location_y` decimal(10,6) DEFAULT NULL COMMENT '地理位置经度',
  `scale` int DEFAULT NULL COMMENT '地图缩放大小',
  `label` varchar(255) DEFAULT NULL COMMENT '地理位置信息',
  `title` varchar(255) DEFAULT NULL COMMENT '消息标题',
  `description` text DEFAULT NULL COMMENT '消息描述',
  `url` varchar(500) DEFAULT NULL COMMENT '消息链接',
  `event` varchar(50) DEFAULT NULL COMMENT '事件类型',
  `event_key` varchar(255) DEFAULT NULL COMMENT '事件KEY值',
  `ticket` varchar(255) DEFAULT NULL COMMENT '二维码ticket',
  `latitude` decimal(10,6) DEFAULT NULL COMMENT '纬度',
  `longitude` decimal(10,6) DEFAULT NULL COMMENT '经度',
  `precision` decimal(10,6) DEFAULT NULL COMMENT '位置精度',
  `create_time_wx` bigint DEFAULT NULL COMMENT '微信消息创建时间',
  `delete_flag` varchar(20) DEFAULT 'NOT_DELETE' COMMENT '删除标志',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_user` varchar(64) DEFAULT NULL COMMENT '创建人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_user` varchar(64) DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`id`),
  KEY `idx_from_user` (`from_user`),
  KEY `idx_msg_type` (`msg_type`),
  KEY `idx_create_time_wx` (`create_time_wx`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='微信消息记录表';

-- 微信菜单表
CREATE TABLE `wx_menu` (
  `id` varchar(64) NOT NULL COMMENT '主键ID',
  `parent_id` varchar(64) DEFAULT '0' COMMENT '父级ID',
  `menu_name` varchar(100) NOT NULL COMMENT '菜单名称',
  `menu_type` varchar(20) NOT NULL COMMENT '菜单类型',
  `menu_key` varchar(255) DEFAULT NULL COMMENT '菜单KEY',
  `url` varchar(500) DEFAULT NULL COMMENT '菜单链接',
  `media_id` varchar(255) DEFAULT NULL COMMENT '媒体文件ID',
  `appid` varchar(100) DEFAULT NULL COMMENT '小程序appid',
  `pagepath` varchar(255) DEFAULT NULL COMMENT '小程序页面路径',
  `sort_code` int DEFAULT 100 COMMENT '排序',
  `status` varchar(20) DEFAULT 'ENABLE' COMMENT '状态',
  `delete_flag` varchar(20) DEFAULT 'NOT_DELETE' COMMENT '删除标志',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_user` varchar(64) DEFAULT NULL COMMENT '创建人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_user` varchar(64) DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`id`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_sort_code` (`sort_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='微信菜单表';

-- 微信自动回复规则表
CREATE TABLE `wx_reply_rule` (
  `id` varchar(64) NOT NULL COMMENT '主键ID',
  `rule_name` varchar(100) NOT NULL COMMENT '规则名称',
  `rule_type` varchar(20) NOT NULL COMMENT '规则类型',
  `match_value` varchar(255) NOT NULL COMMENT '匹配值',
  `reply_type` varchar(20) NOT NULL COMMENT '回复类型',
  `reply_content` text COMMENT '回复内容',
  `media_id` varchar(255) DEFAULT NULL COMMENT '媒体文件ID',
  `status` varchar(20) DEFAULT 'ENABLE' COMMENT '状态',
  `sort_code` int DEFAULT 100 COMMENT '排序',
  `delete_flag` varchar(20) DEFAULT 'NOT_DELETE' COMMENT '删除标志',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_user` varchar(64) DEFAULT NULL COMMENT '创建人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_user` varchar(64) DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`id`),
  KEY `idx_rule_type` (`rule_type`),
  KEY `idx_match_value` (`match_value`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='微信自动回复规则表';

-- 插入微信公众号配置项
INSERT INTO `dev_config` (`id`, `config_key`, `config_value`, `category`, `remark`, `sort_code`, `delete_flag`, `create_time`, `create_user`) VALUES
('wechat_config_001', 'WECHAT_MP_APP_ID', '', 'WECHAT_CONFIG', '微信公众号AppId', 100, 'NOT_DELETE', NOW(), 'system'),
('wechat_config_002', 'WECHAT_MP_APP_SECRET', '', 'WECHAT_CONFIG', '微信公众号AppSecret', 101, 'NOT_DELETE', NOW(), 'system'),
('wechat_config_003', 'WECHAT_MP_TOKEN', 'znhb_wechat_token', 'WECHAT_CONFIG', '微信公众号Token', 102, 'NOT_DELETE', NOW(), 'system'),
('wechat_config_004', 'WECHAT_MP_ENCODING_AES_KEY', '', 'WECHAT_CONFIG', '微信公众号EncodingAESKey', 103, 'NOT_DELETE', NOW(), 'system');

-- 插入默认菜单
INSERT INTO `wx_menu` (`id`, `parent_id`, `menu_name`, `menu_type`, `menu_key`, `sort_code`, `create_time`, `create_user`) VALUES
('menu_001', '0', '帮助中心', 'click', 'MENU_HELP', 1, NOW(), 'system'),
('menu_002', '0', '账号绑定', 'click', 'MENU_BIND', 2, NOW(), 'system'),
('menu_003', '0', '通行状态', 'click', 'MENU_STATUS', 3, NOW(), 'system');

-- 插入默认自动回复规则
INSERT INTO `wx_reply_rule` (`id`, `rule_name`, `rule_type`, `match_value`, `reply_type`, `reply_content`, `create_time`, `create_user`) VALUES
('rule_001', '关注回复', 'subscribe', 'subscribe', 'text', '🎉 欢迎关注清洁运输门禁管理平台！\n\n我们为您提供：\n• 车辆通行记录查询\n• 实时通行状态\n• 系统通知推送\n• 便民服务功能\n\n发送"帮助"获取更多功能介绍。', NOW(), 'system'),
('rule_002', '帮助回复', 'keyword', '帮助', 'text', '欢迎使用清洁运输门禁管理平台！\n\n您可以发送以下关键词获取帮助：\n• 帮助 - 查看帮助信息\n• 绑定 - 绑定系统账号\n• 状态 - 查看绑定状态\n• 通知 - 查看最新通知', NOW(), 'system'),
('rule_003', '默认回复', 'default', 'default', 'text', '感谢您的消息！\n发送"帮助"获取更多信息。', NOW(), 'system');
