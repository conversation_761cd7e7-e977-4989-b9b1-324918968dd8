<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.znhb.wechat.modular.user.mapper.WechatUserMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.znhb.wechat.modular.user.entity.WechatUser">
        <id column="id" property="id" />
        <result column="open_id" property="openId" />
        <result column="union_id" property="unionId" />
        <result column="nickname" property="nickname" />
        <result column="sex" property="sex" />
        <result column="head_img_url" property="headImgUrl" />
        <result column="country" property="country" />
        <result column="province" property="province" />
        <result column="city" property="city" />
        <result column="language" property="language" />
        <result column="subscribe_status" property="subscribeStatus" />
        <result column="subscribe_time" property="subscribeTime" />
        <result column="bind_user_id" property="bindUserId" />
        <result column="bind_time" property="bindTime" />
        <result column="remark" property="remark" />
        <result column="delete_flag" property="deleteFlag" />
        <result column="create_time" property="createTime" />
        <result column="create_user" property="createUser" />
        <result column="update_time" property="updateTime" />
        <result column="update_user" property="updateUser" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, open_id, union_id, nickname, sex, head_img_url, country, province, city, language,
        subscribe_status, subscribe_time, bind_user_id, bind_time, remark, delete_flag,
        create_time, create_user, update_time, update_user
    </sql>

</mapper>
