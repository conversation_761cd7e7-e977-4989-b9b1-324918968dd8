import request from '@/utils/request'

// 获取${functionName}分页
export function ${classNameFirstLower}Page(data) {
    return request({
        url: '/${moduleName}/${busName}/page',
        method: 'get',
        data: data
    })
}
// 提交${functionName}表单 add为false时为编辑，默认为新增
export function ${classNameFirstLower}SubmitForm(data, add = true) {
    return request({
        url: '/${moduleName}/${busName}/'+ (add ? 'add' : 'edit'),
        method: 'post',
        data: data
    })
}
// 删除${functionName}
export function ${classNameFirstLower}Delete(data) {
    return request({
        url: '/${moduleName}/${busName}/delete',
        method: 'post',
        data: data
    })
}
//  获取${functionName}详情
export function ${classNameFirstLower}Detail(data) {
    return request({
        url: '/${moduleName}/${busName}/detail',
        method: 'get',
        data: data
    })
}
