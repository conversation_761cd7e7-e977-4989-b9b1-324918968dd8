/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package ${packageName}.${moduleName}.modular.${busName}.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import ${packageName}.${moduleName}.modular.${busName}.entity.${className};
import ${packageName}.${moduleName}.modular.${busName}.param.${className}AddParam;
import ${packageName}.${moduleName}.modular.${busName}.param.${className}EditParam;
import ${packageName}.${moduleName}.modular.${busName}.param.${className}IdParam;
import ${packageName}.${moduleName}.modular.${busName}.param.${className}PageParam;

import java.util.List;

/**
 * ${functionName}Service接口
 *
 * <AUTHOR>
 * @date ${genTime}
 **/
public interface ${className}Service extends IService<${className}> {

    /**
     * 获取${functionName}分页
     *
     * <AUTHOR>
     * @date ${genTime}
     */
    Page<${className}> page(${className}PageParam ${classNameFirstLower}PageParam);

    /**
     * 添加${functionName}
     *
     * <AUTHOR>
     * @date ${genTime}
     */
    void add(${className}AddParam ${classNameFirstLower}AddParam);

    /**
     * 编辑${functionName}
     *
     * <AUTHOR>
     * @date ${genTime}
     */
    void edit(${className}EditParam ${classNameFirstLower}EditParam);

    /**
     * 删除${functionName}
     *
     * <AUTHOR>
     * @date ${genTime}
     */
    void delete(List<${className}IdParam> ${classNameFirstLower}IdParamList);

    /**
     * 获取${functionName}详情
     *
     * <AUTHOR>
     * @date ${genTime}
     */
    ${className} detail(${className}IdParam ${classNameFirstLower}IdParam);

    /**
     * 获取${functionName}详情
     *
     * <AUTHOR>
     * @date ${genTime}
     **/
    ${className} queryEntity(String id);
}
