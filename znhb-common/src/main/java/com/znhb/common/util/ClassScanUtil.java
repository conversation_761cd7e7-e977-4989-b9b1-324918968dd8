package com.znhb.common.util;

import cn.hutool.core.util.ClassUtil;
import cn.hutool.core.util.ReflectUtil;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.ClassPathScanningCandidateComponentProvider;
import org.springframework.core.type.filter.AssignableTypeFilter;
import org.springframework.core.type.filter.TypeFilter;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * 类扫描工具类
 */
public class ClassScanUtil {

    /**
     * 扫描指定类的所有子类
     * @param parentClass 父类
     * @param basePackage 基础包路径
     * @return 子类的全限定名列表
     */
    public static List<String> scanSubClasses(Class<?> parentClass, String basePackage) {
        List<String> classNames = new ArrayList<>();
        
        // 创建扫描器
        ClassPathScanningCandidateComponentProvider scanner = new ClassPathScanningCandidateComponentProvider(false);
        
        // 添加类型过滤器
        TypeFilter typeFilter = new AssignableTypeFilter(parentClass);
        scanner.addIncludeFilter(typeFilter);
        
        // 扫描指定包下的所有类
        Set<BeanDefinition> components = scanner.findCandidateComponents(basePackage);

        // 获取所有子类的全限定名（排除父类本身）
        String parentClassName = parentClass.getName();
        for (BeanDefinition component : components) {
            String className = component.getBeanClassName();
            if (!parentClassName.equals(className)) {
                classNames.add(className);
            }
        }
        
        return classNames;
    }
} 