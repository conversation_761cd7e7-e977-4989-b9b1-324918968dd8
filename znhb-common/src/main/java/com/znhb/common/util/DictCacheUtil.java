package com.znhb.common.util;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.znhb.common.cache.CommonCacheOperator;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * 字典缓存工具，直接管理Redis中的字典数据缓存
 */
@Component
public class DictCacheUtil {

    /**
     * 自定义字典缓存Key前缀，与easy-trans区分开
     */
    public static final String DICT_CACHE_KEY_PREFIX = "dict-all:";

    @Resource
    private CommonCacheOperator commonCacheOperator;

    /**
     * 缓存整个字典类型的数据
     *
     * @param dictType 字典类型
     * @param dictMap  字典数据Map，key为dictType_dictValue格式，value为字典标签
     */
    public void cacheDictType(String dictType, Map<String, String> dictMap) {
        if (StrUtil.isBlank(dictType) || CollectionUtil.isEmpty(dictMap)) {
            return;
        }
        String cacheKey = DICT_CACHE_KEY_PREFIX + dictType;
        commonCacheOperator.put(cacheKey, dictMap);
    }

    /**
     * 获取字典标签
     *
     * @param dictType  字典类型
     * @param dictValue 字典值
     * @return 字典标签
     */
    @SuppressWarnings("unchecked")
    public String getDictLabel(String dictType, String dictValue) {
        if (StrUtil.isBlank(dictType) || StrUtil.isBlank(dictValue)) {
            return null;
        }
        
        String cacheKey = DICT_CACHE_KEY_PREFIX + dictType;
        Object dictMapObj = commonCacheOperator.get(cacheKey);
        
        if (dictMapObj instanceof Map) {
            Map<String, String> dictMap = (Map<String, String>) dictMapObj;
            // 使用新的dictType_dictValue格式查找
            String newKey = dictType + "_" + dictValue;
            return dictMap.get(newKey);
        }
        
        return null;
    }

    /**
     * 获取指定类型的所有字典数据 (转换为普通dictValue->dictLabel格式)
     *
     * @param dictType 字典类型
     * @return 字典数据Map，key为dictValue，value为dictLabel
     */
    @SuppressWarnings("unchecked")
    public Map<String, String> getDictMapByType(String dictType) {
        if (StrUtil.isBlank(dictType)) {
            return new HashMap<>();
        }
        
        String cacheKey = DICT_CACHE_KEY_PREFIX + dictType;
        Object dictMapObj = commonCacheOperator.get(cacheKey);
        
        if (dictMapObj instanceof Map) {
            Map<String, String> dictMap = (Map<String, String>) dictMapObj;
            // 从dictType_dictValue格式转换为普通的dictValue->dictLabel格式
            Map<String, String> result = new HashMap<>();
            String prefix = dictType + "_";
            
            dictMap.forEach((key, value) -> {
                if (key.startsWith(prefix)) {
                    String dictValue = key.substring(prefix.length());
                    result.put(dictValue, value);
                }
            });
            
            return result;
        }
        
        return new HashMap<>();
    }
    
    /**
     * 获取原始格式的字典数据（不进行格式转换）
     *
     * @param dictType 字典类型
     * @return 字典数据Map，key为dictType_dictValue格式，value为dictLabel
     */
    @SuppressWarnings("unchecked")
    public Map<String, String> getRawDictMapByType(String dictType) {
        if (StrUtil.isBlank(dictType)) {
            return new HashMap<>();
        }
        
        String cacheKey = DICT_CACHE_KEY_PREFIX + dictType;
        Object dictMapObj = commonCacheOperator.get(cacheKey);
        
        if (dictMapObj instanceof Map) {
            return (Map<String, String>) dictMapObj;
        }
        
        return new HashMap<>();
    }

    /**
     * 删除指定类型的字典缓存
     *
     * @param dictType 字典类型
     */
    public void removeDictCache(String dictType) {
        if (StrUtil.isBlank(dictType)) {
            return;
        }
        
        String cacheKey = DICT_CACHE_KEY_PREFIX + dictType;
        commonCacheOperator.remove(cacheKey);
    }

    /**
     * 删除所有字典缓存
     */
    public void removeAllDictCache() {
        commonCacheOperator.removeBatch(DICT_CACHE_KEY_PREFIX + "*");
    }

    /**
     * 设置字典缓存过期时间
     *
     * @param dictType 字典类型
     * @param timeout  过期时间（秒）
     */
    public void setExpire(String dictType, long timeout) {
        if (StrUtil.isBlank(dictType)) {
            return;
        }
        
        String cacheKey = DICT_CACHE_KEY_PREFIX + dictType;
        // 由于CommonCacheOperator不直接提供设置过期时间的方法，我们需要重新存储并设置过期时间
        Object dictMapObj = commonCacheOperator.get(cacheKey);
        if (dictMapObj != null) {
            commonCacheOperator.put(cacheKey, dictMapObj, timeout);
        }
    }
} 