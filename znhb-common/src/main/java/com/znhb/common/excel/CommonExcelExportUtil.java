package com.znhb.common.excel;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.write.builder.ExcelWriterBuilder;
import com.alibaba.excel.write.handler.CellWriteHandler;
import com.alibaba.excel.write.handler.context.CellWriteHandlerContext;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.alibaba.excel.write.style.row.AbstractRowHeightStyleStrategy;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Row;
import com.znhb.common.util.CommonDownloadUtil;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.util.List;
import java.util.function.Consumer;

/**
 * 通用Excel导出工具类
 *
 * <AUTHOR>
 * @date 2025/xx/xx
 **/
@Slf4j
public class CommonExcelExportUtil {

    /**
     * 导出Excel文件
     *
     * @param dataList      数据列表
     * @param clazz         导出对象Class
     * @param sheetName     Sheet名称
     * @param fileName      文件名前缀
     * @param response      HTTP响应对象
     * @param <T>           数据类型
     */
    public static <T> void exportExcel(List<T> dataList, Class<T> clazz, String sheetName, String fileName, HttpServletResponse response) {
        exportExcel(dataList, clazz, sheetName, fileName, response, null);
    }

    /**
     * 导出Excel文件，支持自定义处理
     *
     * @param dataList       数据列表
     * @param clazz          导出对象Class
     * @param sheetName      Sheet名称
     * @param fileName       文件名前缀
     * @param response       HTTP响应对象
     * @param customHandler  自定义处理函数，可为null
     * @param <T>            数据类型
     */
    public static <T> void exportExcel(List<T> dataList, Class<T> clazz, String sheetName, 
                                     String fileName, HttpServletResponse response, 
                                     Consumer<ExcelWriterBuilder> customHandler) {
        File tempFile = null;
        try {
            //创建临时文件
            tempFile = FileUtil.file(FileUtil.getTmpDir() + FileUtil.FILE_SEPARATOR + 
                       fileName + "_" + IdWorker.getIdStr() + ".xlsx");
            
            //设置表头样式
            WriteCellStyle headWriteCellStyle = new WriteCellStyle();
            headWriteCellStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
            WriteFont headWriteFont = new WriteFont();
            headWriteFont.setFontHeightInPoints((short)12);
            headWriteFont.setBold(true);
            headWriteCellStyle.setWriteFont(headWriteFont);

            //设置内容样式
            WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
            WriteFont contentWriteFont = new WriteFont();
            contentWriteFont.setFontHeightInPoints((short)11);
            contentWriteCellStyle.setWriteFont(contentWriteFont);

            //设置样式策略
            HorizontalCellStyleStrategy horizontalCellStyleStrategy = 
                new HorizontalCellStyleStrategy(headWriteCellStyle, contentWriteCellStyle);

            //创建EasyExcel写入构建器
            ExcelWriterBuilder writerBuilder = EasyExcel.write(tempFile, clazz)
                    .registerWriteHandler(horizontalCellStyleStrategy)
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                    .registerWriteHandler(new CellWriteHandler() {
                        @Override
                        public void afterCellDispose(CellWriteHandlerContext context) {
                            WriteCellData<?> cellData = context.getFirstCellData();
                            WriteCellStyle writeCellStyle = cellData.getOrCreateStyle();
                            Integer rowIndex = context.getRowIndex();
                            if(rowIndex == 0) {
                                WriteFont headWriteFont = new WriteFont();
                                headWriteFont.setFontName("宋体");
                                headWriteFont.setBold(true);
                                headWriteFont.setFontHeightInPoints((short) 16);
                                writeCellStyle.setWriteFont(headWriteFont);
                            }
                        }
                    })
                    .registerWriteHandler(new AbstractRowHeightStyleStrategy() {
                        @Override
                        protected void setHeadColumnHeight(Row row, int relativeRowIndex) {
                            if(relativeRowIndex == 0) {
                                // 表头第一行
                                row.setHeightInPoints(34);
                            } else {
                                // 表头其他行
                                row.setHeightInPoints(30);
                            }
                        }
                        @Override
                        protected void setContentColumnHeight(Row row, int relativeRowIndex) {
                            // 内容行
                            row.setHeightInPoints(20);
                        }
                    });
            
            // 应用自定义处理（如果有）
            if (customHandler != null) {
                customHandler.accept(writerBuilder);
            }

            // 写入数据
            writerBuilder.sheet(sheetName).doWrite(dataList);

            // 下载文件
            CommonDownloadUtil.download(tempFile, response);
        } catch (Exception e) {
            log.error(">>> Excel导出失败：", e);
        } finally {
            // 删除临时文件
            if(ObjectUtil.isNotEmpty(tempFile)) {
                FileUtil.del(tempFile);
            }
        }
    }
}