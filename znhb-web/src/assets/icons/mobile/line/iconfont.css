@font-face {
  font-family: "snowy"; /* Project id 3791763 */
  src: url('iconfont.ttf?t=1675526220710') format('truetype');
}

.snowy {
  font-family: "snowy" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.export-outlined:before {
  content: "\e792";
}

.experiment-outlined:before {
  content: "\e7c9";
}

.expand-outlined:before {
  content: "\e915";
}

.expand-alt-outlined:before {
  content: "\e7e9";
}

.exception-outlined:before {
  content: "\e7bb";
}

.euro-outlined:before {
  content: "\e78f";
}

.euro-circle-outlined:before {
  content: "\eb62";
}

.environment-outlined:before {
  content: "\e790";
}

.ellipsis-outlined:before {
  content: "\e815";
}

.download-outlined:before {
  content: "\e814";
}

.dollar-outlined:before {
  content: "\e78d";
}

.dollar-circle-outlined:before {
  content: "\eb61";
}

.dislike-outlined:before {
  content: "\e7c8";
}

.disconnect-outlined:before {
  content: "\e7e8";
}

.dingtalk-outlined:before {
  content: "\e881";
}

.desktop-outlined:before {
  content: "\e845";
}

.deployment-unit-outlined:before {
  content: "\e7d2";
}

.delivered-procedure-outlined:before {
  content: "\e911";
}

.delete-column-outlined:before {
  content: "\e901";
}

.delete-row-outlined:before {
  content: "\e902";
}

.database-outlined:before {
  content: "\e7b9";
}

.dashboard-outlined:before {
  content: "\e78b";
}

.customer-service-outlined:before {
  content: "\e7ca";
}

.crown-outlined:before {
  content: "\e844";
}

.credit-card-outlined:before {
  content: "\e7e5";
}

.copyright-outlined:before {
  content: "\e789";
}

.copyright-circle-outlined:before {
  content: "\eb60";
}

.control-outlined:before {
  content: "\e79c";
}

.container-outlined:before {
  content: "\e7b8";
}

.contacts-outlined:before {
  content: "\e7e4";
}

.console-sql-outlined:before {
  content: "\e910";
}

.compress-outlined:before {
  content: "\e914";
}

.compass-outlined:before {
  content: "\e786";
}

.comment-outlined:before {
  content: "\e8ea";
}

.coffee-outlined:before {
  content: "\e6b5";
}

.code-outlined:before {
  content: "\e79b";
}

.cloud-server-outlined:before {
  content: "\e7db";
}

.cloud-upload-outlined:before {
  content: "\e7dc";
}

.cloud-outlined:before {
  content: "\e7dd";
}

.cloud-download-outlined:before {
  content: "\e7de";
}

.cloud-sync-outlined:before {
  content: "\e7e0";
}

.clear-outlined:before {
  content: "\e900";
}

.ci-circle-outlined:before {
  content: "\e77f";
}

.carry-out-outlined:before {
  content: "\e7d6";
}

.car-outlined:before {
  content: "\e7da";
}

.ci-outlined:before {
  content: "\eb5f";
}

.camera-outlined:before {
  content: "\e7d9";
}

.calendar-outlined:before {
  content: "\e7d4";
}

.calculator-outlined:before {
  content: "\e79a";
}

.bulb-outlined:before {
  content: "\e7c7";
}

.build-outlined:before {
  content: "\e7d5";
}

.bug-outlined:before {
  content: "\e8e9";
}

.branches-outlined:before {
  content: "\e7e7";
}

.borderless-table-outlined:before {
  content: "\e813";
}

.border-outlined:before {
  content: "\e7b7";
}

.book-outlined:before {
  content: "\e7b6";
}

.block-outlined:before {
  content: "\e7df";
}

.bell-outlined:before {
  content: "\e7c5";
}

.bars-outlined:before {
  content: "\e71a";
}

.barcode-outlined:before {
  content: "\e7d8";
}

.bank-outlined:before {
  content: "\e7c6";
}

.audit-outlined:before {
  content: "\e7c0";
}

.audio-outlined:before {
  content: "\e89b";
}

.audio-muted-outlined:before {
  content: "\e8e8";
}

.api-outlined:before {
  content: "\e7e3";
}

.apartment-outlined:before {
  content: "\e89a";
}

.alert-outlined:before {
  content: "\e7c4";
}

.aim-outlined:before {
  content: "\e913";
}

.account-book-outlined:before {
  content: "\e7d3";
}

.column-height-outlined:before {
  content: "\e811";
}

.column-width-outlined:before {
  content: "\e812";
}

.radius-setting-outlined:before {
  content: "\e7b5";
}

.unordered-list-outlined:before {
  content: "\e80f";
}

.ordered-list-outlined:before {
  content: "\e810";
}

.drag-outlined:before {
  content: "\e843";
}

.sort-descending-outlined:before {
  content: "\e80d";
}

.sort-ascending-outlined:before {
  content: "\e80e";
}

.font-colors-outlined:before {
  content: "\e808";
}

.font-size-outlined:before {
  content: "\e809";
}

.line-height-outlined:before {
  content: "\e80a";
}

.dash-outlined:before {
  content: "\e80b";
}

.small-dash-outlined:before {
  content: "\e80c";
}

.zoom-out-outlined:before {
  content: "\e898";
}

.zoom-in-outlined:before {
  content: "\e899";
}

.undo-outlined:before {
  content: "\e787";
}

.redo-outlined:before {
  content: "\e788";
}

.bold-outlined:before {
  content: "\e804";
}

.strikethrough-outlined:before {
  content: "\e805";
}

.underline-outlined:before {
  content: "\e806";
}

.italic-outlined:before {
  content: "\e807";
}

.bg-colors-outlined:before {
  content: "\e803";
}

.align-right-outlined:before {
  content: "\e7fb";
}

.align-left-outlined:before {
  content: "\e802";
}

.align-center-outlined:before {
  content: "\e7f5";
}

.highlight-outlined:before {
  content: "\e7e2";
}

.diff-outlined:before {
  content: "\e7bf";
}

.snippets-outlined:before {
  content: "\e7bd";
}

.delete-outlined:before {
  content: "\e7c3";
}

.scissor-outlined:before {
  content: "\e7e6";
}

.copy-outlined:before {
  content: "\e7bc";
}

.form-outlined:before {
  content: "\e791";
}

.edit-outlined:before {
  content: "\e7e1";
}

.stop-outlined:before {
  content: "\e842";
}

.issues-close-outlined:before {
  content: "\e68e";
}

.warning-outlined:before {
  content: "\e682";
}

.clock-circle-outlined:before {
  content: "\e784";
}

.check-circle-outlined:before {
  content: "\e77d";
}

.check-square-outlined:before {
  content: "\e794";
}

.check-outlined:before {
  content: "\e7fc";
}

.exclamation-circle-outlined:before {
  content: "\e785";
}

.exclamation-outlined:before {
  content: "\e7fa";
}

.info-circle-outlined:before {
  content: "\e77e";
}

.info-outlined:before {
  content: "\e7f9";
}

.minus-square-outlined:before {
  content: "\e796";
}

.plus-square-outlined:before {
  content: "\e797";
}

.minus-circle-outlined:before {
  content: "\e780";
}

.minus-outlined:before {
  content: "\e801";
}

.pause-circle-outlined:before {
  content: "\e783";
}

.pause-outlined:before {
  content: "\e800";
}

.plus-circle-outlined:before {
  content: "\e781";
}

.plus-outlined:before {
  content: "\e8fe";
}

.question-circle-outlined:before {
  content: "\e782";
}

.question-outlined:before {
  content: "\e7ff";
}

.fullscreen-outlined:before {
  content: "\e7ec";
}

.fullscreen-exit-outlined:before {
  content: "\e7ed";
}

.radius-bottomleft-outlined:before {
  content: "\e7b1";
}

.radius-bottomright-outlined:before {
  content: "\e7b2";
}

.radius-upleft-outlined:before {
  content: "\e7b3";
}

.radius-upright-outlined:before {
  content: "\e7b4";
}

.pic-center-outlined:before {
  content: "\e7f6";
}

.pic-right-outlined:before {
  content: "\e7f7";
}

.pic-left-outlined:before {
  content: "\e7f8";
}

.border-outer-outlined:before {
  content: "\e7a9";
}

.border-top-outlined:before {
  content: "\e7aa";
}

.border-bottom-outlined:before {
  content: "\e7ab";
}

.border-left-outlined:before {
  content: "\e7ac";
}

.border-right-outlined:before {
  content: "\e7ad";
}

.border-inner-outlined:before {
  content: "\e7ae";
}

.border-verticle-outlined:before {
  content: "\e7af";
}

.border-horizontal-outlined:before {
  content: "\e7b0";
}

.menu-unfold-outlined:before {
  content: "\e7f3";
}

.menu-fold-outlined:before {
  content: "\e7f4";
}

.logout-outlined:before {
  content: "\e78c";
}

.login-outlined:before {
  content: "\e8f4";
}

.cluster-outlined:before {
  content: "\e7d7";
}

.down-square-outlined:before {
  content: "\e793";
}

.left-square-outlined:before {
  content: "\e795";
}

.right-square-outlined:before {
  content: "\e798";
}

.up-Square-outlined:before {
  content: "\e799";
}

.play-circle-outlined:before {
  content: "\e67a";
}

.arrow-down-outlined:before {
  content: "\e66d";
}

.arrow-right-outlined:before {
  content: "\e66e";
}

.arrow-up-outlined:before {
  content: "\e66f";
}

.arrow-left-outlined:before {
  content: "\e670";
}

.swap-outlined:before {
  content: "\e7f2";
}

.swap-right-outlined:before {
  content: "\e8f2";
}

.swap-left-outlined:before {
  content: "\e8f3";
}

.enter-outlined:before {
  content: "\e7fd";
}

.rollback-outlined:before {
  content: "\e7fe";
}

.retweet-outlined:before {
  content: "\e8f1";
}

.fast-backward-outlined:before {
  content: "\e8ed";
}

.fast-forward-outlined:before {
  content: "\e8ee";
}

.vertical-align-bottom-outlined:before {
  content: "\e7ef";
}

.vertical-align-middle-outlined:before {
  content: "\e7f0";
}

.vertical-align-top-outlined:before {
  content: "\e7f1";
}

.vertical-right-outlined:before {
  content: "\e7ea";
}

.vertical-left-outlined:before {
  content: "\e7eb";
}

.double-left-outlined:before {
  content: "\e66b";
}

.double-right-outlined:before {
  content: "\e66c";
}

.up-circle-outlined:before {
  content: "\e666";
}

.right-circle-outlined:before {
  content: "\e667";
}

.left-circle-outlined:before {
  content: "\e66a";
}

.down-circle-outlined:before {
  content: "\eb5e";
}

.caret-up-outlined:before {
  content: "\e689";
}

.caret-down-outlined:before {
  content: "\e68a";
}

.caret-left-outlined:before {
  content: "\e68b";
}

.caret-right-outlined:before {
  content: "\e68c";
}

.left-outlined:before {
  content: "\e685";
}

.up-outlined:before {
  content: "\e686";
}

.down-outlined:before {
  content: "\e687";
}

.right-outlined:before {
  content: "\e688";
}

.arrows-alt-outlined:before {
  content: "\e665";
}

.shrink-outlined:before {
  content: "\e68d";
}

.step-backward-outlined:before {
  content: "\e8ef";
}

.step-forward-outlined:before {
  content: "\e8f0";
}

.robot-outlined:before {
  content: "\e897";
}

.file-word-outlined:before {
  content: "\e7ba";
}

.usergroup-delete-outlined:before {
  content: "\e760";
}

.field-time-outlined:before {
  content: "\eb5d";
}

.setting-outlined:before {
  content: "\e78e";
}

.file-search-outlined:before {
  content: "\e730";
}

.team-outlined:before {
  content: "\e67d";
}

.message-outlined:before {
  content: "\e78a";
}

.mail-outlined:before {
  content: "\e62e";
}

.send-outlined:before {
  content: "\e622";
}

.appstore-add-outlined:before {
  content: "\e8eb";
}

.user-outlined:before {
  content: "\e641";
}

.project-outlined:before {
  content: "\e746";
}

.hdd-outlined:before {
  content: "\e734";
}

.tool-outlined:before {
  content: "\e75b";
}

.user-switch-outlined:before {
  content: "\ea3d";
}

.appstore-outlined:before {
  content: "\e601";
}

.home-outlined:before {
  content: "\e965";
}

