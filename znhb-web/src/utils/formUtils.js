import { nextTick } from 'vue'
import { message } from 'ant-design-vue'

/**
 * 滚动到表单的第一个错误字段
 * @param {Object} errors - 表单验证错误信息对象
 * @param {Object} options - 配置选项
 * @param {Boolean} options.smooth - 是否使用平滑滚动，默认true
 * @param {Boolean} options.highlight - 是否高亮显示错误字段，默认true
 * @param {Boolean} options.showMessage - 是否显示错误消息，默认true
 * @param {String} options.messagePrefix - 错误消息前缀，默认"请完善表单信息: "
 */
export function scrollToFirstError(errors, options = {}) {
  // 设置默认选项
  const defaultOptions = {
    smooth: true,
    highlight: true,
    showMessage: true,
    messagePrefix: '请完善表单信息: '
  }
  
  // 合并用户选项
  const mergedOptions = { ...defaultOptions, ...options }
  
  // 防止没有错误时的调用
  if (!errors || !Object.keys(errors).length) return
  
  // 获取第一个错误字段名
  const firstErrorField = Object.keys(errors)[0]
  
  // 使用 nextTick 确保 DOM 更新后再滚动
  nextTick(() => {
    // 查找错误字段对应的表单项
    const errorElement = document.querySelector(`.ant-form-item-has-error[name="${firstErrorField}"], .ant-form-item-has-error [name="${firstErrorField}"]`)
    
    if (errorElement) {
      // 找到错误元素，滚动到可视区域
      errorElement.scrollIntoView({
        behavior: mergedOptions.smooth ? 'smooth' : 'auto',
        block: 'center'
      })
      
      // 添加闪烁效果，突出显示错误字段
      if (mergedOptions.highlight) {
        highlightElement(errorElement)
      }
    } else {
      // 备选方案：如果找不到具体字段，尝试找到有错误类的表单项
      const firstErrorItem = document.querySelector('.ant-form-item-has-error')
      if (firstErrorItem) {
        firstErrorItem.scrollIntoView({
          behavior: mergedOptions.smooth ? 'smooth' : 'auto',
          block: 'center'
        })
        
        if (mergedOptions.highlight) {
          highlightElement(firstErrorItem)
        }
      }
    }
    
    // 显示提示消息
    if (mergedOptions.showMessage) {
      message.error(`${mergedOptions.messagePrefix}${errors[firstErrorField]?.message || '有必填项未填'}`)
    }
  })
}

/**
 * 高亮显示元素
 * @param {HTMLElement} element - 需要高亮的DOM元素
 * @param {Number} duration - 高亮持续时间(毫秒)，默认2000
 */
function highlightElement(element, duration = 2000) {
  // 检查是否已经添加了全局样式
  if (!document.getElementById('form-error-highlight-style')) {
    // 动态添加高亮动画样式
    const style = document.createElement('style')
    style.id = 'form-error-highlight-style'
    style.innerHTML = `
      @keyframes errorHighlight {
        0%, 100% { box-shadow: 0 0 0 rgba(255, 77, 79, 0); }
        50% { box-shadow: 0 0 8px rgba(255, 77, 79, 0.8); }
      }
      .form-error-highlight {
        animation: errorHighlight 1s ease-in-out 3;
      }
    `
    document.head.appendChild(style)
  }
  
  // 添加一个临时类，用于闪烁效果
  element.classList.add('form-error-highlight')
  
  // 指定时间后移除高亮效果
  setTimeout(() => {
    element.classList.remove('form-error-highlight')
  }, duration)
}

/**
 * 表单验证辅助方法，用于在验证失败时自动滚动到错误字段
 * @param {Object} formRef - 表单ref对象
 * @param {Function} onSuccess - 验证成功时的回调函数
 * @param {Object} options - scrollToFirstError的配置选项
 * @returns {Promise} 返回验证promise
 */
export function validateForm(formRef, onSuccess, options) {
  return formRef.validate()
    .then(onSuccess)
    .catch(errors => {
      scrollToFirstError(errors, options)
      return Promise.reject(errors) // 继续抛出错误，便于外部处理
    })
} 