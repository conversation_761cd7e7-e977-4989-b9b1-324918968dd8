import { message } from 'ant-design-vue'
import vehicleApi from '@/api/biz/vehicleApi'
import fileApi from '@/api/dev/fileApi'

/**
 * 行驶证上传与识别工具类
 */
const vehicleLicenseUtils = {
  /**
   * 文件上传前校验 
   * @param {File} file 文件对象
   * @returns {Boolean} 是否通过校验
   */
  beforeUpload(file) {
    const isImage = file.type.startsWith('image/')
    if (!isImage) {
      message.error('请上传图片文件!')
      return false
    }
    
    const isLt5M = file.size / 1024 / 1024 < 5
    if (!isLt5M) {
      message.error('图片大小不能超过5MB!')
      return false
    }
    
    return true
  },
  
  /**
   * 上传图片并返回URL
   * @param {File} file 文件对象
   * @param {String} messageKey 消息提示的key
   * @param {String} fileType 文件类型描述(用于消息提示)
   * @returns {Promise<String>} 返回上传后的URL
   */
  async uploadImage(file, messageKey, fileType = '图片') {
    if (!this.beforeUpload(file)) {
      return Promise.reject(new Error('文件校验未通过'))
    }
    
    // 创建FormData对象
    const formData = new FormData()
    formData.append('file', file)
    
    // 显示上传中提示
    message.loading({ content: `正在上传${fileType}...`, key: messageKey })
    
    try {
      // 使用fileApi上传文件
      const url = await fileApi.fileUploadLocalReturnUrl(formData)
      
      message.success({ content: `${fileType}上传成功`, key: messageKey })
      return url
    } catch (error) {
      console.error(`${fileType}上传失败:`, error)
      return Promise.reject(error)
    }
  },
  
  /**
   * 将文件转换为Base64
   * @param {File} file 文件对象
   * @returns {Promise<String>} Base64字符串
   */
  getBase64(file) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.readAsDataURL(file)
      reader.onload = () => {
        // 移除base64的前缀部分 (如 "data:image/jpeg;base64,")
        const base64String = reader.result.split(',')[1]
        resolve(base64String)
      }
      reader.onerror = (error) => reject(error)
    })
  },
  
  /**
   * 识别行驶证
   * @param {Object} params 识别参数
   * @param {String} params.mode 识别模式 'single'|'double'
   * @param {File} params.frontFile 正面图片文件(单页模式)
   * @param {File} params.backFile 背面图片文件(单页模式)
   * @param {File} params.doubleFile 双页图片文件(双页模式)
   * @param {String} params.frontUrl 正面图片URL(单页模式)
   * @param {String} params.backUrl 背面图片URL(单页模式)
   * @param {String} params.doubleUrl 双页图片URL(双页模式)
   * @returns {Promise<Object>} 识别结果
   */
  async recognizeLicense(params) {
    const { mode } = params
    let response
    let uploadedImages = {}
    
    try {
      if (mode === 'single') {
        if (!params.frontFile || !params.backFile) {
          throw new Error('缺少行驶证首页或副页图片，请先上传')
        }
        
        // 获取Base64用于OCR识别
        const frontImageBase64 = await this.getBase64(params.frontFile)
        const backImageBase64 = await this.getBase64(params.backFile)
        
        // 保存图片URL
        uploadedImages = {
          uploadMode: 'single',
          drivingLicenseFrontImgUrl: params.frontUrl,
          drivingLicenseBackImgUrl: params.backUrl
        }
        
        // 调用单页OCR识别接口
        response = await vehicleApi.vehicleScanSingleLicense(frontImageBase64, backImageBase64)
      } else {
        if (!params.doubleFile) {
          throw new Error('缺少行驶证图片，请先上传')
        }
        
        // 获取Base64用于OCR识别
        const imageBase64 = await this.getBase64(params.doubleFile)
        
        // 保存图片URL
        uploadedImages = {
          uploadMode: 'double',
          drivingLicenseImgUrl: params.doubleUrl
        }
        
        // 调用双页OCR识别接口
        response = await vehicleApi.vehicleScanDoubleLicense(imageBase64)
      }
      
      // 处理响应结果
      return this.formatRecognitionResult(response, uploadedImages)
    } catch (error) {
      console.error('行驶证识别失败:', error)
      return Promise.reject(error)
    }
  },
  
  /**
   * 格式化识别结果
   * @param {Object} response API返回的识别结果
   * @param {Object} uploadedImages 上传的图片信息
   * @returns {Object} 格式化后的结果
   */
  formatRecognitionResult(response, uploadedImages) {
    let finalData = {}
    
    // 处理双页识别的返回结果，合并front和back
    if (response && response.front && response.back) {
      // 合并首页和副页数据
      const frontData = response.front
      const backData = response.back
      
      finalData = {
        // 前端模式信息
        ...uploadedImages,
        
        // 合并OCR识别出的信息
        plateNumber: frontData.number,
        vehicleType: frontData.vehicle_type,
        name: frontData.name,
        address: frontData.address,
        useCharacter: frontData.use_character,
        model: frontData.model,
        engineNo: frontData.engine_no,
        vin: frontData.vin,
        registerDate: frontData.register_date,
        issueDate: frontData.issue_date,
        issuingAuthority: frontData.issuing_authority,
        
        // 副页信息
        fileNo: backData.file_no,
        approvedPassengers: backData.approved_passengers,
        grossMass: backData.gross_mass,
        unladenMass: backData.unladen_mass,
        approvedLoad: backData.approved_load,
        dimension: backData.dimension,
        tractionMass: backData.traction_mass,
        remarks: backData.remarks,
        inspectionRecord: backData.inspection_record,
        codeNumber: backData.code_number,
        energyType: backData.energy_type
      }
    } else {
      // 如果不是双页结构，合并response和上传图片信息
      finalData = {
        ...uploadedImages,
        plateNumber: response.number,
        vehicleType: response.vehicle_type,
        name: response.name,
        address: response.address,
        useCharacter: response.use_character,
        model: response.model,
        engineNo: response.engine_no,
        vin: response.vin,
        registerDate: response.register_date,
        issueDate: response.issue_date,
        issuingAuthority: response.issuing_authority,
        
        // 副页信息
        fileNo: response.file_no,
        approvedPassengers: response.approved_passengers,
        grossMass: response.gross_mass,
        unladenMass: response.unladen_mass,
        approvedLoad: response.approved_load,
        dimension: response.dimension,
        tractionMass: response.traction_mass,
        remarks: response.remarks,
        inspectionRecord: response.inspection_record,
        codeNumber: response.code_number,
        energyType: response.energy_type
      }
    }
    
    return finalData
  }
}

export default vehicleLicenseUtils 