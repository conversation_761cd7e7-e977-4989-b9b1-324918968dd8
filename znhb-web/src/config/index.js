/**
 *  Copyright [2022] [https://www.xiaonuo.vip]
 *	Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *	1.请不要删除和修改根目录下的LICENSE文件。
 *	2.请不要删除和修改Snowy源码头部的版权声明。
 *	3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 *	4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 *	5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 *	6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */

// 获取动态API基础URL
const getDynamicApiUrl = () => {
	// 先获取环境变量，处理开发环境和生产环境的差异
	const viteEnv = import.meta.env || {}
	
	// 如果是开发环境，直接使用环境变量中的配置
	if (viteEnv.DEV) {
		return viteEnv.VITE_API_BASEURL
	}

	// 在生产环境中，检查是否为内网访问
	const hostname = window.location.hostname
	
	// 更准确的内网IP检测函数
	const isInternalNetwork = (host) => {
		// 如果是域名形式（非IP），默认认为是公网访问
		if (!/^(\d{1,3}\.){3}\d{1,3}$/.test(host)) {
			// 特殊处理localhost
			return host === 'localhost' || host === '127.0.0.1'
		}
		
		// 拆分IP地址
		const ipParts = host.split('.').map(Number)
		
		// 检查是否属于RFC 1918定义的私有IP地址范围
		// 10.0.0.0 - ************** (10/8 prefix)
		if (ipParts[0] === 10) return true
		
		// ********** - ************** (172.16/12 prefix)
		if (ipParts[0] === 172 && ipParts[1] >= 16 && ipParts[1] <= 31) return true
		
		// *********** - *************** (192.168/16 prefix)
		if (ipParts[0] === 192 && ipParts[1] === 168) return true
		
		// ********* - *************** (127/8 prefix) - 本地回环
		if (ipParts[0] === 127) return true
		
		// 其他情况视为公网IP
		return false
	}
	
	// 使用改进的内网检测方法
	if (isInternalNetwork(hostname)) {
		// 内网环境下，我们就使用配置文件中的地址
		// 判断内网API配置是否存在
		if (viteEnv.VITE_INTERNAL_API_BASEURL) {
			// 如果有专门的内网配置，则使用内网配置
			return viteEnv.VITE_INTERNAL_API_BASEURL
		} else {
			// 如果没有内网专属配置，则依然使用常规配置
			return viteEnv.VITE_API_BASEURL
		}
	}
	
	// 公网环境，使用配置的公网API地址
	return viteEnv.VITE_API_BASEURL
}

const DEFAULT_CONFIG = {
	// 首页地址
	DASHBOARD_URL: '/index',

	// 接口地址
	API_URL: getDynamicApiUrl(),

	// 请求超时
	TIMEOUT: 60000,

	// TokenName // Authorization
	TOKEN_NAME: 'token',

	// Token前缀，注意最后有个空格，如不需要需设置空字符串 // Bearer
	TOKEN_PREFIX: '',

	// 追加其他头
	HEADERS: {},

	// 请求是否开启缓存
	REQUEST_CACHE: false,

	// 布局 经典：classical，双排菜单：doublerow
	SNOWY_LAYOUT: 'doublerow',

	// 菜单是否折叠
	SNOWY_MENU_COLLAPSE: false,

	// 模块坞
	SNOWY_MODULE_UNFOLD_OPEN: true,

	// 是否开启多标签
	SNOWY_LAYOUT_TAGS_OPEN: true,

	// 是否开启展示面包屑
	SNOWY_BREADCRUMD_OPEN: false,

	// 顶栏是否应用主题色
	SNOWY_TOP_HEADER_THEME_COLOR_OPEN: false,

	// 顶栏主题色通栏
	SNOWY_TOP_HEADER_THEME_COLOR_SPREAD: false,

	// 侧边菜单是否排他展开
	SNOWY_SIDE_UNIQUE_OPEN: true,

	// 语言
	LANG: 'zh-cn',

	// 主题颜色
	COLOR: '#1890FF',

	// 默认整体主题
	SNOWY_THEME: 'dark',

	// 整体表单风格
	SNOWY_FORM_STYLE: 'drawer',

	// 成功色
	success: '#52c41a',

	// 警告色
	warning: '#faad14',

	// 错误色
	error: '#f5222f',

	// 系统基础配置，这些是数据库中保存起来的
	SYS_BASE_CONFIG: {
		// 默认logo
		SNOWY_SYS_LOGO: '/img/logo.png',
		// 背景图
		SNOWY_SYS_BACK_IMAGE: '',
		// 系统名称
		SNOWY_SYS_NAME: '清洁运输门禁管理平台',
		// 版本
		SNOWY_SYS_VERSION: '2.0',
		// 版权
		SNOWY_SYS_COPYRIGHT: 'Snowy ©2022 Created by xiaonuo.vip',
		// 版权跳转URL
		SNOWY_SYS_COPYRIGHT_URL: 'https://www.xiaonuo.vip',
		// 默认文件存储
		SNOWY_SYS_DEFAULT_FILE_ENGINE: 'LOCAL',
		// 是否开启验证码
		SNOWY_SYS_DEFAULT_CAPTCHA_OPEN: 'false',
		// 默认重置密码
		SNOWY_SYS_DEFAULT_PASSWORD: '123456'
	}
}

export default DEFAULT_CONFIG
