import { createApp } from 'vue'
import Antd from 'ant-design-vue'
import { createPinia } from 'pinia'

import './style/index.less'
import 'vant/lib/index.css'
import snowy from './snowy'
import i18n from './locales'
import router from './router'
import App from './App.vue'
import './tailwind.css'

const app = createApp(App)
app.use(createPinia())
app.use(router)
app.use(Antd)
app.use(i18n)
app.use(snowy)

// 挂载app
app.mount('#app')

app.config.warnHandler = (msg, instance, trace) => {
  // 忽略特定警告
  if (msg.includes('Extraneous non-props attributes (value)')) {
    return;
  }
  console.warn(`[Vue warn]: ${msg}\n${trace}`);
};
