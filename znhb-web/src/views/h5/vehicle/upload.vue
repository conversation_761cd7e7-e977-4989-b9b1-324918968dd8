<template>
  <div class="license-upload-h5">
    <!-- 顶部导航栏 -->
    <div class="nav-header">
      <div class="back-btn" @click="goBack">
        <Icon name="arrow-left" />
      </div>
      <div class="page-title">车辆备案</div>
      <div class="placeholder"></div>
    </div>

    <!-- 主体内容 -->
    <div class="content-container">
      <!-- 上传模式选择 -->
      <div class="info-card">
        <div class="card-header">
          <Icon name="setting-o" />
          <span>上传模式</span>
        </div>
        <div class="card-content">
          <RadioGroup v-model="uploadMode" direction="horizontal">
            <Radio name="single">单页模式</Radio>
            <Radio name="double">双页模式</Radio>
          </RadioGroup>
        </div>
      </div>

      <!-- 单页模式上传(分开上传首页和副页) -->
      <div v-if="uploadMode === 'single'" class="info-card">
        <div class="card-header">
          <Icon name="photograph" />
          <span>行驶证首页</span>
        </div>
        <div class="card-content">
          <Uploader
            v-model="frontImageList"
            :max-count="1"
            :after-read="afterRead"
            :before-read="beforeRead"
            :before-delete="beforeDelete"
            :max-size="5 * 1024 * 1024"
            accept="image/*"
            @delete="handleDelete"
            @upload="handleUpload"
          >
            <template #preview-cover="{ file }">
              <div class="preview-cover">
                <Icon name="delete" @click.stop="handleDelete(file)" />
              </div>
            </template>
          </Uploader>
        </div>

        <div class="card-header">
          <Icon name="photograph" />
          <span>行驶证副页</span>
        </div>
        <div class="card-content">
          <Uploader
            v-model="backImageList"
            :max-count="1"
            :after-read="afterRead"
            :before-read="beforeRead"
            :before-delete="beforeDelete"
            :max-size="5 * 1024 * 1024"
            accept="image/*"
            @delete="handleDelete"
            @upload="handleUpload"
          >
            <template #preview-cover="{ file }">
              <div class="preview-cover">
                <Icon name="delete" @click.stop="handleDelete(file)" />
              </div>
            </template>
          </Uploader>
        </div>
      </div>

      <!-- 双页模式上传(一张包含首页和副页的照片) -->
      <div v-else class="info-card">
        <div class="card-header">
          <Icon name="photograph" />
          <span>行驶证图片(一张包含首页和副页)</span>
        </div>
        <div class="card-content">
          <Uploader
            v-model="doubleImageList"
            :max-count="1"
            :after-read="afterRead"
            :before-read="beforeRead"
            :before-delete="beforeDelete"
            :max-size="5 * 1024 * 1024"
            accept="image/*"
            @delete="handleDelete"
            @upload="handleUpload"
          >
            <template #preview-cover="{ file }">
              <div class="preview-cover">
                <Icon name="delete" @click.stop="handleDelete(file)" />
              </div>
            </template>
          </Uploader>
        </div>
      </div>

      <!-- 随车清单上传 -->
      <div class="info-card">
        <div class="card-header">
          <Icon name="description" />
          <span>随车清单</span>
        </div>
        <div class="card-content">
          <Uploader
            v-model="vehicleListImageList"
            :max-count="1"
            :after-read="afterReadVehicleList"
            :before-read="beforeRead"
            :before-delete="beforeDelete"
            :max-size="5 * 1024 * 1024"
            accept="image/*"
            @delete="handleDeleteVehicleList"
            @upload="handleUpload"
          >
            <template #preview-cover="{ file }">
              <div class="preview-cover">
                <Icon name="delete" @click.stop="handleDeleteVehicleList(file)" />
              </div>
            </template>
          </Uploader>
        </div>
      </div>

      <!-- 车头照片上传 -->
      <div class="info-card">
        <div class="card-header">
          <Icon name="photograph" />
          <span>车头照片</span>
        </div>
        <div class="card-content">
          <Uploader
            v-model="carImageList"
            :max-count="1"
            :after-read="afterReadCarImage"
            :before-read="beforeRead"
            :before-delete="beforeDelete"
            :max-size="5 * 1024 * 1024"
            accept="image/*"
            @delete="handleDeleteCarImage"
            @upload="handleUpload"
          >
            <template #preview-cover="{ file }">
              <div class="preview-cover">
                <Icon name="delete" @click.stop="handleDeleteCarImage(file)" />
              </div>
            </template>
          </Uploader>
        </div>
      </div>

      <!-- 识别按钮 -->
      <div class="action-container">
        <Button
          type="primary"
          block
          :loading="recognizing"
          :disabled="!canRecognize"
          @click="handleRecognize"
        >
          开始识别
        </Button>
      </div>
    </div>



    <Popup v-model:show="showRegisterDatePicker" position="bottom" round>
      <DatePicker
        v-model="registerDateTemp"
        type="date"
        title="选择注册日期"
        :min-date="new Date(1980, 0, 1)"
        :max-date="new Date()"
        @confirm="onRegisterDateConfirm"
        @cancel="showRegisterDatePicker = false"
        show-toolbar
      />
    </Popup>

    <Popup v-model:show="showIssueDatePicker" position="bottom" round>
      <DatePicker
        v-model="issueDateTemp"
        type="date"
        title="选择发证日期"
        :min-date="new Date(1980, 0, 1)"
        :max-date="new Date()"
        @confirm="onIssueDateConfirm"
        @cancel="showIssueDatePicker = false"
        show-toolbar
      />
    </Popup>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, reactive } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import {
  Icon,
  Button,
  RadioGroup,
  Radio,
  Uploader,
  showToast
} from 'vant';
import vehicleLicenseUtils from '@/utils/vehicleLicenseUtils';
import fileApi from '@/api/dev/fileApi';

// 路由
const router = useRouter();
const route = useRoute();
const goBack = () => router.back();

// 上传模式
const uploadMode = ref('single');

// 图片列表
const frontImageList = ref([]);
const backImageList = ref([]);
const doubleImageList = ref([]);
const vehicleListImageList = ref([]); // 随车清单图片列表
const carImageList = ref([]); // 车头照片列表

// 上传后的文件URL
const doubleFileUrl = ref('');
const frontFileUrl = ref('');
const backFileUrl = ref('');
const vehicleListFileUrl = ref(''); // 随车清单文件URL
const carImgUrl = ref(''); // 车头照片文件URL

// 识别数据
const recognizedData = ref({});

// 上传状态对象
const doubleUploadStatus = reactive({
  uploading: false,
  success: false,
  error: false,
  message: ''
});

const frontUploadStatus = reactive({
  uploading: false,
  success: false,
  error: false,
  message: ''
});

const backUploadStatus = reactive({
  uploading: false,
  success: false,
  error: false,
  message: ''
});

const vehicleListUploadStatus = reactive({
  uploading: false,
  success: false,
  error: false,
  message: ''
});

const carImageUploadStatus = reactive({
  uploading: false,
  success: false,
  error: false,
  message: ''
});

// 识别状态
const recognizing = ref(false);

// 表单字段选项
const plateColorOptions = ref([]);
const emissionStageOptions = ref([]);
const netStatusOptions = ref([]);

// 是否可以识别
const canRecognize = computed(() => {
  const hasVehicleList = vehicleListUploadStatus.success;
  const hasCarImage = carImageUploadStatus.success;

  if (uploadMode.value === 'single') {
    return frontUploadStatus.success && backUploadStatus.success && hasVehicleList && hasCarImage;
  } else {
    return doubleUploadStatus.success && hasVehicleList && hasCarImage;
  }
});

// 上传前校验
const beforeRead = (file) => {
  const isImage = file.type.startsWith('image/');
  if (!isImage) {
    showToast('请上传图片文件');
    return false;
  }
  const isLt5M = file.size / 1024 / 1024 < 5;
  if (!isLt5M) {
    showToast('图片大小不能超过 5MB');
    return false;
  }
  return true;
};

// 删除前校验
const beforeDelete = () => {
  return true;
};

// 处理删除
const handleDelete = (file) => {
  if (uploadMode.value === 'double') {
    doubleImageList.value = [];
    doubleUploadStatus.success = false;
    doubleFileUrl.value = '';
  } else {
    // 单页模式，需要判断删除的是首页还是副页图片
    if (file.file && frontImageList.value.length > 0 && file.file.name === frontImageList.value[0]?.file.name) {
      frontImageList.value = [];
      frontUploadStatus.success = false;
      frontFileUrl.value = '';
    } else {
      backImageList.value = [];
      backUploadStatus.success = false;
      backFileUrl.value = '';
    }
  }
};

// 处理上传
const handleUpload = () => {
  // 可以在这里添加上传进度提示等
};

// 上传后处理
const afterRead = async (file) => {
  // 构建FormData
  const formData = new FormData();
  formData.append('file', file.file);

  try {
    // 判断上传类型并设置状态
    let messageKey = '';
    let statusObj = null;
    let fileType = '';

    if (uploadMode.value === 'double') {
      messageKey = 'license_double';
      statusObj = doubleUploadStatus;
      fileType = '行驶证双页图片';
    } else {
      // 需要判断是首页还是副页
      const isFront = frontImageList.value.some(item => item.file.name === file.file.name);
      if (isFront) {
        messageKey = 'license_front';
        statusObj = frontUploadStatus;
        fileType = '行驶证首页';
      } else {
        messageKey = 'license_back';
        statusObj = backUploadStatus;
        fileType = '行驶证副页';
      }
    }

    // 设置状态为上传中
    statusObj.uploading = true;
    statusObj.success = false;
    statusObj.error = false;

    // 显示上传中提示
    showToast({
      type: 'loading',
      message: `正在上传${fileType}...`,
      duration: 0
    });

    // 上传文件到服务器
    const url = await fileApi.fileUploadLocalReturnUrl(formData);

    // 保存URL
    if (uploadMode.value === 'double') {
      doubleFileUrl.value = url;
    } else {
      const isFront = frontImageList.value.some(item => item.file.name === file.file.name);
      if (isFront) {
        frontFileUrl.value = url;
      } else {
        backFileUrl.value = url;
      }
    }

    // 设置状态为成功
    statusObj.uploading = false;
    statusObj.success = true;

    // 显示成功提示
    showToast({
      type: 'success',
      message: `${fileType}上传成功`
    });
  } catch (error) {
    // 判断上传类型并设置状态
    let statusObj = null;
    let fileType = '';

    if (uploadMode.value === 'double') {
      statusObj = doubleUploadStatus;
      fileType = '行驶证双页图片';
    } else {
      // 需要判断是首页还是副页
      const isFront = frontImageList.value.some(item => item.file.name === file.file.name);
      if (isFront) {
        statusObj = frontUploadStatus;
        fileType = '行驶证首页';
      } else {
        statusObj = backUploadStatus;
        fileType = '行驶证副页';
      }
    }

    // 设置状态为错误
    statusObj.uploading = false;
    statusObj.success = false;
    statusObj.error = true;
    statusObj.message = error.message || '上传失败';

    // 显示错误提示
    showToast({
      type: 'fail',
      message: `${fileType}上传失败: ${error.message || '未知错误'}`
    });
  }
};



// 处理识别结果接口
const handleRecognize = async () => {
  // 验证是否满足识别条件
  if (uploadMode.value === 'single' && (!frontUploadStatus.success || !backUploadStatus.success)) {
    showToast('请确保行驶证首页和副页已成功上传');
    return;
  }

  if (uploadMode.value === 'double' && !doubleUploadStatus.success) {
    showToast('请确保行驶证图片已成功上传');
    return;
  }

  if (!vehicleListUploadStatus.success) {
    showToast('请确保随车清单已成功上传');
    return;
  }

  if (!carImageUploadStatus.success) {
    showToast('请确保车头照片已成功上传');
    return;
  }

  recognizing.value = true;
  try {
    // 准备识别参数
    const params = {
      mode: uploadMode.value
    };

    if (uploadMode.value === 'single') {
      params.frontFile = frontImageList.value[0].file;
      params.backFile = backImageList.value[0].file;
      params.frontUrl = frontFileUrl.value;
      params.backUrl = backFileUrl.value;
    } else {
      params.doubleFile = doubleImageList.value[0].file;
      params.doubleUrl = doubleFileUrl.value;
    }

    // 识别行驶证
    const result = await vehicleLicenseUtils.recognizeLicense(params);

    if (result) {
      // 识别成功，显示表单让用户确认
      recognizedData.value = result;

      // 保留图片信息
      recognizedData.value.uploadMode = uploadMode.value;
      recognizedData.value.drivingLicenseFrontImgUrl = frontFileUrl.value;
      recognizedData.value.drivingLicenseBackImgUrl = backFileUrl.value;
      recognizedData.value.drivingLicenseImgUrl = doubleFileUrl.value;
      recognizedData.value.vehicleListImgUrl = vehicleListFileUrl.value; // 添加随车清单图片URL
      recognizedData.value.carImgUrl = carImgUrl.value; // 添加车头照片URL

      // 默认设置为未审核
      recognizedData.value.verifyStatus = '0';

      // 设置文本显示
      if (recognizedData.value.plateColor) {
        const item = plateColorOptions.value.find(item => item.value === recognizedData.value.plateColor);
        recognizedData.value.plateColorText = item ? item.label : '';
      }

      if (recognizedData.value.netStatus) {
        const item = netStatusOptions.value.find(item => item.value === recognizedData.value.netStatus);
        recognizedData.value.netStatusText = item ? item.label : '';
      }

      if (recognizedData.value.emissionStage) {
        const item = emissionStageOptions.value.find(item => item.value === recognizedData.value.emissionStage);
        recognizedData.value.emissionStageText = item ? item.label : '';
      }

      // 处理日期
      if (recognizedData.value.registerDate) {
        recognizedData.value.registerDateText = formatDateDisplay(recognizedData.value.registerDate);
      }

      if (recognizedData.value.issueDate) {
        recognizedData.value.issueDateText = formatDateDisplay(recognizedData.value.issueDate);
      }


      // 获取orgId参数
      const orgId = route.query.orgId;

      // 将orgId添加到识别数据中
      recognizedData.value.orgId = orgId;

      // 将数据序列化并存储到localStorage
      const formDataStr = JSON.stringify(recognizedData.value);
      localStorage.setItem('vehicleFormData', formDataStr);

      showToast({
        type: 'success',
        message: '识别成功，正在跳转...'
      });

      // 导航到表单页面
      router.push({
        path: '/h5/vehicle/form',
        query: { orgId: orgId } // 确保传递orgId参数
      });
    }
  } catch (error) {
    showToast({
      type: 'fail',
      message: '识别失败：' + (error.message || '未知错误')
    });
  } finally {
    recognizing.value = false;
  }
};





// 日期格式化函数
const formatDate = (date) => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
};

// 重置上传数据
const resetUploadData = () => {
  frontImageList.value = [];
  backImageList.value = [];
  doubleImageList.value = [];
  vehicleListImageList.value = []; // 清空随车清单图片列表
  carImageList.value = []; // 清空车头照片列表

  frontFileUrl.value = '';
  backFileUrl.value = '';
  doubleFileUrl.value = '';
  vehicleListFileUrl.value = ''; // 清空随车清单URL
  carImgUrl.value = ''; // 清空车头照片URL

  frontUploadStatus.success = false;
  frontUploadStatus.uploading = false;
  frontUploadStatus.error = false;

  backUploadStatus.success = false;
  backUploadStatus.uploading = false;
  backUploadStatus.error = false;

  doubleUploadStatus.success = false;
  doubleUploadStatus.uploading = false;
  doubleUploadStatus.error = false;

  vehicleListUploadStatus.success = false; // 重置随车清单上传状态
  vehicleListUploadStatus.uploading = false;
  vehicleListUploadStatus.error = false;

  carImageUploadStatus.success = false; // 重置车头照片上传状态
  carImageUploadStatus.uploading = false;
  carImageUploadStatus.error = false;
};

// 页面加载时获取数据
onMounted(() => {
  // 页面初始化逻辑
  // 这里可以添加默认数据加载或配置初始化
});

// 处理日期的展示
const formatDateDisplay = (date) => {
  if (!date) return '';

  // 如果是字符串，转换为日期对象
  let dateObj = date;
  if (typeof date === 'string') {
    // 尝试解析字符串为日期
    const parts = date.split('-');
    if (parts.length === 3) {
      dateObj = new Date(parseInt(parts[0]), parseInt(parts[1]) - 1, parseInt(parts[2]));
    } else {
      return date; // 返回原始字符串
    }
  }

  return formatDate(dateObj);
};



// 处理删除随车清单
const handleDeleteVehicleList = () => {
  vehicleListImageList.value = [];
  vehicleListUploadStatus.success = false;
  vehicleListFileUrl.value = '';
};

// 上传随车清单图片后处理
const afterReadVehicleList = async (file) => {
  // 构建FormData
  const formData = new FormData();
  formData.append('file', file.file);

  try {
    // 设置状态为上传中
    vehicleListUploadStatus.uploading = true;
    vehicleListUploadStatus.success = false;
    vehicleListUploadStatus.error = false;

    // 显示上传中提示
    showToast({
      type: 'loading',
      message: '正在上传随车清单...',
      duration: 0
    });

    // 上传文件到服务器
    const url = await fileApi.fileUploadLocalReturnUrl(formData);

    // 保存URL
    vehicleListFileUrl.value = url;

    // 设置状态为成功
    vehicleListUploadStatus.uploading = false;
    vehicleListUploadStatus.success = true;

    // 显示成功提示
    showToast({
      type: 'success',
      message: '随车清单上传成功'
    });
  } catch (error) {
    // 设置状态为错误
    vehicleListUploadStatus.uploading = false;
    vehicleListUploadStatus.success = false;
    vehicleListUploadStatus.error = true;
    vehicleListUploadStatus.message = error.message || '上传失败';

    // 显示错误提示
    showToast({
      type: 'fail',
      message: `随车清单上传失败: ${error.message || '未知错误'}`
    });
  }
};

// 处理删除车头照片
const handleDeleteCarImage = () => {
  carImageList.value = [];
  carImageUploadStatus.success = false;
  carImgUrl.value = '';
};

// 上传车头照片后处理
const afterReadCarImage = async (file) => {
  // 构建FormData
  const formData = new FormData();
  formData.append('file', file.file);

  try {
    // 设置状态为上传中
    carImageUploadStatus.uploading = true;
    carImageUploadStatus.success = false;
    carImageUploadStatus.error = false;

    // 显示上传中提示
    showToast({
      type: 'loading',
      message: '正在上传车头照片...',
      duration: 0
    });

    // 上传文件到服务器
    const url = await fileApi.fileUploadLocalReturnUrl(formData);

    // 保存URL
    carImgUrl.value = url;

    // 设置状态为成功
    carImageUploadStatus.uploading = false;
    carImageUploadStatus.success = true;

    // 显示成功提示
    showToast({
      type: 'success',
      message: '车头照片上传成功'
    });
  } catch (error) {
    // 设置状态为错误
    carImageUploadStatus.uploading = false;
    carImageUploadStatus.success = false;
    carImageUploadStatus.error = true;
    carImageUploadStatus.message = error.message || '上传失败';

    // 显示错误提示
    showToast({
      type: 'fail',
      message: `车头照片上传失败: ${error.message || '未知错误'}`
    });
  }
};


</script>

<style scoped>
.license-upload-h5 {
  min-height: 100vh;
  background-color: #f5f7fa;
  padding-bottom: 60px;
}

.nav-header {
  position: sticky;
  top: 0;
  z-index: 100;
  height: 46px;
  background-color: #fff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
}

.back-btn {
  font-size: 20px;
  width: 24px;
}

.page-title {
  font-size: 17px;
  font-weight: 500;
}

.placeholder {
  width: 24px;
}

.content-container {
  padding: 12px 12px 0;
}

.info-card {
  background-color: #fff;
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.card-header {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  background-color: #f8f9fa;
  font-size: 15px;
  font-weight: 500;
  color: #333;
  border-bottom: 1px solid #ebedf0;
}

.card-header .van-icon {
  margin-right: 8px;
  color: #1989fa;
}

.card-content {
  padding: 12px 16px;
}

.preview-cover {
  position: absolute;
  top: 0;
  right: 0;
  padding: 4px;
  color: #fff;
  font-size: 20px;
  background: rgba(0, 0, 0, 0.5);
  border-bottom-left-radius: 4px;
}

.action-container {
  padding: 16px;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  box-shadow: 0 -1px 4px rgba(0, 0, 0, 0.08);
}

.form-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;
}

.form-panel {
  background-color: #fff;
  border-radius: 8px;
  overflow: hidden;
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
}

.form-header {
  display: flex;
  align-items: center;
  padding: 16px;
  background-color: #f8f9fa;
  font-size: 17px;
  font-weight: 500;
  flex-shrink: 0;
}

.form-title {
  flex: 1;
}

.close-icon {
  font-size: 20px;
  color: #1989fa;
}

.form-content {
  padding: 16px;
  flex: 1;
  overflow-y: auto;
}

.form-section {
  margin-bottom: 16px;
}

.section-title {
  font-size: 15px;
  font-weight: 500;
  margin-bottom: 8px;
  color: #1989fa;
  padding-left: 8px;
  border-left: 3px solid #1989fa;
}

.picker-value {
  padding: 8px;
  border: 1px solid #ebedf0;
  border-radius: 4px;
  width: 100%;
  text-align: left;
  color: #323233;
}

.form-footer {
  padding: 16px;
  background-color: #f8f9fa;
  flex-shrink: 0;
  display: flex;
  gap: 12px;
}

.form-footer .van-button {
  flex: 1;
}

.img-preview {
  padding: 12px;
  background-color: #fff;
  border-radius: 8px;
  text-align: center;
}

.preview-img {
  max-width: 100%;
  max-height: 200px;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  cursor: pointer;
}
</style>

<style>
/* 全局样式以确保Toast显示在表单上方 */
.van-toast {
  z-index: 3000 !important;
}
</style>
