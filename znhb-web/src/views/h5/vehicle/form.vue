<template>
  <div class="vehicle-form-h5" id="vehicle-form-container">
    <!-- 顶部导航栏 -->
    <div class="nav-header">
      <div class="back-btn" @click="goBack">
        <Icon name="arrow-left" />
      </div>
      <div class="page-title">车辆信息</div>
      <div class="placeholder"></div>
    </div>

    <!-- 主体内容 -->
    <div class="content-container">
      <!-- 车辆基本信息 -->
      <div class="form-section">
        <div class="section-title">车辆基本信息</div>
        <CellGroup inset>
          <Cell title="车牌号码">
            <template #value>
              <Field v-model="formData.plateNumber" placeholder="请输入车牌号码" readonly />
            </template>
          </Cell>
          <Cell title="车牌颜色" :required="true">
            <template #value>
              <Field
                readonly
                clickable
                v-model="selectedPlateColorText"
                placeholder="请选择车牌颜色"
                @click="showPlateColorPicker = true"
              />
            </template>
          </Cell>
          <!-- 车牌颜色选择器 -->
          <Popup v-model:show="showPlateColorPicker" position="bottom" round>
            <Picker
              title="选择车牌颜色"
              :columns="plateColorOptions"
              @confirm="onPlateColorConfirm"
              @cancel="showPlateColorPicker = false"
              :default-index="getDefaultColorIndex()"
            />
          </Popup>
          <Cell title="车辆类型">
            <template #value>
              <Field v-model="formData.vehicleType" placeholder="请输入车辆类型" readonly />
            </template>
          </Cell>
          <Cell title="能源类型">
            <template #value>
              <Field v-model="formData.energyType" placeholder="请输入能源类型" readonly />
            </template>
          </Cell>
          <Cell title="品牌型号">
            <template #value>
              <Field v-model="formData.model" placeholder="请输入品牌型号" readonly />
            </template>
          </Cell>
          <Cell title="发动机号码">
            <template #value>
              <Field v-model="formData.engineNo" placeholder="请输入发动机号码" readonly />
            </template>
          </Cell>
          <Cell title="车辆识别代号">
            <template #value>
              <Field v-model="formData.vin" placeholder="请输入车辆识别代号" readonly />
            </template>
          </Cell>
        </CellGroup>
      </div>

      <!-- 所有人信息 -->
      <div class="form-section">
        <div class="section-title">所有人信息</div>
        <CellGroup inset>
          <Cell title="所有人">
            <template #value>
              <Field v-model="formData.name" placeholder="请输入所有人姓名" readonly />
            </template>
          </Cell>
          <Cell title="使用性质">
            <template #value>
              <Field v-model="formData.useCharacter" placeholder="请输入使用性质" readonly />
            </template>
          </Cell>
          <Cell title="住址">
            <template #value>
              <Field v-model="formData.address" placeholder="请输入住址" readonly />
            </template>
          </Cell>
        </CellGroup>
      </div>



      <!-- 证件信息 -->
      <div class="form-section">
        <div class="section-title">证件信息</div>
        <CellGroup inset>
          <Cell title="注册日期">
            <template #value>
              <Field
                v-model="formData.registerDateText"
                readonly
                placeholder="注册日期"
              />
            </template>
          </Cell>
          <Cell title="发证日期">
            <template #value>
              <Field
                v-model="formData.issueDateText"
                readonly
                placeholder="发证日期"
              />
            </template>
          </Cell>
          <Cell title="排放标准" v-if="formData.emissionStageText">
            <template #value>
              <span>{{ formData.emissionStageText }}</span>
            </template>
          </Cell>
          <Cell title="发证机关">
            <template #value>
              <Field v-model="formData.issuingAuthority" placeholder="请输入发证机关" readonly />
            </template>
          </Cell>
        </CellGroup>
      </div>

      <!-- 车辆参数 -->
      <div class="form-section">
        <div class="section-title">车辆参数</div>
        <CellGroup inset>
          <Cell title="核定载人数">
            <template #value>
              <Field v-model="formData.approvedPassengers" placeholder="请输入核定载人数" readonly />
            </template>
          </Cell>
          <Cell title="总质量(kg)">
            <template #value>
              <Field v-model="formData.grossMass" placeholder="请输入总质量" readonly />
            </template>
          </Cell>
          <Cell title="整备质量(kg)">
            <template #value>
              <Field v-model="formData.unladenMass" placeholder="请输入整备质量" readonly />
            </template>
          </Cell>
          <Cell title="外廓尺寸">
            <template #value>
              <Field v-model="formData.dimension" placeholder="请输入外廓尺寸" readonly />
            </template>
          </Cell>
        </CellGroup>
      </div>

      <!-- 联系人信息 -->
      <div class="form-section">
        <div class="section-title">联系人信息</div>
        <CellGroup inset>
          <Cell title="联系人" :required="true">
            <template #value>
              <Field
                v-model="formData.contactName"
                placeholder="请输入联系人姓名"

              />
            </template>
          </Cell>
          <Cell title="联系电话" :required="true">
            <template #value>
              <Field
                v-model="formData.contactPhone"
                placeholder="请输入联系人电话"
                type="tel"
              />
            </template>
          </Cell>
        </CellGroup>
      </div>

      <!-- 行驶证图片 - 双页模式 -->
      <div class="form-section" v-if="formData.uploadMode === 'double' && formData.drivingLicenseImgUrl">
        <div class="section-title">行驶证图片</div>
        <div class="img-preview">
          <img
            :src="formData.drivingLicenseImgUrl"
            alt="行驶证双页"
            @click="previewImage(formData.drivingLicenseImgUrl)"
            class="preview-img"
          />
        </div>
      </div>

      <!-- 行驶证图片 - 单页模式 -->
      <div class="form-section" v-if="formData.uploadMode === 'single'">
        <div class="section-title">行驶证图片</div>
        <!-- 正页 -->
        <div class="img-preview" v-if="formData.drivingLicenseFrontImgUrl">
          <div class="img-title">正页</div>
          <img
            :src="formData.drivingLicenseFrontImgUrl"
            alt="行驶证正页"
            @click="previewImage(formData.drivingLicenseFrontImgUrl)"
            class="preview-img"
          />
        </div>
        <!-- 副页 -->
        <div class="img-preview" v-if="formData.drivingLicenseBackImgUrl" style="margin-top: 12px;">
          <div class="img-title">副页</div>
          <img
            :src="formData.drivingLicenseBackImgUrl"
            alt="行驶证副页"
            @click="previewImage(formData.drivingLicenseBackImgUrl)"
            class="preview-img"
          />
        </div>
      </div>

      <!-- 随车清单 -->
      <div class="form-section" v-if="formData.vehicleListImgUrl">
        <div class="section-title">随车清单</div>
        <div class="img-preview">
          <img
            :src="formData.vehicleListImgUrl"
            alt="随车清单"
            @click="previewImage(formData.vehicleListImgUrl)"
            class="preview-img"
          />
        </div>
      </div>

      <!-- 车头照片 -->
      <div class="form-section" v-if="formData.carImgUrl">
        <div class="section-title">车头照片</div>
        <div class="img-preview">
          <img
            :src="formData.carImgUrl"
            alt="车头照片"
            @click="previewImage(formData.carImgUrl)"
            class="preview-img"
          />
        </div>
      </div>
    </div>

    <!-- 底部按钮 -->
    <div class="action-container">
      <Button @click="cancelForm">取消</Button>
      <Button
        type="primary"
        :loading="submitting"
        @click="submitForm"
      >
        确认提交
      </Button>
    </div>


  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import {
  Icon,
  Button,
  Cell,
  Field,
  CellGroup,
  Picker,
  Popup,
  showToast,
  showDialog,
  showImagePreview
} from 'vant';
import vehicleApi from '@/api/biz/vehicleApi';

// 路由
const router = useRouter();
const route = useRoute();
const goBack = () => {
  // 显示确认对话框，与取消按钮行为一致
  showDialog({
    title: '确认返回',
    message: '确定要返回吗？',
    showCancelButton: true,
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    confirmButtonColor: '#1989fa',
  }).then(() => {
    // 清除localStorage中的数据
    localStorage.removeItem('vehicleFormData');
    // 返回上一页
    router.back();
  }).catch(() => {
    // 用户点击取消，继续留在当前页面
  });
};

// 表单数据
const formData = ref({});
const submitting = ref(false);

// 车牌颜色选择器
const showPlateColorPicker = ref(false);
const selectedPlateColor = ref('');
const selectedPlateColorText = ref('');
const plateColorOptions = [
  { text: '蓝牌', value: '0' },
  { text: '黄牌', value: '1' },
  { text: '白牌', value: '2' },
  { text: '黑牌', value: '3' },
  { text: '新能源绿牌', value: '4' },
  { text: '其他', value: '5' },
  { text: '新能源绿黄牌', value: '6' }
];

// 获取默认选中的颜色索引
const getDefaultColorIndex = () => {
  if (!formData.value || !formData.value.plateColor) return 0;
  const index = plateColorOptions.findIndex(option => option.value === formData.value.plateColor);
  return index >= 0 ? index : 0;
};

// 车牌颜色选择确认
const onPlateColorConfirm = (selectedOption) => {
  // 打印日志以便调试
  console.log('选择的原始数据:', selectedOption);

  // 确保 formData.value 存在
  if (!formData.value) {
    formData.value = {};
  }

  // 获取选中的值和文本
  const selectedValue = selectedOption.selectedOptions[0].value;
  const selectedText = selectedOption.selectedOptions[0].text;

  // 设置车牌颜色值和文本
  formData.value.plateColor = selectedValue;
  formData.value.plateColorName = selectedText;

  // 同时更新独立的响应式变量
  selectedPlateColor.value = selectedValue;
  selectedPlateColorText.value = selectedText;

  // 关闭选择器
  showPlateColorPicker.value = false;
};

// 页面加载时获取数据
onMounted(() => {
  // 从 localStorage 中获取表单数据
  const storedData = localStorage.getItem('vehicleFormData');

  if (storedData) {
    try {
      formData.value = JSON.parse(storedData);

      // 如果有车牌颜色值但没有文本，根据值设置文本
      if (formData.value.plateColor && !formData.value.plateColorName) {
        const colorOption = plateColorOptions.find(option => option.value === formData.value.plateColor);
        if (colorOption) {
          formData.value.plateColorName = colorOption.text;
        }
      }

      // 同步设置独立的响应式变量
      selectedPlateColor.value = formData.value.plateColor || '';
      selectedPlateColorText.value = formData.value.plateColorName || '';

      // 打印日志以便调试
      console.log('加载的表单数据:', formData.value);

      // 获取URL中的orgId参数，如果有，则覆盖localStorage中的值
      const orgId = route.query.orgId;
      if (orgId) {
        console.log('从URL中获取到orgId:', orgId);
        formData.value.orgId = orgId;
      } else {
        // 如果URL中没有orgId，但localStorage中也没有，则设置默认值
        if (!formData.value.orgId) {
          console.log('设置默认orgId: 1');
          formData.value.orgId = '1';
        }
      }



      // 确保页面可以滚动
      setTimeout(() => {
        document.body.style.overflow = 'auto';
        document.body.style.height = 'auto';
        document.documentElement.style.overflow = 'auto';
        document.documentElement.style.height = 'auto';

        // 尝试解决iOS上的滚动问题
        const container = document.getElementById('vehicle-form-container');
        if (container) {
          container.style.height = 'auto';
          container.style.overflow = 'auto';
          container.style.webkitOverflowScrolling = 'touch';
        }
      }, 300);
    } catch (error) {
      console.error('解析表单数据失败', error);
    }
  } else {
    // 如果没有数据，返回上一页
    showToast('未找到车辆信息');
    router.back();
  }
});


// 提交表单
const submitForm = async () => {
  try {
    // 验证必填字段
    if (!formData.value.contactName || !formData.value.contactPhone) {
      showToast({
        type: 'fail',
        message: '联系人和联系电话不能为空'
      });
      return;
    }

    if (!formData.value.plateColor) {
      showToast({
        type: 'fail',
        message: '请选择车牌颜色'
      });
      return;
    }

    submitting.value = true;

    // 克隆数据进行处理
    const submitData = { ...formData.value };

    // 设置默认值
    if (!submitData.netStatus) {
      submitData.netStatus = '1'; // 默认未联网
    }

    if (!submitData.verifyStatus) {
      submitData.verifyStatus = '0'; // 默认未审核
    }

    // 从URL中获取orgId参数
    const orgId = route.query.orgId;
    if (orgId) {
      submitData.orgId = orgId;
    } else if (!submitData.orgId) {
      submitData.orgId = '1'; // 设置默认值
    }

    // 确保环保清单图片地址正确传递
    if (submitData.vehicleListImgUrl) {
      // 重命名字段名为environmentalListImgUrl，以匹配后端API期望的字段名
      submitData.environmentalListImgUrl = submitData.vehicleListImgUrl;
    }

    // 提交数据到服务器
    await vehicleApi.vehicleMobileSubmitForm(submitData);

    // 显示成功消息
    showDialog({
      title: '提交成功',
      message: '车辆已提交审核，请耐心等待',
      confirmButtonText: '确定',
      confirmButtonColor: '#1989fa',
    }).then(() => {
      // 清除localStorage中的数据
      localStorage.removeItem('vehicleFormData');
      // 返回上传页面
      router.replace('/h5/vehicle/upload?orgId=' + submitData.orgId);
    });
  } catch (error) {
    console.error('提交失败:', error);
  } finally {
    submitting.value = false;
  }
};

// 取消表单
const cancelForm = () => {
  showDialog({
    title: '确认取消',
    message: '确定要取消吗？',
    showCancelButton: true,
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    confirmButtonColor: '#1989fa',
  }).then(() => {
    // 清除localStorage中的数据
    localStorage.removeItem('vehicleFormData');
    // 返回上传页面
    router.back();
  }).catch(() => {
    // 用户点击取消，继续编辑
  });
};

// 图片预览方法
const previewImage = (url) => {
  if (!url) return;
  const images = [url];
  showImagePreview({
    images,
    closeable: true,
    showIndex: false
  });
};
</script>

<style scoped>
.vehicle-form-h5 {
  min-height: 100vh;
  background-color: #f5f7fa;
  padding-bottom: 80px;
  position: relative;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch; /* 增强iOS滚动体验 */
}

.nav-header {
  position: sticky;
  top: 0;
  z-index: 100;
  height: 46px;
  background-color: #fff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
}

.back-btn {
  font-size: 20px;
  width: 24px;
}

.page-title {
  font-size: 17px;
  font-weight: 500;
}

.placeholder {
  width: 24px;
}

.content-container {
  padding: 12px 12px 0;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  padding-bottom: 100px; /* 增加底部内边距，确保内容不被底部操作栏遮挡 */
}

.form-section {
  margin-bottom: 16px;
}

.section-title {
  font-size: 15px;
  font-weight: 500;
  margin-bottom: 8px;
  color: #1989fa;
  padding-left: 8px;
  border-left: 3px solid #1989fa;
}

.action-container {
  padding: 16px;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  box-shadow: 0 -1px 4px rgba(0, 0, 0, 0.08);
  display: flex;
  gap: 12px;
  z-index: 100; /* 确保底部操作栏始终显示在最上层 */
}

.action-container .van-button {
  flex: 1;
}

.img-preview {
  padding: 12px;
  background-color: #fff;
  border-radius: 8px;
  text-align: center;
}

.preview-img {
  max-width: 100%;
  max-height: 200px;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  cursor: pointer;
}

.img-title {
  font-size: 14px;
  color: #646566;
  margin-bottom: 8px;
  font-weight: 500;
  text-align: left;
}
</style>

<style>
/* 全局样式以确保Toast显示在表单上方 */
.van-toast {
  z-index: 3000 !important;
}

/* 确保页面可以滚动 */
html, body {
  height: 100%;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}
</style>
