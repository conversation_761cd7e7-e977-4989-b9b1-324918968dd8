<template>
	<a-modal
		:title="formData.orderNumber ? `编辑车辆派单 - ${formData.plateNumber}` : '增加车辆派单'"
		:width="720"
		:visible="visible"
		:destroy-on-close="true"
		@cancel="onClose"
		:footer="null"
	>
		<a-form ref="formRef" :model="formData" :rules="formRules" layout="horizontal" :label-col="{ span: 7 }" :wrapper-col="{ span: 17 }">
			<!-- 基本信息 -->
			<a-divider orientation="left">
				<template #plain>
					<tag-outlined />
					<span class="divider-title">基本信息</span>
				</template>
			</a-divider>
			<a-row :gutter="16">
				<a-col :span="12">
					<a-form-item label="派单编号" name="orderNumber" v-if="formData.orderNumber">
						<a-input v-model:value="formData.orderNumber" disabled placeholder="系统自动生成" />
					</a-form-item>
					<a-form-item label="车牌号码" name="plateNumber" v-else>
						<a-auto-complete
							v-model:value="formData.plateNumber"
							placeholder="请输入车牌号码"
							:options="plateOptions"
							@select="handlePlateNumberSelect"
							:filter-option="false"
							@keyup.enter="executeSearch(formData.plateNumber)"
							@search="handlePlateNumberSearch"
						>
							<template #prefix><car-outlined /></template>
							<template #suffix>
								<a-button type="link" size="small" @click="executeSearch(formData.plateNumber)">
									<search-outlined />
								</a-button>
							</template>
						</a-auto-complete>
						<div class="form-item-hint">输入车牌号后按回车键进行搜索</div>
					</a-form-item>
				</a-col>
				<a-col :span="12">
					<a-form-item label="派单类型" name="orderType">
						<a-radio-group v-model:value="formData.orderType" button-style="solid" @change="handleOrderTypeChange">
							<a-radio-button v-for="(dict, index) in orderTypeOptions" :key="index" :value="dict.value">
								{{ dict.label }}
							</a-radio-button>
						</a-radio-group>
					</a-form-item>
				</a-col>
			</a-row>

			<a-row :gutter="16">
				<a-col :span="12">
					<a-form-item label="车辆类别" name="vehicleCategory">
						<a-select v-model:value="formData.vehicleCategory" placeholder="请选择车辆类别" disabled>
							<a-select-option v-for="(dict, index) in vehicleCategoryOptions" :key="index" :value="dict.value">
								{{ dict.label }}
							</a-select-option>
						</a-select>
					</a-form-item>
				</a-col>
			</a-row>

			<!-- 物料信息 -->
			<a-divider orientation="left">
				<template #plain>
					<inbox-outlined />
					<span class="divider-title">物料信息</span>
				</template>
			</a-divider>

			<a-row :gutter="16">
				<a-col :span="12">
					<a-form-item label="物料分类" name="materialCategory">
						<a-select
							v-model:value="formData.materialCategoryId"
							placeholder="请选择物料分类"
							@change="handleCategoryChange"
							allow-clear
							show-search
							:options="categoryOptions"
							:filter-option="filterOption"
						>
							<template #suffixIcon><folder-outlined /></template>
						</a-select>
					</a-form-item>
				</a-col>
				<a-col :span="12">
					<a-form-item label="物料名称" name="material">
						<a-select
							v-model:value="formData.materialId"
							placeholder="请选择物料名称"
							@change="handleMaterialChange"
							allow-clear
							show-search
							:options="materialOptions"
							:filter-option="filterOption"
							:disabled="!formData.materialCategoryId"
						>
							<template #suffixIcon><container-outlined /></template>
						</a-select>
					</a-form-item>
				</a-col>
			</a-row>

			<a-row :gutter="16">
				<a-col :span="12">
					<a-form-item label="物料重量" name="weight">
						<a-row :gutter="8">
							<a-col :span="16">
								<a-input-number
									v-model:value="formData.weight"
									style="width: 100%"
									placeholder="请输入重量"
									:precision="2"
								/>
							</a-col>
							<a-col :span="8">
								<a-select
									v-model:value="formData.unit"
									style="width: 100%"
									placeholder="请选择单位"
								>
									<a-select-option v-for="(dict, index) in unitOptions" :key="index" :value="dict.value">
										{{ dict.label }}
									</a-select-option>
								</a-select>
							</a-col>
						</a-row>
					</a-form-item>
				</a-col>

				<!-- <a-col :span="12">
					<a-form-item label="洗车管控" name="needCarWash">
						<a-select v-model:value="formData.needCarWash" placeholder="请选择是否需要洗车">
							<a-select-option v-for="(dict, index) in carWashOptions" :key="index" :value="dict.value">
								{{ dict.label }}
							</a-select-option>
						</a-select>
					</a-form-item>
				</a-col> -->
			</a-row>

			<!-- 运输信息 -->
			<a-divider orientation="left">
				<template #plain>
					<car-outlined />
					<span class="divider-title">运输信息</span>
				</template>
			</a-divider>

			<a-row :gutter="16">
				<a-col :span="12">
					<a-form-item label="运输发货方" name="sources">
						<a-input v-model:value="formData.sources" placeholder="请输入运输发货方" allow-clear>
							<template #prefix><export-outlined /></template>
						</a-input>
					</a-form-item>
				</a-col>
				<a-col :span="12">
					<a-form-item label="运输收货方" name="target">
						<a-input v-model:value="formData.target" placeholder="请输入运输收货方" allow-clear>
							<template #prefix><import-outlined /></template>
						</a-input>
					</a-form-item>
				</a-col>
			</a-row>

			<a-row :gutter="16" v-if="formData.orderType !== '1'">
				<a-col :span="12">
					<a-form-item label="进厂开始时间" name="startTime">
						<a-date-picker
							v-model:value="formData.startTime"
							value-format="YYYY-MM-DD HH:mm:ss"
							show-time
							placeholder="请选择进厂开始时间"
							style="width: 100%"
						/>
					</a-form-item>
				</a-col>
				<a-col :span="12">
					<a-form-item label="进厂结束时间" name="endTime">
						<a-date-picker
							v-model:value="formData.endTime"
							value-format="YYYY-MM-DD HH:mm:ss"
							show-time
							placeholder="请选择进厂结束时间"
							style="width: 100%"
						/>
					</a-form-item>
				</a-col>
			</a-row>
			<a-row :gutter="16">
				<a-col :span="12">
					<a-form-item label="上传单据" name="orderImgUrl" class="image-form-item">
						<div class="image-container">
							<div class="image-display-area" @click="handleImageClick('orderImgUrl')">
								<template v-if="formData.orderImgUrl">
									<div class="image-wrapper">
										<a-image
											:src="formData.orderImgUrl"
											class="displayed-image"
											:preview="{ src: formData.orderImgUrl }"
										/>
										<div class="custom-overlay">
											<div class="image-hover-actions">
												<eye-outlined class="hover-icon preview-icon" @click.stop="(e) => handlePreview(e, formData.orderImgUrl)" />
												<delete-outlined class="hover-icon delete-icon" @click.stop="(e) => handleImageDelete(e, 'orderImgUrl')" />
											</div>
										</div>
									</div>
								</template>
								<div v-else class="empty-image-placeholder">
									<upload-outlined />
									<span class="placeholder-text">点击上传单据照片</span>
								</div>
							</div>
						</div>
					</a-form-item>
				</a-col>
			</a-row>

			<!-- 隐藏字段，用于存储关联ID等技术字段 -->
			<a-form-item label="车辆备案ID" name="vehicleId" hidden>
				<a-input v-model:value="formData.vehicleId" />
			</a-form-item>
			<a-form-item label="运输台账ID" name="recordId" hidden>
				<a-input v-model:value="formData.recordId" />
			</a-form-item>
			<a-form-item label="ID" name="id" hidden>
				<a-input v-model:value="formData.id" />
			</a-form-item>
		</a-form>

		<div class="modal-footer">
			<a-button style="margin-right: 8px" @click="onClose">取消</a-button>
			<a-button type="primary" @click="onSubmit" :loading="submitLoading">
				<template #icon><save-outlined /></template>
				保存
			</a-button>
		</div>
	</a-modal>
</template>

<script setup name="orderForm">
	import { cloneDeep } from 'lodash-es'
	import { required } from '@/utils/formRules'
	import tool from '@/utils/tool'
	import orderApi from '@/api/biz/orderApi'
	import vehicleApi from '@/api/biz/vehicleApi'
	import materialCategoryApi from '@/api/biz/materialCategoryApi'
	import materialDetailApi from '@/api/biz/materialDetailApi'
	import { message, Modal } from 'ant-design-vue'
	import {
		CarOutlined,
		SaveOutlined,
		TagOutlined,
		InboxOutlined,
		ContainerOutlined,
		ExportOutlined,
		ImportOutlined,
		FolderOutlined,
		SearchOutlined,
		UploadOutlined,
		EyeOutlined,
		DeleteOutlined
	} from '@ant-design/icons-vue'
	import fileApi from '@/api/dev/fileApi'
	import { onMounted, onUnmounted } from 'vue'
import { log } from '@antv/g2plot/lib/utils'

	// 弹窗状态
	const visible = ref(false)
	const emit = defineEmits({ successful: null })
	const formRef = ref()
	// 表单数据
	const formData = ref({})
	const submitLoading = ref(false)
	// 车牌号搜索选项
	const plateOptions = ref([])
	// 文件上传相关
	const fileInput = ref(null)
	const currentField = ref('')

	// 物料分类和明细数据
	const categoryOptions = ref([])
	const materialOptions = ref([])

	// 字典数据
	const orderTypeOptions = tool.dictList('ORDER_TYPE')
	const vehicleCategoryOptions = tool.dictList('VEHICLE_CATEGORY')
	const unitOptions = tool.dictList('MEASURE_UNIT')

	// 实际执行车牌号码搜索的方法
	const executeSearch = async (value) => {
		if (!value || value.length < 2) {
			plateOptions.value = []
			return
		}

		try {
			const result = await vehicleApi.loadVehicleForOrder({ plateNumber: value })
			if (result && Array.isArray(result)) {
				plateOptions.value = result.map(item => {
					// 获取车辆类别的字典标签
					let categoryLabel = '未知类型'
					if (item.vehicleCategory) {
						const categoryOption = vehicleCategoryOptions.find(opt => opt.value === item.vehicleCategory)
						if (categoryOption) {
							categoryLabel = categoryOption.label
						}
					}

					return {
						value: item.id,
						label: `${item.plateNumber} - ${categoryLabel}`,
						plateNumber: item.plateNumber,
						item: item
					}
				})


			} else if (result && result.id) {
				// 如果返回单个对象而不是数组
				// 获取车辆类别的字典标签
				let categoryLabel = '未知类型'
				if (result.vehicleCategory) {
					const categoryOption = vehicleCategoryOptions.find(opt => opt.value === result.vehicleCategory)
					if (categoryOption) {
						categoryLabel = categoryOption.label
					}
				}

				plateOptions.value = [{
					value: result.id,
					label: `${result.plateNumber} - ${categoryLabel}`,
					plateNumber: result.plateNumber,
					item: result
				}]
			} else {
				plateOptions.value = []
			}
		} catch (error) {
			console.error('搜索车牌号失败:', error)
			plateOptions.value = []
		}
	}

	// 处理车牌号码选择
	const handlePlateNumberSelect = (value) => {

		// 现在value是车辆ID，直接找到对应的选项
		const selected = plateOptions.value.find(option => option.value === value)
		if (selected && selected.item) {
			// 设置车辆ID（直接使用value，因为现在value就是车辆ID）
			formData.value.vehicleId = value
			// 设置车牌号
			formData.value.plateNumber = selected.plateNumber

			// 设置车辆类型
			if (selected.item.vehicleCategory) {
				formData.value.vehicleCategory = selected.item.vehicleCategory
			}

			message.success('已选择车辆信息')
		}
	}

	// 处理车牌号搜索输入
	const handlePlateNumberSearch = (value) => {
		// 清空车辆ID
		formData.value.vehicleId = undefined

		// 清空车牌号搜索选项
		plateOptions.value = []

		//
		formData.value.vehicleCategory = undefined

	}

	// 加载物料分类数据
	const loadCategories = async () => {
		try {
			const response = await materialCategoryApi.materialCategoryPage({
				size: 500,
				current: 1,
			})

			if (response && response.records) {
				categoryOptions.value = response.records.map(item => ({
					value: item.id,
					label: item.name,
					record: item
				}))
			}
		} catch (error) {
			console.error('加载物料分类失败:', error)
		}
	}

	// 根据分类ID加载物料明细
	const loadMaterialsByCategory = async (categoryId) => {
		if (!categoryId) {
			materialOptions.value = []
			return
		}

		try {
			const response = await materialDetailApi.materialDetailPage({
				size: 500,
				current: 1,
				categoryId: categoryId
			})

			if (response && response.records) {
				materialOptions.value = response.records.map(item => ({
					value: item.name,
					label: item.name,
					record: item
				}))
			} else {
				materialOptions.value = []
			}
		} catch (error) {
			console.error('加载物料明细失败:', error)
			materialOptions.value = []
		}
	}

	// 处理物料分类变化
	const handleCategoryChange = (value) => {
		// 清空已选物料明细
		formData.value.materialId = undefined
		formData.value.material = ''

		// 根据新选择的分类加载物料明细
		loadMaterialsByCategory(value)

		// 更新分类名称
		if (value) {
			const category = categoryOptions.value.find(item => item.value === value)
			if (category) {
				formData.value.materialCategory = category.label
			}
		} else {
			formData.value.materialCategory = ''
		}
	}

	// 处理物料明细变化
	const handleMaterialChange = (value) => {
		if (value) {
			const material = materialOptions.value.find(item => item.value === value)
			if (material && material.record) {
				// 填充物料名称
				formData.value.material = material.label

				// 如果物料记录中有其他需要的属性，可以在这里设置
				// 例如: 是否需要洗车
				if (material.record.needCarWash) {
					formData.value.needCarWash = material.record.needCarWash ? '1' : '0'
				}

				// 其他属性...
			}
		} else {
			formData.value.material = ''
		}
	}

	// 处理派单类型变化
	const handleOrderTypeChange = (e) => {
		const orderType = e.target ? e.target.value : e

		// 如果是长期生效(1)，隐藏并清空时间字段
		if (orderType === '1') {
			formData.value.startTime = null
			formData.value.endTime = null
		}
	}

	// 点击图片区域处理
	const handleImageClick = (field) => {
		if (formData.value[field]) {
			// 如果已有图片，不触发上传
			return
		}
		currentField.value = field
		fileInput.value.click()
	}

	// 文件选择后处理
	const handleFileSelected = async (event) => {
		const file = event.target.files[0]
		if (!file) return

		// 验证文件类型
		const isImage = file.type.startsWith('image/')
		if (!isImage) {
			message.error('请选择图片文件')
			fileInput.value.value = null
			return
		}

		// 验证文件大小 (限制为5MB)
		if (file.size > 5 * 1024 * 1024) {
			message.error('图片大小不能超过5MB')
			fileInput.value.value = null
			return
		}

		try {
			const uploadData = new FormData()
			uploadData.append('file', file)

			// 上传图片
			const result = await fileApi.fileUploadLocalReturnUrl(uploadData)
			if (result) {
				// 更新表单数据
				formData.value[currentField.value] = result
				message.success('上传成功')
			} else {
				message.error('上传失败')
			}
		} catch (error) {
			console.error('上传图片失败:', error)
		} finally {
			// 重置文件输入
			fileInput.value.value = null
		}
	}

	// 删除图片
	const removeImage = () => {
		formData.value.orderImgUrl = ''
	}

	// 带确认的删除图片
	const handleImageDelete = (e, field) => {
		e.stopPropagation() // 阻止事件冒泡

		// 显示确认对话框
		Modal.confirm({
			title: '确认删除',
			content: '确定要删除单据照片吗？',
			okText: '确认',
			cancelText: '取消',
			onOk: () => {
				formData.value[field] = ''
				message.success('已删除单据照片')
			}
		})
	}

	// 手动预览图片
	const handlePreview = (e, url) => {
		e.stopPropagation()
		// 查找对应的a-image组件，并触发其预览
		const image = e.target.closest('.image-wrapper').querySelector('.ant-image')
		if (image) {
			// 触发a-image的点击事件
			image.click()
		}
	}

	// 打开弹窗
	const onOpen = async (record) => {
		visible.value = true
		await loadCategories() // 确保每次打开表单都重新加载分类，并等待加载完成

		// 初始化表单默认值
		formData.value = {
			orderType: '0',
			vehicleCategory: undefined,  // 默认为undefined，确保placeholder显示
			status: '0',
			needCarWash: '0',
			unit: '1',  // 默认单位为吨
			orderImgUrl: ''  // 初始化单据图片URL
		}

		if (record) {
			let recordData = cloneDeep(record)
			formData.value = Object.assign({}, formData.value, recordData)

			// 处理派单类型对时间字段的影响
			handleOrderTypeChange(formData.value.orderType)

			// 如果是编辑模式且有分类ID，加载对应的物料明细
			if (recordData.materialCategoryId) {
				await loadMaterialsByCategory(recordData.materialCategoryId)
			}
			// 如果没有分类ID但有分类名称，则查找对应的分类ID
			else if (recordData.materialCategory) {
				// 查找匹配的分类
				const category = categoryOptions.value.find(item => item.label === recordData.materialCategory)
				if (category) {
					formData.value.materialCategoryId = category.value
					await loadMaterialsByCategory(category.value)

					// 如果有物料名称，查找对应的物料ID
					if (recordData.material) {
						const material = materialOptions.value.find(item => item.label === recordData.material)
						if (material) {
							formData.value.materialId = material.value
						}
					}
				}
			}
		}
	}

	// 创建隐藏的文件上传输入
	onMounted(() => {
		const input = document.createElement('input')
		input.type = 'file'
		input.accept = 'image/*'
		input.style.display = 'none'
		input.addEventListener('change', handleFileSelected)
		document.body.appendChild(input)
		fileInput.value = input
	})

	// 组件销毁时移除文件输入
	onUnmounted(() => {
		if (fileInput.value) {
			fileInput.value.removeEventListener('change', handleFileSelected)
			document.body.removeChild(fileInput.value)
		}
	})

	// 关闭弹窗
	const onClose = () => {
		formRef.value.resetFields()
		formData.value = {}
		plateOptions.value = [] // 清空车牌号搜索下拉选项
		visible.value = false
	}

	// 表单验证规则
	const formRules = {
		plateNumber: [required('请选择车牌号码')],
		materialCategoryId: [required('请选择物料分类')],
		material: [required('请选择物料名称')],
		weight: [required('请输入物料重量')],
		unit: [required('请选择重量单位')],
		sources: [required('请输入运输发货方')],
		target: [required('请输入运输收货方')]
	}

	// 验证并提交数据
	const onSubmit = () => {
		// 如果没有vehicleId，说明没有从搜索结果中选择车牌
		if (!formData.value.orderNumber && !formData.value.vehicleId) {
			message.error('请从搜索结果中选择有效的车牌号')
			return
		}

		formRef.value.validate().then(() => {
			submitLoading.value = true
			const formDataParam = cloneDeep(formData.value)

			// 确保长期生效的派单时间字段为空
			if (formDataParam.orderType === '1') {
				formDataParam.startTime = null
				formDataParam.endTime = null
			}
			orderApi
				.orderSubmitForm(formDataParam, formDataParam.id)
				.then(() => {
					onClose()
					emit('successful')
				})
				.finally(() => {
					submitLoading.value = false
				})
		})
	}

	// 抛出函数
	defineExpose({
		onOpen
	})
</script>

<style scoped>
.divider-title {
	margin-left: 8px;
	font-weight: 500;
	font-size: 15px;
}

:deep(.ant-form-item-label) {
	text-align: right;
}

:deep(.ant-divider) {
	margin: 16px 0;
}

.modal-footer {
	margin-top: 24px;
	text-align: right;
}

.form-item-hint {
	font-size: 12px;
	color: #999;
	margin-top: 4px;
}

.image-container {
  display: flex;
  flex-direction: column;
  max-width: 100%;
  margin-bottom: 16px;
}

.image-display-area {
  width: 100%;
  height: 160px;
  background-color: #fafafa;
  border: 1px dashed #d9d9d9;
  border-radius: 4px;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  position: relative;
  transition: all 0.3s;
  cursor: pointer;
}

.image-display-area:hover {
  border-color: #1890ff;
}

.empty-image-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #d9d9d9;
}

.empty-image-placeholder .anticon {
  font-size: 32px;
  margin-bottom: 8px;
}

.placeholder-text {
  font-size: 14px;
  color: #bfbfbf;
}

.displayed-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s;
}

.image-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
}

.image-wrapper :deep(.ant-image) {
  width: 100%;
  height: 100%;
  display: block;
}

.image-wrapper :deep(.ant-image-img) {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.custom-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 10;
}

.image-wrapper:hover .custom-overlay {
  opacity: 1;
}

.image-hover-actions {
  display: flex;
  gap: 16px;
}

.hover-icon {
  font-size: 20px;
  color: white;
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.5);
  transition: transform 0.2s, background-color 0.2s;
}

.hover-icon:hover {
  transform: scale(1.2);
  background-color: rgba(0, 0, 0, 0.7);
}

.preview-icon {
  background-color: rgba(24, 144, 255, 0.7);
}

.preview-icon:hover {
  background-color: rgba(24, 144, 255, 0.9);
}

.delete-icon {
  background-color: rgba(255, 77, 79, 0.7);
}

.delete-icon:hover {
  background-color: rgba(255, 77, 79, 0.9);
}
</style>
