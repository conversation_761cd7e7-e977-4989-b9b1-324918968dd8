<template>
  <div class="video-player-container">
    <a-card :bordered="false" class="video-card">
      <template #title>
        <div class="player-header">
          <div class="title-container">
            <camera-outlined />
            <span class="title">{{ currentCamera ? currentCamera.label : '视频预览' }}</span>
            <a-tag v-if="isPlaying" color="success">正在播放</a-tag>
            <a-tag v-else-if="isInitialized" color="warning">就绪</a-tag>
            <a-tag v-else color="default">加载中...</a-tag>
          </div>
          <div class="area-tag" v-if="currentCamera && currentCamera.area">
            {{ currentCamera.area }}
          </div>
        </div>
      </template>

      <div class="main-content">
        <div class="video-view">
          <div class="player-window-container">
            <div id="playWnd" class="player-window"></div>
            <div class="player-placeholder" v-if="!isInitialized">
              <play-circle-outlined />
              <p>正在加载插件，请稍候...</p>
            </div>
            <div class="player-placeholder waiting" v-else-if="!isPlaying && !currentCamera">
              <video-camera-outlined />
              <p>请从左侧选择一个摄像头</p>
            </div>
            <div class="player-overlay" v-if="isLoading">
              <a-spin tip="加载中..." />
            </div>
          </div>
        </div>
      </div>
      <!-- 插件初始化失败时显示重试按钮 -->
      <div v-if="!isInitialized && !isLoading" style="text-align:center;margin-top:16px;">
        <a-button type="primary" @click="() => { retryCount = 0; initPlugin(); }">重试插件初始化</a-button>
      </div>
    </a-card>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, watch, nextTick } from 'vue'
import {
  PlayCircleOutlined, DeleteOutlined,
  VideoCameraOutlined, RightOutlined, LeftOutlined
} from '@ant-design/icons-vue'
import configApi from '@/api/dev/configApi'
import { data } from '@/components/Cron/data'

// 插件配置信息
const hikConfig = {
  appkey: "", // 需要替换为实际的appkey
  ip: "", // 需要替换为实际的IP
  port: 443,
  secret: "", // 需要替换为实际的secret
  enableHTTPS: 1,
  language: "zh_CN",
  layout: "1x1", // 单窗口布局
  playMode: 0, // 0为预览模式
  reconnectDuration: 5,
  reconnectTimes: 5,
  showSmart: 0,
  showToolbar: 1,
  // 工具栏按钮IDs
  toolBarButtonIDs: "2048,2049,2050,2304,2306,2305,2307,2308,2309,4096,4608,4097,4099,4098,4609,4100",
  snapDir: "D:/snap", // 截图保存目录
  videoDir: "D:/video" // 录像保存目录
}

// 插件对象实例
let oWebControl = null
// 状态变量
const isInitialized = ref(false)
const isPlaying = ref(false)
const isLoading = ref(false)
const currentCamera = ref(null)
const currentLayout = ref('1x1')
const currentWndId = ref(0) // 当前选中的窗口ID，默认为0
const playingCameras = ref({}) // 记录每个窗口正在播放的摄像头 {窗口ID: 摄像头对象}



// 添加一个超时控制变量，确保连接超时也能释放资源
let connectionTimeoutId = null;

// 新增全局变量保存公钥
let pubKey = ''

// 重连相关状态
let retryCount = 0
const maxRetry = 3
const retryDelay = 2000 // ms

// 接收父组件传递的摄像头信息
const props = defineProps({
  camera: {
    type: Object,
    default: null
  },
  cameraList: {
    type: Array,
    default: () => []
  }
})

// 定义事件
const emit = defineEmits(['select-camera'])

// 监听摄像头变化
watch(() => props.camera, (newCamera) => {
  if (newCamera && newCamera.cameraIndexCode) {
    currentCamera.value = newCamera
    if (isInitialized.value) {
      isLoading.value = true

      // 停止当前窗口的预览，而不是所有预览
      const stopSinglePreview = () => {
        return oWebControl.JS_RequestInterface({
          funcName: "stopPreview",
          argument: {
            wndId: currentWndId.value
          }
        }).then(() => {
          console.info(`停止窗口 ${currentWndId.value} 的预览`)
          return Promise.resolve()
        }).catch((error) => {
          console.error(`停止预览失败: ${JSON.stringify(error)}`)
          return Promise.resolve() // 即使失败也继续
        })
      }

      // 先停止当前窗口预览，然后开始新预览
      stopSinglePreview().then(() => {
        setTimeout(() => {
          startPreview(newCamera.cameraIndexCode, currentWndId.value)
        }, 100)
      })
    } else {
      console.warn('插件未初始化，请稍候')
    }
  }
}, { deep: true })

// 简化的日志函数，只输出到控制台
const log = (type, message) => {
  const logMethod = type === 'success' ? 'info' : type
  if (console[logMethod]) {
    console[logMethod](`[视频播放器] ${message}`)
  } else {
    console.log(`[视频播放器-${type}] ${message}`)
  }
}

// 获取RSA公钥
const getPubKey = (callback) => {
  if (!oWebControl) return
  oWebControl.JS_RequestInterface({
    funcName: "getRSAPubKey",
    argument: JSON.stringify({ keyLength: 1024 })
  }).then(function (oData) {
    if (oData.responseMsg && oData.responseMsg.data) {
      // 兼容返回格式
      if (typeof oData.responseMsg.data === 'string') {
        try {
          pubKey = JSON.parse(oData.responseMsg.data).rsaPubKey
        } catch (e) {
          pubKey = oData.responseMsg.data
        }
      } else if (oData.responseMsg.data.rsaPubKey) {
        pubKey = oData.responseMsg.data.rsaPubKey
      }
      log('success', '获取RSA公钥成功')
      callback && callback()
    } else {
      log('error', '获取RSA公钥失败')
      // 自动重试
      if (retryCount < maxRetry) {
        retryCount++
        log('warning', `获取RSA公钥失败，正在第${retryCount}次重试...`)
        setTimeout(() => {
          initPlugin()
        }, retryDelay)
      } else {
        log('error', '插件初始化失败，请点击重试')
      }
    }
  }).catch(function (e) {
    log('error', '获取RSA公钥异常')
    // 自动重试
    if (retryCount < maxRetry) {
      retryCount++
      log('warning', `获取RSA公钥异常，正在第${retryCount}次重试...`)
      setTimeout(() => {
        initPlugin()
      }, retryDelay)
    }
  })
}

// 设置加密
const setEncrypt = (value) => {
  if (!pubKey) return value
  const encrypt = new window.JSEncrypt()
  encrypt.setPublicKey(pubKey)
  return encrypt.encrypt(value)
}

// 统一封装插件接口调用
const callPluginInterface = (funcName, argumentObj) => {
  const argStr = argumentObj ? JSON.stringify(argumentObj) : undefined
  return oWebControl.JS_RequestInterface({
    funcName,
    ...(argStr ? { argument: argStr } : {})
  }).then(res => {
    return res
  }).catch(err => {
    log('error', `插件接口${funcName}异常`)
    throw err
  })
}

// 初始化插件
const initPlugin = () => {
  console.log('初始化视频插件')
  isLoading.value = true
  log('info', '正在加载插件...')

  // 如果已经初始化，先销毁
  if (window.oWebControl != null) {
    try {
      // 使用RequestInterface方法进行反初始化，而不是直接调用JS_Uninit
      window.oWebControl.JS_RequestInterface({
        funcName: "uninit"
      }).catch(e => {
        console.error("卸载失败", e)
      })
    } catch (e) {
      console.error("卸载失败", e)
    }
  }

  // 清除可能存在的超时计时器
  if (connectionTimeoutId) {
    clearTimeout(connectionTimeoutId)
    connectionTimeoutId = null
  }

  // 检查全局对象是否已存在
  if (window.jQuery && window.JSEncrypt && window.WebControl) {
    log('info', '海康SDK已加载，直接初始化')
    initializeWebControl()

    // 添加连接超时检查
    connectionTimeoutId = setTimeout(() => {
      if (isLoading.value && !isInitialized.value) {
        log('error', '插件连接超时，请刷新页面重试')
        isLoading.value = false

        // 强制清理资源
        if (oWebControl) {
          try {
            oWebControl.JS_Disconnect && oWebControl.JS_Disconnect().catch(e => {})
            oWebControl.JS_DestroyWnd && oWebControl.JS_DestroyWnd().catch(e => {})
            oWebControl = null
            window.oWebControl = null
          } catch (e) {
            console.error('清理超时连接资源失败:', e)
          }
        }
      }
    }, 25000) // 25秒超时
  } else {
    // 如果SDK对象不存在，提示错误
    log('error', '找不到海康SDK对象，请确保页面已正确加载SDK脚本')
    isLoading.value = false
  }
}

// 初始化Web Control
const initializeWebControl = () => {
  oWebControl = new window.WebControl({
    szPluginContainer: 'playWnd',
    iServicePortStart: 15900,
    iServicePortEnd: 15909,
    cbConnectSuccess: function() {
      setCallbacks()
      if (connectionTimeoutId) {
        clearTimeout(connectionTimeoutId)
        connectionTimeoutId = null
      }
      oWebControl.JS_StartService('window', {
        dllPath: './VideoPluginConnect.dll',
        manifastUrl: '',
        contextMenu: false
      }).then(function() {
        const element = document.getElementById('playWnd')
        const width = element ? element.clientWidth : 800
        const height = element ? element.clientHeight : 400
        oWebControl.JS_CreateWnd('playWnd', width, height).then(function() {
          // 注册插件实例
          registerPluginInstance();
          // 先获取公钥，再初始化参数
          getPubKey(() => {
            initPluginParams()
          })
        }).catch(function(error) {
          isLoading.value = false
          log('error', `创建窗口失败`)
        })
      }).catch(function(error) {
        isLoading.value = false
        log('error', `启动服务失败`)
      })
    },
    cbConnectError: function(e) {
      log('error', '插件连接错误')
      isLoading.value = false
    }
  })
  window.oWebControl = oWebControl
}

// 设置回调函数
const setCallbacks = () => {
  oWebControl.JS_SetWindowControlCallback({
    cbIntegrationCallBack: (data) => {
      // 处理窗口选择事件
      if (data && data.type === 'selectedWnd') {
        currentWndId.value = parseInt(data.wndId || 0)
        log('info', `选中窗口: ${currentWndId.value}`)
      }
    }
  })
}

// 初始化参数
const initPluginParams = () => {
  const cameraConfig = currentCamera.value ? {
    appkey: currentCamera.value.appkey || hikConfig.appkey,
    ip: currentCamera.value.ip || hikConfig.ip,
    port: currentCamera.value.port || hikConfig.port,
    secret: currentCamera.value.secret || hikConfig.secret
  } : hikConfig
  const encryptedAppkey = setEncrypt(cameraConfig.appkey)
  const encryptedSecret = setEncrypt(cameraConfig.secret)
  const initParam = {
    appkey: encryptedAppkey,
    ip: cameraConfig.ip,
    port: parseInt(cameraConfig.port),
    secret: encryptedSecret,
    enableHTTPS: hikConfig.enableHTTPS,
    language: hikConfig.language,
    layout: currentLayout.value,
    playMode: hikConfig.playMode,
    reconnectDuration: hikConfig.reconnectDuration,
    reconnectTimes: hikConfig.reconnectTimes,
    showSmart: hikConfig.showSmart,
    showToolbar: hikConfig.showToolbar,
    toolBarButtonIDs: hikConfig.toolBarButtonIDs,
    snapDir: hikConfig.snapDir,
    videoDir: hikConfig.videoDir,
    encryptedFields: 'appkey,secret'
  }
  callPluginInterface('init', initParam)
    .then(function () {
      log('success', '插件初始化成功')
      isLoading.value = false
      isInitialized.value = true
      retryCount = 0
      resizeWindow()
      setTimeout(resizeWindow, 300)
      setTimeout(resizeWindow, 800)

      // 只有当已经有摄像头被选中时才播放
      if (currentCamera.value && currentCamera.value.cameraIndexCode) {
        startPreview(currentCamera.value.cameraIndexCode)
      } else {
        // 不再自动选择第一个摄像头
        log('info', '插件已就绪，请从左侧选择一个摄像头进行播放')
      }
    }).catch(function (error) {
      isLoading.value = false
      log('error', `插件初始化失败`)
      if (retryCount < maxRetry) {
        retryCount++
        log('warning', `插件初始化失败，正在第${retryCount}次重试...`)
        setTimeout(() => {
          initPlugin()
        }, retryDelay)
      } else {
        log('error', '插件初始化失败，请点击重试')
      }
    })
}

// 开始预览
const startPreview = (cameraIndexCode, wndId = -1) => {
  if (!oWebControl || !isInitialized.value) {
    isLoading.value = false
    log('warning', '插件未初始化，无法开始预览')
    return
  }
  const targetWndId = wndId >= 0 ? wndId : currentWndId.value
  const previewParam = {
    cameraIndexCode: cameraIndexCode,
    streamMode: 0,
    transMode: 1,
    gpuMode: 0,
    wndId: targetWndId,
    ezvizDirect: 0,
    cascade: 1
  }
  if (currentCamera.value && currentCamera.value.username && currentCamera.value.password) {
    previewParam.username = currentCamera.value.username
    previewParam.password = currentCamera.value.password
  }
  log('info', `开始预览摄像头(${cameraIndexCode}) - 窗口ID: ${targetWndId}`)
  callPluginInterface('startPreview', previewParam)
    .then(function(data) {
      isLoading.value = false
      log('success', `开始预览成功 - 摄像头编号: ${cameraIndexCode}, 窗口ID: ${targetWndId}`)
      isPlaying.value = true
      playingCameras.value[targetWndId] = { ...currentCamera.value, cameraIndexCode }
      resizeWindow()
      setTimeout(resizeWindow, 300)
    }).catch(function(error) {
      isLoading.value = false
      log('error', `开始预览失败`)
    })
}

// 停止所有预览
const stopAllPreview = () => {
  if (!oWebControl || !isInitialized.value) {
    isLoading.value = false
    return Promise.resolve()
  }
  return callPluginInterface('stopAllPreview')
    .then(function(data) {
      log('info', '停止所有预览')
      isPlaying.value = false
      return Promise.resolve()
    }).catch(function(error) {
      log('error', `停止预览失败`)
      return Promise.reject(error)
    })
}

// 视频窗口大小调整
const resizeWindow = () => {
  if (oWebControl && isInitialized.value) {
    const element = document.getElementById('playWnd')
    if (element) {
      try {
        const parent = element.parentElement
        // 先调整容器大小，确保能获取到正确的尺寸
        if (parent) {
          parent.style.width = '100%'
          parent.style.height = '100%'
        }

        // 获取实际尺寸
        const width = element.clientWidth || element.offsetWidth || parent?.clientWidth || 800
        const height = element.clientHeight || element.offsetHeight || parent?.clientHeight || 400

        // 确保尺寸合理
        if (width > 10 && height > 10) {
          try {
            // 调整尺寸
            oWebControl.JS_Resize(width, height);
          } catch (e) {
            console.error('视频窗口调整失败: ' + e)
          }
        } else {
          console.warn(`视频窗口尺寸过小: width=${width}, height=${height}`)
        }
      } catch (e) {
        console.error('调整视频窗口大小失败: ' + e)
      }
    }
  }
}

// 切换布局
const setLayout = (newLayout) => {
  if (!oWebControl || !isInitialized.value) {
    log('warning', '插件未初始化，无法切换布局')
    return Promise.reject('插件未初始化')
  }
  return callPluginInterface('setLayout', { layout: newLayout })
    .then(function(data) {
      currentLayout.value = newLayout
      currentWndId.value = 0
      log('success', `布局切换成功: ${newLayout}`)
      return Promise.resolve()
    }).catch(function(error) {
      log('error', `布局切换失败`)
      return Promise.reject(error)
    })
}

// 插件资源释放流程，严格按顺序
const releasePluginResources = async () => {
  if (!oWebControl) {
    return
  }
  if (!isInitialized.value) {
    return
  }
  try {
    if (typeof oWebControl.JS_HideWnd === 'function') {
      try {
        await oWebControl.JS_HideWnd()
      } catch (e) {
        console.warn('JS_HideWnd异常: ' + e)
      }
    }
    if (typeof oWebControl.JS_Disconnect === 'function') {
      try {
        await oWebControl.JS_Disconnect()
      } catch (e) {
        console.warn('JS_Disconnect异常: ' + e)
      }
    }
    if (typeof oWebControl.JS_DestroyWnd === 'function') {
      try {
        await oWebControl.JS_DestroyWnd()
      } catch (e) {
        console.warn('JS_DestroyWnd异常: ' + e)
      }
    }
  } catch (e) {
    console.error('插件资源释放流程异常: ' + e)
  }
}

// 反初始化插件
const uninitPlugin = async () => {
  if (oWebControl && isInitialized.value) {
    try {
      console.log('正在销毁海康插件...')
      await releasePluginResources()
      // 使用变量保存Promise，确保可以正确处理
      const uninitPromise = oWebControl.JS_RequestInterface({
        funcName: "uninit"
      })
      if (uninitPromise && typeof uninitPromise.then === 'function') {
        uninitPromise.then(() => {
          console.log('海康插件销毁成功')
          oWebControl = null
          window.oWebControl = null
        }).catch(e => {
          console.error('海康插件销毁失败:', e)
          oWebControl = null
          window.oWebControl = null
        }).finally(() => {
          cleanupGlobalReferences()
        })
      } else {
        console.log('海康插件uninit未返回Promise，直接进行资源清理')
        oWebControl = null
        window.oWebControl = null
        cleanupGlobalReferences()
      }
    } catch (e) {
      console.error('销毁插件时发生错误:', e)
      oWebControl = null
      window.oWebControl = null
      cleanupGlobalReferences()
    }
  } else {
    cleanupGlobalReferences()
  }
}

// 简化setupGlobalCleanup函数，减少性能开销
const setupGlobalCleanup = () => {
  // 确保只添加一次全局清理函数
  if (!window._hikvisionGlobalCleanupInitialized) {
    window._hikvisionGlobalCleanupInitialized = true;

    // 添加全局卸载事件，清理所有可能残留的插件实例
    window.addEventListener('unload', () => {
      // 查找并清理所有可能残留的海康插件实例
      const possibleWebControls = window._hikvisionWebControls || [];
      if (possibleWebControls.length > 0) {
        // 处理所有实例
        possibleWebControls.forEach(control => {
          try {
            if (control && typeof control.JS_HideWnd === 'function') {
              control.JS_HideWnd();
              if (control && typeof control.JS_Disconnect === 'function') {
                control.JS_Disconnect();
              }
              if (control && typeof control.JS_DestroyWnd === 'function') {
                control.JS_DestroyWnd();
              }
            }
          } catch (e) {
            console.error('清理残留插件失败', e);
          }
        });
      } else if (window.oWebControl) {
        // 如果注册表为空，检查window.oWebControl
        try {
          if (typeof window.oWebControl.JS_HideWnd === 'function') {
            window.oWebControl.JS_HideWnd();
            window.oWebControl.JS_Disconnect();
            window.oWebControl.JS_DestroyWnd();
          }
        } catch (e) {
          console.error('清理window.oWebControl失败', e);
        }
      }

      // 清空插件实例数组
      window._hikvisionWebControls = [];
      window.oWebControl = null;
    });
  }
}

// 恢复原始的destroyPluginAsync函数，简化实现
function destroyPluginAsync() {
  try {
    if (oWebControl && typeof oWebControl.JS_HideWnd === 'function') {
      oWebControl.JS_HideWnd().catch(e => console.warn('JS_HideWnd异常: ' + e))
    }
    if (oWebControl && typeof oWebControl.JS_Disconnect === 'function') {
      oWebControl.JS_Disconnect().catch(e => console.warn('JS_Disconnect异常: ' + e))
    }
    if (oWebControl && typeof oWebControl.JS_DestroyWnd === 'function') {
      oWebControl.JS_DestroyWnd().catch(e => console.warn('JS_DestroyWnd异常: ' + e))
    }
    if (oWebControl && typeof oWebControl.JS_RequestInterface === 'function') {
      oWebControl.JS_RequestInterface({ funcName: "uninit" }).catch(e => console.warn('uninit异常: ' + e))
    }
  } catch (e) {
    console.error('destroyPluginAsync异常: ' + e)
  }
}

// 修改registerPluginInstance函数，不区分环境
const registerPluginInstance = () => {
  if (!window._hikvisionWebControls) {
    window._hikvisionWebControls = [];
  }
  if (oWebControl) {
    window._hikvisionWebControls.push(oWebControl);
  }
}

// 页面卸载前的处理函数
const handleBeforeUnload = () => {
  console.log('页面即将卸载，执行最终清理');
  ensurePluginDestroy();
}

// 在组件卸载前停止所有播放并销毁插件
onBeforeUnmount(() => {
  console.log('视频组件准备卸载...')

  // 1. 尝试同步隐藏插件窗口
  try {
    if (oWebControl && typeof oWebControl.JS_HideWnd === 'function') {
      oWebControl.JS_HideWnd().catch(() => {})
    }
  } catch (e) {
    console.warn('同步JS_HideWnd异常: ' + e)
  }

  // 2. 多次延迟移除 DOM，确保彻底清理
  const removeDom = () => {
    const playWnd = document.getElementById('playWnd')
    if (playWnd) {
      playWnd.innerHTML = '';
    }
  }
  removeDom()

  // 3. 同步清理引用和定时器
  unregisterPluginInstance(); // 从全局注册表移除
  oWebControl = null
  window.oWebControl = null
  isInitialized.value = false
  isPlaying.value = false
  isLoading.value = false
  if (window.resizeTimer) {
    clearTimeout(window.resizeTimer)
    window.resizeTimer = null
  }
  if (connectionTimeoutId) {
    clearTimeout(connectionTimeoutId)
    connectionTimeoutId = null
  }
  window.removeEventListener('resize', resizeWindow)
  window.removeEventListener('scroll', resizeWindow)
  window.removeEventListener('beforeunload', handleBeforeUnload)

  // 4. 异步触发插件资源释放
  destroyPluginAsync()
  // 添加资源释放保障机制
  ensurePluginDestroy();

  console.log('视频组件卸载完成')

  // 断开 ResizeObserver
  if (resizeObserver) {
    resizeObserver.disconnect()
    resizeObserver = null
  }
})

// 清理全局引用
const cleanupGlobalReferences = () => {
  try {
    console.log('清理全局引用...')

    // 清理全局对象引用
    oWebControl = null
    window.oWebControl = null

    // 清理所有定时器
    if (window.resizeTimer) {
      clearTimeout(window.resizeTimer)
      window.resizeTimer = null
    }

    if (connectionTimeoutId) {
      clearTimeout(connectionTimeoutId)
      connectionTimeoutId = null
    }

    // 移除DOM变化监听
    if (window.videoObserver) {
      window.videoObserver.disconnect()
      window.videoObserver = null
    }

    // 移除菜单观察器
    if (window.menuObserver) {
      window.menuObserver.disconnect()
      window.menuObserver = null
    }

    // 移除事件侦听器
    window.removeEventListener('resize', resizeWindow)
    window.removeEventListener('scroll', resizeWindow)
    window.removeEventListener('beforeunload', handleBeforeUnload)

    // 重置组件状态
    isInitialized.value = false
    isPlaying.value = false
    isLoading.value = false

    console.log('全局引用清理完成')
  } catch (error) {
    console.error('清理全局引用时出错:', error)
  }
}

// 添加全局兜底清理措施
const ensurePluginDestroy = () => {
  try {
    if (oWebControl) {
      // 首先隐藏窗口 - 防止窗口滞留问题
      oWebControl.JS_HideWnd();

      // 添加短暂延迟确保隐藏命令被执行
      setTimeout(() => {
        try {
          // 断开服务连接
          oWebControl.JS_Disconnect().then(() => {
            try {
              // 最后销毁窗口
              oWebControl.JS_DestroyWnd().then(() => {
                console.log('插件资源完全释放');
                oWebControl = null;
                window.oWebControl = null;
              }).catch(err => {
                console.error('销毁窗口失败:', err);
                oWebControl = null;
                window.oWebControl = null;
              });
            } catch (innerError) {
              console.error('销毁窗口过程异常:', innerError);
              oWebControl = null;
              window.oWebControl = null;
            }
          }).catch(err => {
            console.error('断开服务连接失败:', err);
            // 即使断开失败也尝试销毁
            try {
              oWebControl.JS_DestroyWnd();
            } catch (finalError) {
              console.error('最终尝试销毁失败', finalError);
            }
            oWebControl = null;
            window.oWebControl = null;
          });
        } catch (error) {
          console.error('断开连接过程异常:', error);
          oWebControl = null;
          window.oWebControl = null;
        }
      }, 100); // 短暂延迟确保隐藏窗口命令执行
    }
  } catch (outerError) {
    console.error('资源释放保障机制异常:', outerError);
    oWebControl = null;
    window.oWebControl = null;
  }
}

// 移除全局插件实例
const unregisterPluginInstance = () => {
  if (window._hikvisionWebControls && oWebControl) {
    const index = window._hikvisionWebControls.indexOf(oWebControl);
    if (index > -1) {
      window._hikvisionWebControls.splice(index, 1);
    }
  }
}

// 修改forceCleanup函数，简化逻辑
const forceCleanup = () => {
  isInitialized.value = false
  isPlaying.value = false

  return new Promise(async (resolve) => {
    try {
      // 仅使用组件实例，不做复杂查找
      if (oWebControl) {
        try {
          // 停止预览
          await oWebControl.JS_RequestInterface({
            funcName: "stopAllPreview"
          }).catch(() => {});

          // 执行基本的资源释放
          if (typeof oWebControl.JS_HideWnd === 'function') {
            await oWebControl.JS_HideWnd().catch(() => {});
          }

          if (typeof oWebControl.JS_Disconnect === 'function') {
            await oWebControl.JS_Disconnect().catch(() => {});
          }

          if (typeof oWebControl.JS_DestroyWnd === 'function') {
            await oWebControl.JS_DestroyWnd().catch(() => {});
          }

          // 清理引用
          oWebControl = null;
          window.oWebControl = null;
        } catch (e) {
          console.error('清理视频资源失败:', e);
        }
      }

      // 保留全局实例注册机制
      if (window._hikvisionWebControls) {
        window._hikvisionWebControls = [];
      }

      resolve();
    } catch (error) {
      console.error('强制清理过程中发生错误:', error);
      resolve();
    }
  });
}

// ResizeObserver 监听播放器容器宽度变化，自动 resize
let resizeObserver = null

onMounted(() => {
  console.log('HikvisionPlayer onMounted')

  // 设置全局兜底清理措施
  setupGlobalCleanup();

  // 监听页面卸载事件，确保资源释放
  window.addEventListener('beforeunload', handleBeforeUnload);

  //从后台加载isc配置
  configApi.iscConfig()
    .then((res) => {
      console.log('从后台获取ISC配置成功')
      // 更新hikConfig配置
      if (res) {
        // 合并配置，使用后台配置覆盖默认配置
        if (res.appkey) hikConfig.appkey = res.appkey
        if (res.ip) hikConfig.ip = res.ip
        if (res.port) hikConfig.port = parseInt(res.port) || hikConfig.port
        if (res.secret) hikConfig.secret = res.secret
        // 可能的附加配置
        if (res.enableHTTPS !== undefined) hikConfig.enableHTTPS = parseInt(res.enableHTTPS)
        if (res.language) hikConfig.language = res.language
        if (res.toolBarButtonIDs) hikConfig.toolBarButtonIDs = res.toolBarButtonIDs
        if (res.snapDir) hikConfig.snapDir = res.snapDir
        if (res.videoDir) hikConfig.videoDir = res.videoDir
      }
      // 初始化插件
      log('info', '视频播放器初始化...')
      initPlugin()
    })
    .catch(error => {
      console.error('获取ISC配置失败:', error)
      log('error', '获取ISC配置失败')
      // 即使获取配置失败，也使用默认配置初始化插件
      log('warning', '使用默认配置初始化播放器')
      initPlugin()
    })

  window.addEventListener('resize', resizeWindow)
  window.addEventListener('scroll', resizeWindow)

  // 监听 .video-view 容器宽度变化
  const container = document.querySelector('.video-view')
  if (container && window.ResizeObserver) {
    resizeObserver = new ResizeObserver(() => {
      resizeWindow()
    })
    resizeObserver.observe(container)
  }
})

// 导出方法给父组件调用
defineExpose({
  initPlugin,
  startPreview,
  stopAllPreview,
  uninitPlugin,
  forceCleanup,
  isInitialized,
  setLayout,
  // 检查插件状态的方法
  checkPluginStatus() {
    return {
      isInitialized: isInitialized.value,
      isPlaying: isPlaying.value,
      isLoading: isLoading.value,
      currentLayout: currentLayout.value,
      hasPluginObject: !!oWebControl
    }
  }
})
</script>

<style scoped>
.video-player-container {
  height: 100%;
  width: 100%;
  padding: 0;
  flex: 1;
  display: flex;
  position: relative;
  z-index: 5;
}

.video-card {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  border-radius: 0;
  overflow: hidden;
  box-shadow: none;
  flex: 1;
}

.player-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

.title {
  font-size: 16px;
  font-weight: 600;
  margin-left: 8px;
  color: #1e293b;
}

.area-tag {
  font-size: 12px;
  color: #666;
  background-color: #f5f5f5;
  padding: 2px 8px;
  border-radius: 4px;
}

:deep(.ant-card-body) {
  flex: 1;
  padding: 8px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.main-content {
  display: flex;
  flex: 1;
  overflow: hidden;
  position: relative;
  width: 100%;
}

.video-view {
  flex: 1;
  width: 100%;
  position: relative;
  z-index: 5;
}

.player-window-container {
  position: relative;
  height: 100%;
  min-height: 300px;
  background-color: #000;
  border-radius: 8px;
  overflow: hidden;
}

.player-window {
  width: 100%;
  height: 100%;
  position: relative;
}

.player-placeholder {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: #fff;
  background: linear-gradient(135deg, #1f2937, #111827);
  z-index: 7;
}

.player-placeholder.waiting {
  background: linear-gradient(135deg, #0f172a, #1e293b);
}

.player-placeholder :deep(svg) {
  font-size: 48px;
  opacity: 0.7;
  margin-bottom: 16px;
}

.player-placeholder p {
  font-size: 16px;
  opacity: 0.7;
}

.player-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 8;
}
</style>

<style>
/* 全局样式，确保下拉菜单在最顶层 */
.hikvision-dropdown-menu {
  z-index: 9999 !important;
}
</style>
