<template>
  <div class="camera-tree">
    <div class="camera-tree-header">
      <div class="title">摄像头列表</div>
      <a-input-search v-model:value="searchQuery" placeholder="搜索摄像头..." size="small" @search="handleSearch" />
    </div>

    <a-spin :spinning="loading" tip="加载中...">
      <div class="tree-container">
        <a-empty v-if="filteredCameraList.length === 0" description="暂无摄像头" />
        <a-directory-tree v-else :tree-data="filteredCameraList" :fieldNames="{
          title: 'title',
          key: 'key',
          children: 'children'
        }" :default-expanded-keys="defaultExpandedKeys" :selected-keys="selectedKeys" :auto-expand-parent="true"
          :expand-on-click="true" @select="handleSelectCamera" :block-node="true" :selectable="true">

        </a-directory-tree>
      </div>
    </a-spin>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { FolderOutlined, VideoCameraOutlined, DownOutlined, RightOutlined } from '@ant-design/icons-vue'
import deviceApi from '@/api/biz/deviceApi'

// 接收父组件传递的摄像头列表数据
const props = defineProps({
  cameraListData: {
    type: Array,
    default: () => []
  }
})

// 定义数据
const cameraList = ref([])
const filteredCameraList = computed(() => {
  if (!searchQuery.value) return cameraList.value

  // 递归搜索实现 - 修复搜索逻辑
  const search = (nodes, term) => {
    if (!nodes || !Array.isArray(nodes)) return []

    return nodes.reduce((result, node) => {
      if (!node) return result

      const matchTitle = node.title && node.title.toLowerCase().includes(term.toLowerCase())

      if (node.children && node.children.length > 0) {
        const matchedChildren = search(node.children, term)

        if (matchedChildren.length > 0) {
          result.push({
            ...node,
            children: matchedChildren
          })
          return result
        }
      }

      if (matchTitle) {
        result.push(node)
      }

      return result
    }, [])
  }

  return search(JSON.parse(JSON.stringify(cameraList.value)), searchQuery.value)
})

const defaultExpandedKeys = ref([])
const selectedKeys = ref([])
const loading = ref(false)
const searchQuery = ref('')

// 监听父组件传递的摄像头列表数据
watch(() => props.cameraListData, (newData) => {
  console.log('CameraTree接收到数据:', newData)
  if (newData && newData.length > 0) {
    // 使用处理方法转换为树形结构
    const processedData = processParentCameraData(newData)
    console.log('处理后的树形结构:', processedData)
    if (processedData && processedData.length > 0) {
      cameraList.value = processedData
      // 默认展开所有区域
      defaultExpandedKeys.value = ['all-cameras']
      // 收集所有区域的key用于展开
      const areaKeys = processedData[0]?.children
        ?.filter(node => !node.isLeaf)
        ?.map(node => node.key) || []
      if (areaKeys.length > 0) {
        defaultExpandedKeys.value = defaultExpandedKeys.value.concat(areaKeys)
      }
    }
  } else {
    console.warn('CameraTree: 接收到空数据或无效数据')
  }
}, { deep: true, immediate: true })

// 处理父组件传递的摄像头数据
const processParentCameraData = (cameras) => {
  if (!cameras || cameras.length === 0) return []

  // 创建根节点
  const rootNode = {
    key: 'all-cameras',
    title: '所有摄像头',
    children: []
  }

  // 创建区域映射，用于快速找到区域节点
  const areaMap = new Map()

  // 收集未分类的摄像头
  const unclassifiedCameras = []

  // 检查是否有任何摄像头有区域信息
  const hasAnyAreaInfo = cameras.some(cam => cam.areaId && cam.areaName)

  // 遍历摄像头，按区域组织
  cameras.forEach(camera => {
    if (!camera) {
      console.warn('无效的摄像头数据:', camera)
      return
    }

    // 创建摄像头节点
    const cameraNode = {
      key: camera.key || camera.id || `camera-${Math.random().toString(36).substr(2, 9)}`,
      title: camera.title || camera.name || '未命名摄像头',
      cameraIndexCode: camera.cameraIndexCode || '',
      isLeaf: true,
      ip: camera.ip || '',
      port: camera.port || '',
      username: camera.username || '',
      password: camera.password || '',
      rawData: camera
    }

    // 如果有区域信息，则放到对应区域下
    if (hasAnyAreaInfo && camera.areaId && camera.areaName) {
      // 检查区域节点是否已创建
      if (!areaMap.has(camera.areaId)) {
        // 创建区域节点
        const areaNode = {
          key: `area-${camera.areaId}`,
          title: camera.areaName,
          children: []
        }
        // 保存到映射和根节点的子节点中
        areaMap.set(camera.areaId, areaNode)
        rootNode.children.push(areaNode)
      }

      // 将摄像头添加到对应区域的子节点中
      areaMap.get(camera.areaId).children.push(cameraNode)
    } else {
      // 无区域信息或者没有任何摄像头有区域信息，收集到未分类组
      unclassifiedCameras.push(cameraNode)
    }
  })

  // 如果没有任何摄像头有区域信息，则所有摄像头直接作为根节点的子节点
  if (!hasAnyAreaInfo) {
    rootNode.children = unclassifiedCameras
    return [rootNode]
  }

  // 如果有未分类的摄像头，创建未分类区域节点
  if (unclassifiedCameras.length > 0) {
    const unclassifiedNode = {
      key: 'unclassified',
      title: '未分类',
      children: unclassifiedCameras
    }
    rootNode.children.push(unclassifiedNode)
  }

  // 按区域名称排序区域节点
  rootNode.children.sort((a, b) => {
    if (a.key === 'unclassified') return 1 // 未分类始终放在最后
    if (b.key === 'unclassified') return -1
    return a.title.localeCompare(b.title, 'zh-CN')
  })

  console.log('生成的树形结构结果:', [rootNode])

  // 返回树形结构
  return [rootNode]
}

// 获取摄像头列表 - 保留此方法作为备用
const getCameraList = async () => {
  // 如果父组件已经提供了数据，则无需再次获取
  if (props.cameraListData && props.cameraListData.length > 0) {
    return
  }

  loading.value = true
  try {
    const res = await deviceApi.getIscList()
    console.log('摄像头数据:', res)

    if (res && Array.isArray(res)) {
      // 使用新的处理方法组织树形结构
      const treeData = processParentCameraData(res)
      console.log('树形结构数据:', treeData)

      // 设置数据到响应式变量
      cameraList.value = treeData
      defaultExpandedKeys.value = ['all-cameras']
    }
  } catch (error) {
    console.error('获取摄像头列表失败，使用测试数据:', error)
  } finally {
    loading.value = false
  }
}

// 搜索摄像头
const handleSearch = (value) => {
  searchQuery.value = value
}

// 选择摄像头
const handleSelectCamera = (keys, info) => {
  if (keys.length > 0 && info && info.node) {
    // 只有叶子节点（摄像头）才能被选中
    if (info.node.isLeaf) {
      selectedKeys.value = keys

      // 发出事件通知父组件播放该摄像头
      emit('select-camera', {
        ...info.node,
        label: info.node.title // 保持与之前组件属性名一致
      })
    }
  }
}

// 定义emit
const emit = defineEmits(['select-camera'])

// 加载摄像头列表
onMounted(() => {
  // 仅在父组件未提供数据时调用自己的获取方法
  if (!props.cameraListData || props.cameraListData.length === 0) {
    getCameraList()
  }
})

// 暴露方法供父组件调用
defineExpose({
  getCameraList,
  setSelectedCamera(camera) {
    if (camera && camera.key) {
      selectedKeys.value = [camera.key]
    }
  }
})
</script>

<style scoped>
.camera-tree {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 16px;
  background-color: #fff;
  position: relative;
  z-index: 10;
}

.camera-tree-header {
  margin-bottom: 16px;
}

.title {
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 12px;
}

.tree-container {
  flex: 1;
  overflow: auto;
  padding: 8px 0;
  background-color: #fff;
  position: relative;
  z-index: 11;
}

:deep(.ant-tree) {
  background-color: #fff;
  z-index: 12;
  position: relative;
}

:deep(.ant-tree-node-content-wrapper) {
  transition: all 0.3s;
  display: inline-flex !important;
  align-items: center;
  width: 100% !important;
}

:deep(.ant-tree-title) {
  color: #333;
  font-size: 14px;
}

:deep(.ant-tree-node-content-wrapper:hover) {
  background-color: #f0f7ff;
}

:deep(.ant-tree-node-selected) {
  
  width: 100% !important;
}

:deep(.ant-tree-directory) {
  background-color: #fff;
}


:deep(.ant-tree-directory .ant-tree-treenode:hover::before) {
  background-color: #1890ff;
}

:deep(.ant-tree-directory .ant-tree-treenode-selected .ant-tree-node-content-wrapper) {
  color: #1890ff;
}
</style>
