import { useHikvisionStore } from '@/store/hikvision'

export function callPluginInterface(funcName, argumentObj, addLog) {
  const hikStore = useHikvisionStore()
  const oWebControl = hikStore.oWebControl
  const argStr = argumentObj ? JSON.stringify(argumentObj) : undefined
  addLog && addLog('debug', `调用插件接口: ${funcName}, 参数: ${argStr}`)
  return oWebControl.JS_RequestInterface({
    funcName,
    ...(argStr ? { argument: argStr } : {})
  }).then(res => {
    addLog && addLog('debug', `插件接口${funcName}返回: ${JSON.stringify(res)}`)
    return res
  }).catch(err => {
    addLog && addLog('error', `插件接口${funcName}异常: ${JSON.stringify(err)}, 参数: ${argStr}`)
    throw err
  })
}

export function setEncrypt(value) {
  const hikStore = useHikvisionStore()
  const pubKey = hikStore.pubKey
  if (!pubKey) return value
  const encrypt = new window.JSEncrypt()
  encrypt.setPublicKey(pubKey)
  return encrypt.encrypt(value)
}

export function getPubKey({ addLog, retryCount, maxRetry, retryDelay, initPlugin, onSuccess }) {
  const hikStore = useHikvisionStore()
  const oWebControl = hikStore.oWebControl
  addLog && addLog('debug', 'getPubKey调用')
  if (!oWebControl) return
  oWebControl.JS_RequestInterface({
    funcName: 'getRSAPubKey',
    argument: JSON.stringify({ keyLength: 1024 })
  }).then(function (oData) {
    addLog && addLog('debug', 'getPubKey then, oData=' + JSON.stringify(oData))
    if (oData.responseMsg && oData.responseMsg.data) {
      let pubKey = ''
      if (typeof oData.responseMsg.data === 'string') {
        try {
          pubKey = JSON.parse(oData.responseMsg.data).rsaPubKey
        } catch (e) {
          pubKey = oData.responseMsg.data
        }
      } else if (oData.responseMsg.data.rsaPubKey) {
        pubKey = oData.responseMsg.data.rsaPubKey
      }
      hikStore.pubKey = pubKey
      addLog && addLog('success', '获取RSA公钥成功')
      onSuccess && onSuccess()
    } else {
      addLog && addLog('error', '获取RSA公钥失败')
      if (retryCount.value < maxRetry) {
        retryCount.value++
        addLog && addLog('warning', `获取RSA公钥失败，正在第${retryCount.value}次重试...`)
        setTimeout(() => {
          initPlugin && initPlugin()
        }, retryDelay)
      } else {
        addLog && addLog('error', '插件初始化失败，请点击重试')
      }
    }
  }).catch(function (e) {
    addLog && addLog('error', '获取RSA公钥异常: ' + JSON.stringify(e))
    if (retryCount.value < maxRetry) {
      retryCount.value++
      addLog && addLog('warning', `获取RSA公钥异常，正在第${retryCount.value}次重试...`)
      setTimeout(() => {
        initPlugin && initPlugin()
      }, retryDelay)
    }
  })
}
