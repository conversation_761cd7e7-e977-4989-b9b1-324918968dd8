<template>
  <a-modal
    :title="formState.id ? '编辑小车信息' : '新增小车信息'"
    :width="900"
    :visible="visible"
    :confirm-loading="confirmLoading"
    :maskClosable="false"
    @cancel="onClose"
  >
    <a-form
      ref="formRef"
      :model="formState"
      :rules="formRules"
      :label-col="{ span: 8 }"
      :wrapper-col="{ span: 16 }"
    >
      <!-- 基本信息 -->
      <a-card class="form-card" :bordered="false">
        <template #title>
          <car-outlined />
          <span class="card-title">基本信息</span>
        </template>
        <div class="card-content">
          <a-row :gutter="16" class="form-row">
            <a-col :span="12">
              <a-form-item label="车牌号码" name="plateNumber">
                <a-input v-model:value="formState.plateNumber" placeholder="请输入车牌号码" allowClear />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="车牌颜色" name="plateColor">
                <a-select v-model:value="formState.plateColor" placeholder="请选择车牌颜色" :options="plateColorOptions" allowClear />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="16" class="form-row">
            <a-col :span="12">
              <a-form-item label="车主姓名" name="ownerName">
                <a-input v-model:value="formState.ownerName" placeholder="请输入车主姓名" allowClear />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item
                label="车主电话"
                name="ownerPhone"
                :rules="[{ pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }]"
              >
                <a-input v-model:value="formState.ownerPhone" placeholder="请输入车主电话" allowClear />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="16" class="form-row">
            <a-col :span="12">
              <a-form-item
                label="车主身份证号"
                name="ownerIdCard"
                :rules="[{ pattern: /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/, message: '请输入正确的身份证号码', trigger: 'blur' }]"
              >
                <a-input v-model:value="formState.ownerIdCard" placeholder="请输入车主身份证号" allowClear />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="所属部门" name="orgId">
                <a-input-group compact>
                  <a-input
                    v-model:value="formState.orgName"
                    placeholder="请选择所属部门"
                    style="width: calc(100% - 40px)"
                    readOnly
                  />
                  <a-button type="primary" @click="showOrgSelect" style="width: 40px">
                    <template #icon><search-outlined /></template>
                  </a-button>
                </a-input-group>
              </a-form-item>
            </a-col>
          </a-row>
        </div>
      </a-card>

      <!-- 证件照片 -->
      <a-card class="form-card" :bordered="false">
        <template #title>
          <file-image-outlined />
          <span class="card-title">证件照片</span>
        </template>
        <div class="card-content">
          <a-row :gutter="16" class="form-row">
            <a-col :span="12">
              <a-form-item label="驾驶证照片" class="image-form-item">
                <div class="image-container">
                  <div class="image-display-area">
                    <template v-if="formState.driverLicenseImgUrl">
                      <div class="image-wrapper">
                        <a-image
                          :src="formState.driverLicenseImgUrl"
                          class="displayed-image"
                          :preview="{
                            src: formState.driverLicenseImgUrl
                          }"
                        />
                        <div class="custom-overlay">
                          <div class="image-hover-actions">
                            <eye-outlined class="hover-icon preview-icon" @click.stop="(e) => handlePreview(e, formState.driverLicenseImgUrl)" />
                            <delete-outlined class="hover-icon delete-icon" @click.stop="(e) => handleImageDelete(e, 'driverLicense')" />
                          </div>
                        </div>
                      </div>
                    </template>
                    <div v-else class="empty-image-placeholder">
                      <file-image-outlined />
                      <div class="placeholder-text">暂无驾驶证照片</div>
                    </div>
                  </div>
                  <div class="image-actions">
                    <a-upload
                      v-if="!formState.driverLicenseImgUrl"
                      :showUploadList="false"
                      :customRequest="(options) => handleImageUpload(options, 'driverLicense')"
                    >
                      <a-button type="link" size="small">
                        <upload-outlined /> 上传
                      </a-button>
                    </a-upload>
                  </div>
                  <a-input v-model:value="formState.driverLicenseImgUrl" class="hidden-input" />
                </div>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="行驶证照片" class="image-form-item">
                <div class="image-container">
                  <div class="image-display-area">
                    <template v-if="formState.vehicleLicenseImgUrl">
                      <div class="image-wrapper">
                        <a-image
                          :src="formState.vehicleLicenseImgUrl"
                          class="displayed-image"
                          :preview="{
                            src: formState.vehicleLicenseImgUrl
                          }"
                        />
                        <div class="custom-overlay">
                          <div class="image-hover-actions">
                            <eye-outlined class="hover-icon preview-icon" @click.stop="(e) => handlePreview(e, formState.vehicleLicenseImgUrl)" />
                            <delete-outlined class="hover-icon delete-icon" @click.stop="(e) => handleImageDelete(e, 'vehicleLicense')" />
                          </div>
                        </div>
                      </div>
                    </template>
                    <div v-else class="empty-image-placeholder">
                      <file-image-outlined />
                      <div class="placeholder-text">暂无行驶证照片</div>
                    </div>
                  </div>
                  <div class="image-actions">
                    <a-upload
                      v-if="!formState.vehicleLicenseImgUrl"
                      :showUploadList="false"
                      :customRequest="(options) => handleImageUpload(options, 'vehicleLicense')"
                    >
                      <a-button type="link" size="small">
                        <upload-outlined /> 上传
                      </a-button>
                    </a-upload>
                  </div>
                  <a-input v-model:value="formState.vehicleLicenseImgUrl" class="hidden-input" />
                </div>
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="16" class="form-row">
            <a-col :span="12">
              <a-form-item label="车身照片" class="image-form-item">
                <div class="image-container">
                  <div class="image-display-area">
                    <template v-if="formState.carBodyImgUrl">
                      <div class="image-wrapper">
                        <a-image
                          :src="formState.carBodyImgUrl"
                          class="displayed-image"
                          :preview="{
                            src: formState.carBodyImgUrl
                          }"
                        />
                        <div class="custom-overlay">
                          <div class="image-hover-actions">
                            <eye-outlined class="hover-icon preview-icon" @click.stop="(e) => handlePreview(e, formState.carBodyImgUrl)" />
                            <delete-outlined class="hover-icon delete-icon" @click.stop="(e) => handleImageDelete(e, 'carBody')" />
                          </div>
                        </div>
                      </div>
                    </template>
                    <div v-else class="empty-image-placeholder">
                      <file-image-outlined />
                      <div class="placeholder-text">暂无车身照片</div>
                    </div>
                  </div>
                  <div class="image-actions">
                    <a-upload
                      v-if="!formState.carBodyImgUrl"
                      :showUploadList="false"
                      :customRequest="(options) => handleImageUpload(options, 'carBody')"
                    >
                      <a-button type="link" size="small">
                        <upload-outlined /> 上传
                      </a-button>
                    </a-upload>
                  </div>
                  <a-input v-model:value="formState.carBodyImgUrl" class="hidden-input" />
                </div>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="其他照片" class="image-form-item">
                <div class="image-container">
                  <div class="image-display-area">
                    <template v-if="formState.otherImgUrl">
                      <div class="image-wrapper">
                        <a-image
                          :src="formState.otherImgUrl"
                          class="displayed-image"
                          :preview="{
                            src: formState.otherImgUrl
                          }"
                        />
                        <div class="custom-overlay">
                          <div class="image-hover-actions">
                            <eye-outlined class="hover-icon preview-icon" @click.stop="(e) => handlePreview(e, formState.otherImgUrl)" />
                            <delete-outlined class="hover-icon delete-icon" @click.stop="(e) => handleImageDelete(e, 'other')" />
                          </div>
                        </div>
                      </div>
                    </template>
                    <div v-else class="empty-image-placeholder">
                      <file-image-outlined />
                      <div class="placeholder-text">暂无其他照片</div>
                    </div>
                  </div>
                  <div class="image-actions">
                    <a-upload
                      v-if="!formState.otherImgUrl"
                      :showUploadList="false"
                      :customRequest="(options) => handleImageUpload(options, 'other')"
                    >
                      <a-button type="link" size="small">
                        <upload-outlined /> 上传
                      </a-button>
                    </a-upload>
                  </div>
                  <a-input v-model:value="formState.otherImgUrl" class="hidden-input" />
                </div>
              </a-form-item>
            </a-col>
          </a-row>
        </div>
      </a-card>

      <!-- 其他信息 -->
      <a-card class="form-card" :bordered="false">
        <template #title>
          <info-circle-outlined />
          <span class="card-title">其他信息</span>
        </template>
        <div class="card-content">
          <a-row :gutter="16" class="form-row">
            <a-col :span="24">
              <a-form-item label="有效期" name="validityPeriodTimeRange" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }">
                <a-range-picker
                  :value="validityPeriodRange"
                  show-time
                  format="YYYY-MM-DD HH:mm:ss"
                  :placeholder="['开始时间', '结束时间']"
                  style="width: 100%"
                  @change="handleValidityPeriodChange"
                />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="16" class="form-row">
            <a-col :span="12">
              <a-form-item label="状态" name="status">
                <a-radio-group v-model:value="formState.status" @change="handleStatusChange">
                  <a-radio value="1">
                    <span class="status-enabled">启用</span>
                  </a-radio>
                  <a-radio value="0">
                    <span class="status-disabled">禁用</span>
                  </a-radio>
                </a-radio-group>
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="16" v-if="formState.status === '0'" class="form-row">
            <a-col :span="24">
              <a-form-item
                label="禁用原因"
                name="disableReason"
                :label-col="{ span: 4 }"
                :wrapper-col="{ span: 20 }"
                :rules="[{ required: true, message: '请输入禁用原因', trigger: 'blur' }]"
              >
                <a-textarea
                  v-model:value="formState.disableReason"
                  placeholder="请输入禁用原因"
                  :rows="2"
                  class="disable-reason-textarea"
                />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="16" class="form-row">
            <a-col :span="24">
              <a-form-item label="备注" name="remark" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }">
                <a-textarea v-model:value="formState.remark" placeholder="请输入备注" :rows="3" />
              </a-form-item>
            </a-col>
          </a-row>
        </div>
      </a-card>
    </a-form>

    <!-- 添加组织选择组件 -->
    <OrgSelect
      v-model:visible="orgSelectVisible"
      title="选择所属部门"
      :value="formState.orgId ? [formState.orgId] : []"
      :multiple="false"
      @confirm="handleOrgSelect"
    />

    <template #footer>
      <a-button @click="onClose">取消</a-button>
      <a-button type="primary" :loading="confirmLoading" @click="onSubmit">保存</a-button>
    </template>
  </a-modal>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { message, Modal } from 'ant-design-vue'
import {
  SearchOutlined,
  CarOutlined,
  InfoCircleOutlined,
  FileImageOutlined,
  EyeOutlined,
  UploadOutlined,
  DeleteOutlined
} from '@ant-design/icons-vue'
import { carInfoApi } from '@/api/biz/carApi'
import OrgSelect from '../components/OrgSelect.vue'
import tool from '@/utils/tool'
import { validateForm } from '@/utils/formUtils'
import dayjs from 'dayjs'
import fileApi from '@/api/dev/fileApi'

// 定义组件属性
const emit = defineEmits(['successful', 'close'])

// 表单引用
const formRef = ref(null)

// 弹窗控制
const visible = ref(false)
const confirmLoading = ref(false)

// 组织机构选择相关
const orgSelectVisible = ref(false)

// 显示组织机构选择器
const showOrgSelect = () => {
  orgSelectVisible.value = true
}

// 表单状态
const formState = reactive({
  id: '',
  plateNumber: '',
  plateColor: undefined,
  ownerName: '',
  ownerPhone: '',
  ownerIdCard: '',
  orgId: '',
  orgName: '',
  status: '1',
  disableReason: '', // 禁用原因
  remark: '',
  validityPeriodTimeRange: '',
  // 新增照片字段
  driverLicenseImgUrl: '', // 驾驶证照片
  vehicleLicenseImgUrl: '', // 行驶证照片
  carBodyImgUrl: '', // 车身照片
  otherImgUrl: '' // 其他照片
})

// 表单校验规则
const formRules = {
  plateNumber: [{ required: true, message: '请输入车牌号码', trigger: 'blur' }],
  plateColor: [{ required: true, message: '请选择车牌颜色', trigger: 'change' }]
}

// 字典数据
const plateColorOptions = tool.dictList('PLATE_COLOR')

// 有效期日期范围
const validityPeriodRange = ref([])

// 处理有效期变化
const handleValidityPeriodChange = (dates, dateStrings) => {
  if (dates && dates.length === 2) {
    // 直接设置日期范围和表单值
    validityPeriodRange.value = dates
    formState.validityPeriodTimeRange = `${dateStrings[0]} ~ ${dateStrings[1]}`
  } else {
    // 清空日期范围和表单值
    validityPeriodRange.value = []
    formState.validityPeriodTimeRange = ''
  }
}

// 处理状态变更
const handleStatusChange = (e) => {
  const status = e.target.value
  if (status === '1') {
    formState.disableReason = ''
  }
}

// 上传前校验
const beforeUpload = (file) => {
  const isImage = file.type.startsWith('image/')
  if (!isImage) {
    message.error('只能上传图片文件!')
    return false
  }

  const isLt2M = file.size / 1024 / 1024 < 2
  if (!isLt2M) {
    message.error('图片大小不能超过2MB!')
    return false
  }

  return true
}

// 上传图片
const uploadImage = async (file, type) => {
  // 验证文件类型和大小
  const isValidFile = beforeUpload(file)
  if (!isValidFile) {
    return false
  }

  let messageKey = `upload_${type}`
  let imageTitle = ''

  // 根据类型设置提示文字
  switch(type) {
    case 'driverLicense': imageTitle = '驾驶证照片'; break;
    case 'vehicleLicense': imageTitle = '行驶证照片'; break;
    case 'carBody': imageTitle = '车身照片'; break;
    case 'other': imageTitle = '其他照片'; break;
    default: imageTitle = '图片'; break;
  }

  // 显示上传中提示
  message.loading({ content: `${imageTitle}上传中...`, key: messageKey })

  // 准备表单数据
  const uploadFormData = new FormData()
  uploadFormData.append('file', file)

  try {
    // 调用API进行上传
    const url = await fileApi.fileUploadLocalReturnUrl(uploadFormData)
    // 关闭加载提示
    message.success({ content: `${imageTitle}上传成功`, key: messageKey })

    // 返回URL
    return url
  } catch (error) {
    // 显示错误提示
    console.error('上传异常:', error)
    return false
  }
}

// 处理图片上传
const handleImageUpload = async (options, type) => {
  const { file } = options

  const url = await uploadImage(file, type)
  if (url) {
    // 更新对应的图片URL
    formState[`${type}ImgUrl`] = url
  }
}

// 删除图片
const removeImage = (type) => {
  formState[`${type}ImgUrl`] = ''
}

// 带确认的删除图片
const handleImageDelete = (e, type) => {
  e.stopPropagation() // 阻止事件冒泡

  // 显示确认对话框
  let imageTitle = ''

  // 根据类型设置提示文字
  switch(type) {
    case 'driverLicense': imageTitle = '驾驶证照片'; break;
    case 'vehicleLicense': imageTitle = '行驶证照片'; break;
    case 'carBody': imageTitle = '车身照片'; break;
    case 'other': imageTitle = '其他照片'; break;
    default: imageTitle = '图片'; break;
  }

  Modal.confirm({
    title: `确认删除`,
    content: `确定要删除${imageTitle}吗？`,
    okText: '确认',
    cancelText: '取消',
    onOk: () => {
      removeImage(type)
      message.success(`已删除${imageTitle}`)
    }
  })
}

// 手动预览图片
const handlePreview = (e, url) => {
  e.stopPropagation()
  // 查找对应的a-image组件，并触发其预览
  const image = e.target.closest('.image-wrapper').querySelector('.ant-image')
  if (image) {
    // 触发a-image的点击事件
    image.click()
  }
}

// 打开弹窗
const onOpen = (record) => {
  visible.value = true

  // 重置表单
  resetForm()

  // 如果是编辑模式，加载数据
  if (record) {
    // 获取详情
    carInfoApi.carInfoDetail({ id: record.id }).then(res => {
      Object.assign(formState, res)

      // 处理有效期
      if (formState.validityPeriodTimeRange) {
        const [startStr, endStr] = formState.validityPeriodTimeRange.split(' ~ ')
        validityPeriodRange.value = [dayjs(startStr), dayjs(endStr)]
      }
    })
  }
}

// 关闭弹窗
const onClose = () => {
  visible.value = false
  emit('close')
}

// 重置表单
const resetForm = () => {
  formState.id = ''
  formState.plateNumber = ''
  formState.plateColor = undefined
  formState.ownerName = ''
  formState.ownerPhone = ''
  formState.ownerIdCard = ''
  formState.orgId = ''
  formState.orgName = ''
  formState.status = '1'
  formState.disableReason = ''
  formState.remark = ''
  formState.validityPeriodTimeRange = ''
  // 重置照片字段
  formState.driverLicenseImgUrl = ''
  formState.vehicleLicenseImgUrl = ''
  formState.carBodyImgUrl = ''
  formState.otherImgUrl = ''
  validityPeriodRange.value = []

  // 重置表单校验
  if (formRef.value) {
    formRef.value.resetFields()
  }
}

// 处理组织机构选择结果
const handleOrgSelect = (keys, rows) => {
  // 判断是单选还是多选模式
  if (Array.isArray(keys) && keys.length > 0) {
    // 多选模式
    formState.orgId = keys[0]
    formState.orgName = rows[0].name
  } else if (keys) {
    // 单选模式
    formState.orgId = keys
    formState.orgName = rows.name
  } else {
    // 清空选择
    formState.orgId = ''
    formState.orgName = ''
  }
}

// 提交表单
const onSubmit = () => {
  validateForm(formRef.value, () => {
    confirmLoading.value = true

    // 根据是否有ID判断是新增还是编辑
    const api = formState.id ? carInfoApi.carInfoEdit : carInfoApi.carInfoAdd

    api(formState).then(() => {
      message.success(`${formState.id ? '编辑' : '新增'}成功`)
      visible.value = false
      emit('successful')
    }).finally(() => {
      confirmLoading.value = false
    })
  })
}

// 暴露方法
defineExpose({
  onOpen
})
</script>

<style scoped>
/* 卡片样式 */
.form-card {
  margin-bottom: 16px;
  border-radius: 8px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  transition: all 0.3s;
}

.form-card:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
}

/* 卡片标题样式 */
.card-title {
  font-weight: 500;
  margin-left: 8px;
  font-size: 15px;
  color: #1890ff;
}

/* 卡片内容区域样式 */
.card-content {
  padding: 8px 0;
}

/* 表单项间距 */
.ant-form-item {
  margin-bottom: 16px;
}

/* 带验证提示的表单项间距 */
.ant-form-item-with-help {
  margin-bottom: 0;
}

/* 行间距 */
.ant-row {
  margin-bottom: 0;
}

/* 表单行间距 */
.form-row {
  margin-bottom: 16px;
}

.form-row:last-child {
  margin-bottom: 0;
}

/* 表单标签对齐方式 */
:deep(.ant-form-item-label) {
  text-align: right;
}

/* 卡片标题样式 */
:deep(.ant-card-head) {
  min-height: 48px;
  padding: 0 16px;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.ant-card-head-title) {
  padding: 12px 0;
}

:deep(.ant-card-body) {
  padding: 16px 24px;
}

/* 状态单选按钮样式 */
.status-enabled {
  color: #52c41a;
  font-weight: 500;
}

.status-disabled {
  color: #ff4d4f;
  font-weight: 500;
}

/* 禁用原因文本框样式 */
.disable-reason-textarea {
  border-color: #ff4d4f;
  background-color: #fff1f0;
}

:deep(.disable-reason-textarea:hover) {
  border-color: #ff7875;
}

:deep(.disable-reason-textarea:focus) {
  border-color: #ff7875;
  box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.2);
}

/* 图片上传相关样式 */
.image-form-item :deep(.ant-form-item-label) {
  padding-bottom: 8px;
}

.image-container {
  display: flex;
  flex-direction: column;
}

.image-display-area {
  width: 100%;
  height: 160px;
  background-color: #fafafa;
  border: 1px dashed #d9d9d9;
  border-radius: 4px;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  position: relative;
  transition: all 0.3s;
  cursor: pointer;
}

.image-display-area:hover {
  border-color: #1890ff;
}

.empty-image-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #d9d9d9;
}

.empty-image-placeholder .anticon {
  font-size: 32px;
  margin-bottom: 8px;
}

.placeholder-text {
  font-size: 14px;
  color: #bfbfbf;
}

.displayed-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s;
}

.displayed-image :deep(.ant-image-img) {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-actions {
  display: flex;
  gap: 16px;
  justify-content: center;
  align-items: center;
  margin-top: 8px;
}

.image-actions .ant-btn-link {
  padding: 0;
  height: auto;
}

.image-actions .anticon {
  margin-right: 4px;
}

.hidden-input {
  display: none;
}

/* 图片悬停效果样式 */
.image-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
}

.custom-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  opacity: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: opacity 0.3s;
}

.image-wrapper:hover .custom-overlay {
  opacity: 1;
}

.image-hover-actions {
  display: flex;
  gap: 24px;
}

.hover-icon {
  font-size: 20px;
  color: white;
  cursor: pointer;
  transition: transform 0.2s;
}

.hover-icon:hover {
  transform: scale(1.2);
}

.preview-icon:hover {
  color: #1890ff;
}

.delete-icon:hover {
  color: #ff4d4f;
}
</style>
