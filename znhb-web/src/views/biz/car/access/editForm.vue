<template>
	<a-modal
		title="编辑小车进出记录"
		:width="680"
		:visible="visible"
		:confirm-loading="confirmLoading"
		:maskClosable="false"
		@cancel="onClose"
	>
		<a-form
			ref="formRef"
			:model="formState"
			:rules="formRules"
			:label-col="{ span: 6 }"
			:wrapper-col="{ span: 16 }"
		>
			<!-- 基本信息 -->
			<a-divider>基本信息</a-divider>
			<a-form-item label="车牌号码" name="plateNumber">
				<a-input v-model:value="formState.plateNumber" placeholder="请输入车牌号码" allowClear/>
			</a-form-item>
			<a-form-item label="状态" name="status">
				<a-radio-group v-model:value="formState.status">
					<a-radio value="0">未出厂</a-radio>
					<a-radio value="1">已出厂</a-radio>
				</a-radio-group>
			</a-form-item>

			<!-- 进厂信息 -->
			<a-divider>进厂信息</a-divider>
			<a-form-item label="进厂时间" name="entryTime">
				<a-date-picker
					v-model:value="formState.entryTime"
					show-time
					format="YYYY-MM-DD HH:mm:ss"
					value-format="YYYY-MM-DD HH:mm:ss"
					style="width: 100%"
				/>
			</a-form-item>
			<a-form-item label="进厂道闸" name="entryGateId">
				<a-select v-model:value="formState.entryGateId" placeholder="请选择进厂道闸"
						  :options="barrierGateOptions" allowClear show-search/>
			</a-form-item>
			<a-form-item label="进厂抓拍图片" name="entryImageUrl">
				<div class="image-container">
					<div class="image-display-area" @click="handleImageClick('entryImageUrl')">
						<template v-if="formState.entryImageUrl">
							<div class="image-wrapper">
								<a-image
									:src="formState.entryImageUrl"
									class="displayed-image"
									:preview="{ src: formState.entryImageUrl }"
								/>
								<div class="custom-overlay">
									<div class="image-hover-actions">
										<eye-outlined class="hover-icon preview-icon" @click.stop="(e) => handlePreview(e, formState.entryImageUrl)" />
										<delete-outlined class="hover-icon delete-icon" @click.stop="(e) => handleImageDelete(e, 'entryImageUrl')" />
									</div>
								</div>
							</div>
						</template>
						<div v-else class="empty-image-placeholder">
							<upload-outlined />
							<span class="placeholder-text">点击上传进厂抓拍照片</span>
						</div>
					</div>
				</div>
			</a-form-item>

			<!-- 出厂信息 -->
			<a-divider>出厂信息</a-divider>
			<a-form-item label="出厂时间" name="exitTime">
				<a-date-picker
					v-model:value="formState.exitTime"
					show-time
					format="YYYY-MM-DD HH:mm:ss"
					value-format="YYYY-MM-DD HH:mm:ss"
					style="width: 100%"
				/>
			</a-form-item>
			<a-form-item label="出厂道闸" name="exitGateId">
				<a-select v-model:value="formState.exitGateId" placeholder="请选择出厂道闸"
						  :options="barrierGateOptions" allowClear show-search/>
			</a-form-item>
			<a-form-item label="出厂抓拍图片" name="exitImageUrl">
				<div class="image-container">
					<div class="image-display-area" @click="handleImageClick('exitImageUrl')">
						<template v-if="formState.exitImageUrl">
							<div class="image-wrapper">
								<a-image
									:src="formState.exitImageUrl"
									class="displayed-image"
									:preview="{ src: formState.exitImageUrl }"
								/>
								<div class="custom-overlay">
									<div class="image-hover-actions">
										<eye-outlined class="hover-icon preview-icon" @click.stop="(e) => handlePreview(e, formState.exitImageUrl)" />
										<delete-outlined class="hover-icon delete-icon" @click.stop="(e) => handleImageDelete(e, 'exitImageUrl')" />
									</div>
								</div>
							</div>
						</template>
						<div v-else class="empty-image-placeholder">
							<upload-outlined />
							<span class="placeholder-text">点击上传出厂抓拍照片</span>
						</div>
					</div>
				</div>
			</a-form-item>
			<!--备注信息-->
			<a-form-item label="备注" name="remark">
				<a-textarea
					v-model:value="formState.remark"
					placeholder="请输入备注信息"
					:auto-size="{ minRows: 2, maxRows: 5 }"
				/>
			</a-form-item>
		</a-form>
		<template #footer>
			<a-button @click="onClose">取消</a-button>
			<a-button type="primary" :loading="confirmLoading" @click="onSubmit">保存</a-button>
		</template>
	</a-modal>
</template>

<script setup>
import {ref, reactive, watch, onMounted, onUnmounted} from 'vue'
import {message, Modal} from 'ant-design-vue'
import {PlusOutlined, EyeOutlined, DeleteOutlined, UploadOutlined} from '@ant-design/icons-vue'
import {carAccessRecordApi} from '@/api/biz/carApi'
import {validateForm} from '@/utils/formUtils'
import dayjs from 'dayjs'
import fileApi from '@/api/dev/fileApi'
import barrierGateApi from '@/api/biz/barrierGateApi'

// 定义组件属性
const emit = defineEmits(['successful', 'close'])

// 表单引用
const formRef = ref(null)

// 弹窗控制
const visible = ref(false)
const confirmLoading = ref(false)

// 表单状态
const formState = reactive({
	id: '',
	plateNumber: '',
	status: '0',
	entryTime: '',
	entryGateId: undefined,
	entryImageUrl: '',
	exitTime: '',
	exitGateId: undefined,
	exitImageUrl: ''
})

// 表单校验规则
const formRules = {
	plateNumber: [{required: true, message: '请输入车牌号码', trigger: 'blur'}],
	entryTime: [{required: true, message: '请选择进厂时间', trigger: 'change'}],
	entryGateId: [{required: true, message: '请选择进厂道闸', trigger: 'change'}],
	exitTime: [{required: true, message: '请选择出厂时间', trigger: 'change'}],
	exitGateId: [{required: true, message: '请选择出厂道闸', trigger: 'change'}],
}

// 文件上传
const entryFileList = ref([])
const exitFileList = ref([])

// 道闸选项
const barrierGateOptions = ref([])

// 文件上传相关
const fileInput = ref(null)
const currentField = ref('')

// 获取道闸列表
const getBarrierGateList = async () => {
	try {
		const res = await barrierGateApi.barrierGatePage({
			page: 1,
			limit: 200 // 获取足够的数据以确保列出所有道闸
		})

		if (res && res.records) {
			// 转换数据格式为选择器需要的格式
			barrierGateOptions.value = res.records.map(item => ({
				label: item.name,
				value: item.id
			}))
		}
	} catch (error) {
		console.error('获取道闸列表失败:', error)
	}
}

// 监听状态变化 - 已移除自动清空和设置出厂信息的逻辑
// 所有字段现在都可以自由编辑
watch(() => formState.status, () => {
	// 状态变化时不再自动清空或设置出厂信息
})

// 页面加载时获取道闸列表
onMounted(() => {
	getBarrierGateList()
	
	const input = document.createElement('input')
	input.type = 'file'
	input.accept = 'image/*'
	input.style.display = 'none'
	input.addEventListener('change', handleFileSelected)
	document.body.appendChild(input)
	fileInput.value = input
})

// 组件销毁时移除文件输入
onUnmounted(() => {
	if (fileInput.value) {
		fileInput.value.removeEventListener('change', handleFileSelected)
		document.body.removeChild(fileInput.value)
	}
})

// 打开弹窗
const onOpen = (record) => {
	if (!record || !record.id) {
		message.error('记录ID不能为空')
		return
	}

	visible.value = true

	// 重置表单
	resetForm()

	// 获取详情
	carAccessRecordApi.carAccessDetail({id: record.id}).then(res => {
		formState.id = res.id
		formState.plateNumber = res.plateNumber
		formState.status = res.status
		formState.entryTime = res.entryTime ? dayjs(res.entryTime).format('YYYY-MM-DD HH:mm:ss') : ''
		formState.entryGateId = res.entryGateId
		formState.entryImageUrl = res.entryImageUrl
		formState.exitTime = res.exitTime ? dayjs(res.exitTime).format('YYYY-MM-DD HH:mm:ss') : ''
		formState.exitGateId = res.exitGateId
		formState.exitImageUrl = res.exitImageUrl
		formState.remark = res.remark

		// 设置文件列表
		if (res.entryImageUrl) {
			entryFileList.value = [
				{
					uid: '-1',
					name: '进厂图片',
					status: 'done',
					url: res.entryImageUrl
				}
			]
		}

		if (res.exitImageUrl) {
			exitFileList.value = [
				{
					uid: '-1',
					name: '出厂图片',
					status: 'done',
					url: res.exitImageUrl
				}
			]
		}
	})
}

// 关闭弹窗
const onClose = () => {
	visible.value = false
	emit('close')
}

// 重置表单
const resetForm = () => {
	formState.id = ''
	formState.plateNumber = ''
	formState.status = '0'
	formState.entryTime = ''
	formState.entryGateId = undefined
	formState.entryImageUrl = ''
	formState.exitTime = ''
	formState.exitGateId = undefined
	formState.exitImageUrl = ''

	// 重置文件列表
	entryFileList.value = []
	exitFileList.value = []

	// 重置表单校验
	if (formRef.value) {
		formRef.value.resetFields()
	}
}

// 点击图片区域处理
const handleImageClick = (field) => {
	if (formState[field]) {
		// 如果已有图片，不触发上传
		return
	}
	currentField.value = field
	fileInput.value.click()
}

// 文件选择后处理
const handleFileSelected = async (event) => {
	const file = event.target.files[0]
	if (!file) return

	// 验证文件类型
	const isImage = file.type.startsWith('image/')
	if (!isImage) {
		message.error('请选择图片文件')
		fileInput.value.value = null
		return
	}

	// 验证文件大小 (限制为5MB)
	if (file.size > 5 * 1024 * 1024) {
		message.error('图片大小不能超过5MB')
		fileInput.value.value = null
		return
	}

	try {
		const uploadData = new FormData()
		uploadData.append('file', file)

		// 显示上传中提示
		message.loading({content: '上传中...', key: 'uploadImage'})

		// 上传图片
		const result = await fileApi.fileUploadLocalReturnUrl(uploadData)
		if (result) {
			// 更新表单数据
			formState[currentField.value] = result
			
			message.success({content: '上传成功', key: 'uploadImage'})
		} else {
			message.error({content: '上传失败', key: 'uploadImage'})
		}
	} catch (error) {
		console.error('上传图片失败:', error)
		message.error({content: '上传失败', key: 'uploadImage'})
	} finally {
		// 重置文件输入
		fileInput.value.value = null
	}
}

// 带确认的删除图片
const handleImageDelete = (e, field) => {
	e.stopPropagation() // 阻止事件冒泡

	// 显示确认对话框
	Modal.confirm({
		title: '确认删除',
		content: '确定要删除照片吗？',
		okText: '确认',
		cancelText: '取消',
		onOk: () => {
			formState[field] = ''
			message.success('已删除照片')
		}
	})
}

// 手动预览图片
const handlePreview = (e, url) => {
	e.stopPropagation()
	// 查找对应的a-image组件，并触发其预览
	const image = e.target.closest('.image-wrapper').querySelector('.ant-image')
	if (image) {
		// 触发a-image的点击事件
		image.click()
	}
}

// 提交表单
const onSubmit = () => {
	validateForm(formRef.value, () => {
		confirmLoading.value = true

		carAccessRecordApi.carAccessEdit(formState).then(() => {
			message.success('编辑成功')
			visible.value = false
			emit('successful')
		}).finally(() => {
			confirmLoading.value = false
		})
	})
}

// 暴露方法
defineExpose({
	onOpen
})
</script>

<style scoped>
.image-container {
  display: flex;
  flex-direction: column;
  max-width: 100%;
  margin-bottom: 16px;
}

.image-display-area {
  width: 100%;
  height: 160px;
  background-color: #fafafa;
  border: 1px dashed #d9d9d9;
  border-radius: 4px;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  position: relative;
  transition: all 0.3s;
  cursor: pointer;
}

.image-display-area:hover {
  border-color: #1890ff;
}

.empty-image-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #d9d9d9;
}

.empty-image-placeholder .anticon {
  font-size: 32px;
  margin-bottom: 8px;
}

.placeholder-text {
  font-size: 14px;
  color: #bfbfbf;
}

.displayed-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s;
}

.image-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
}

.image-wrapper :deep(.ant-image) {
  width: 100%;
  height: 100%;
  display: block;
}

.image-wrapper :deep(.ant-image-img) {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.custom-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 10;
}

.image-wrapper:hover .custom-overlay {
  opacity: 1;
}

.image-hover-actions {
  display: flex;
  gap: 16px;
}

.hover-icon {
  font-size: 20px;
  color: white;
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.5);
  transition: transform 0.2s, background-color 0.2s;
}

.hover-icon:hover {
  transform: scale(1.2);
  background-color: rgba(0, 0, 0, 0.7);
}

.preview-icon {
  background-color: rgba(24, 144, 255, 0.7);
}

.preview-icon:hover {
  background-color: rgba(24, 144, 255, 0.9);
}

.delete-icon {
  background-color: rgba(255, 77, 79, 0.7);
}

.delete-icon:hover {
  background-color: rgba(255, 77, 79, 0.9);
}
</style>
