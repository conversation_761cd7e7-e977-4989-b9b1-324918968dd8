<template>
	<a-card :bordered="false">
		<a-form ref="searchFormRef" name="advanced_search" :model="searchFormState" class="ant-advanced-search-form">
			<!-- 基本搜索项 -->
			<a-row :gutter="16">
				<a-col :span="5">
					<a-form-item label="车牌号码" name="plateNumber">
						<a-input v-model:value="searchFormState.plateNumber" placeholder="请输入车牌号码" allowClear/>
					</a-form-item>
				</a-col>
				<a-col :span="5">
					<a-form-item label="状态" name="status">
						<a-select v-model:value="searchFormState.status" placeholder="请选择状态"
								  :options="statusOptions" allowClear/>
					</a-form-item>
				</a-col>
				<a-col :span="5">
					<a-form-item label="进厂道闸" name="entryGate">
						<a-input v-model:value="searchFormState.entryGate" placeholder="请输入进厂道闸" allowClear/>
					</a-form-item>
				</a-col>
				<a-col :span="9">
					<div class="table-page-search-submitButtons">
						<a-button type="primary" @click="tableRef.refresh()">查询</a-button>
						<a-button style="margin: 0 8px" @click="reset">重置</a-button>
						<a @click="toggleAdvanced">
							{{ advanced ? '收起' : '展开' }}
							<component :is="advanced ? UpOutlined : DownOutlined"/>
						</a>
					</div>
				</a-col>
			</a-row>

			<!-- 高级搜索项 -->
			<div v-show="advanced" class="advanced-search-row">
				<a-row :gutter="16" class="advanced">
					<a-col :span="5">
						<a-form-item label="进厂时间" name="entryTime">
							<a-range-picker v-model:value="searchFormState.entryTime" value-format="YYYY-MM-DD HH:mm:ss"
											show-time/>
						</a-form-item>
					</a-col>
					<a-col :span="5">
						<a-form-item label="出厂时间" name="exitTime">
							<a-range-picker v-model:value="searchFormState.exitTime" value-format="YYYY-MM-DD HH:mm:ss"
											show-time/>
						</a-form-item>
					</a-col>
					<a-col :span="5">
						<a-form-item label="出厂道闸" name="exitGate">
							<a-input v-model:value="searchFormState.exitGate" placeholder="请输入出厂道闸" allowClear/>
						</a-form-item>
					</a-col>
				</a-row>
			</div>
		</a-form>
	</a-card>
	<a-card :bordered="false" class="mt-2">
		<s-table ref="tableRef" :columns="columns" :data="loadData" :alert="options.alert.show" bordered
				 :row-key="(record) => record.id" :tool-config="toolConfig" :row-selection="options.rowSelection">
			<template #operator class="table-operator">
				<a-space>
					<a-button type="primary" @click="entryFormRef.onOpen()" v-if="hasPerm('carAccessEntry')">
						<template #icon>
							<plus-outlined/>
						</template>
						登记进厂
					</a-button>
					<a-button type="primary" @click="exportCarAccess" v-if="hasPerm('carAccessExport')">
						<template #icon>
							<download-outlined/>
						</template>
						导出
					</a-button>
					<xn-batch-delete v-if="hasPerm('carAccessDelete')" :selectedRowKeys="selectedRowKeys"
									 @batchDelete="deleteBatchCarAccess"/>
				</a-space>
			</template>
			<template #bodyCell="{ column, record }">
				<template v-if="column.dataIndex === 'status'">
					<a-tag :color="record.status === '1' ? 'success' : 'warning'">
						{{ record.status ? $TOOL.dictTypeData('YES_NO', record.status) : '-' }}
					</a-tag>
				</template>
				<template v-else-if="column.dataIndex === 'syncStatus'">
					<a-tag :color="record.status === '1' ? 'success' : 'warning'">
						{{ record.syncStatus ? $TOOL.dictTypeData('YES_NO', record.syncStatus) : '-' }}
					</a-tag>
				</template>
				<template v-if="column.dataIndex === 'entryImageUrl'">
					<a-image v-if="record.entryImageUrl" :width="50" :src="record.entryImageUrl" :preview="{
            src: record.entryImageUrl,
            mask: () => h(EyeOutlined)
          }"/>
					<span v-else>-</span>
				</template>
				<template v-if="column.dataIndex === 'exitImageUrl'">
					<a-image v-if="record.exitImageUrl" :width="50" :src="record.exitImageUrl" :preview="{
            src: record.exitImageUrl,
            mask: () => h(EyeOutlined)
          }"/>
					<span v-else>-</span>
				</template>
				<template v-if="column.dataIndex === 'action'">
					<a @click="viewDetail(record)" v-if="hasPerm('carAccessDetail')">查看</a>
					<a-divider type="vertical"
							   v-if="hasPerm(['carAccessDetail', 'carAccessExit'], 'and') && record.status === '0'"/>
					<a @click="exitFormRef.onOpen(record)"
					   v-if="hasPerm('carAccessExit') && record.status === '0'">登记出厂</a>
					<a-divider type="vertical" v-if="hasPerm(['carAccessExit', 'carAccessEdit'], 'and')"/>
					<a @click="editFormRef.onOpen(record)" v-if="hasPerm('carAccessEdit')">编辑</a>
					<a-divider type="vertical" v-if="hasPerm(['carAccessEdit', 'carAccessDelete'], 'and')"/>
					<a-popconfirm title="确定要删除吗？" @confirm="deleteCarAccess(record)">
						<a-button type="link" danger size="small" v-if="hasPerm('carAccessDelete')">删除</a-button>
					</a-popconfirm>
				</template>
			</template>
		</s-table>
	</a-card>
	<EntryForm ref="entryFormRef" @successful="tableRef.refresh()"/>
	<ExitForm ref="exitFormRef" @successful="tableRef.refresh()"/>
	<EditForm ref="editFormRef" @successful="tableRef.refresh()"/>
	<DetailModal ref="detailModalRef"/>
</template>

<script setup name="access">
import {ref, reactive, computed, onMounted, h} from 'vue'
import {message} from 'ant-design-vue'
import {PlusOutlined, DownloadOutlined, EyeOutlined, UpOutlined, DownOutlined} from '@ant-design/icons-vue'
import {carAccessRecordApi} from '@/api/biz/carApi'
import EntryForm from './entryForm.vue'
import ExitForm from './exitForm.vue'
import EditForm from './editForm.vue'
import DetailModal from './detailModal.vue'
import {hasPerm} from '@/utils/permission'
import {cloneDeep} from 'lodash-es'

// 表格配置
const toolConfig = {refresh: true, height: true, columnSetting: true, striped: false}

// 表格选择配置
const options = {
	alert: {
		show: true,
		clear: () => {
			selectedRowKeys.value = []
		}
	},
	rowSelection: {
		onChange: (selectedRowKey) => {
			selectedRowKeys.value = selectedRowKey
		}
	}
}

// 表格选中行
const selectedRowKeys = ref([])

// 表单引用
const entryFormRef = ref(null)
const exitFormRef = ref(null)
const editFormRef = ref(null)
const detailModalRef = ref(null)
const tableRef = ref(null)
const searchFormRef = ref(null)

// 高级搜索
const advanced = ref(false)
const toggleAdvanced = () => {
	advanced.value = !advanced.value
}

// 搜索表单状态
const searchFormState = reactive({
	plateNumber: '',
	status: undefined,
	entryGate: '',
	entryTime: [],
	exitTime: [],
	exitGate: ''
})

// 处理时间范围
const searchParams = computed(() => {
	const params = {...searchFormState}

	// 处理进厂时间范围
	if (params.entryTime && params.entryTime.length === 2) {
		params.entryTimeStart = params.entryTime[0]
		params.entryTimeEnd = params.entryTime[1]
	}
	delete params.entryTime

	// 处理出厂时间范围
	if (params.exitTime && params.exitTime.length === 2) {
		params.exitTimeStart = params.exitTime[0]
		params.exitTimeEnd = params.exitTime[1]
	}
	delete params.exitTime

	return params
})

// 重置搜索
const reset = () => {
	searchFormRef.value.resetFields()
	tableRef.value.refresh(true)
}

// 表格列配置
const columns = [
	{
		title: '车牌号码',
		dataIndex: 'plateNumber',
		ellipsis: true,
	},
	{
		title: '进厂时间',
		dataIndex: 'entryTime',
		ellipsis: true,
	},
	{
		title: '进厂道闸',
		dataIndex: 'entryGate',
		ellipsis: true,
	},
	{
		title: '出厂时间',
		dataIndex: 'exitTime',
		ellipsis: true,
	},
	{
		title: '出厂道闸',
		dataIndex: 'exitGate',
		ellipsis: true,
	},
	{
		title: '停留时长(分钟)',
		dataIndex: 'stayDuration',
		ellipsis: true,
	},
	{
		title: '出场状态',
		dataIndex: 'status'
	},
	{
		title: '同步状态',
		dataIndex: 'syncStatus'
	},
	{
		title: '操作',
		dataIndex: 'action',
		width: 250,
		fixed: 'right',
		align: 'center'
	}
]

// 加载表格数据
const loadData = async (parameter) => {
	const params = cloneDeep(searchParams.value)
	const data = await carAccessRecordApi.carAccessPage(Object.assign(parameter, params))
	return data
}

// 查看详情
const viewDetail = (record) => {
	detailModalRef.value.onOpen(record)
}

// 删除单条数据
const deleteCarAccess = (record) => {
	let params = [
		{
			id: record.id
		}
	]
	carAccessRecordApi.carAccessDelete(params).then(() => {
		tableRef.value.refresh(true)
	})
}

// 批量删除
const deleteBatchCarAccess = (params) => {
	carAccessRecordApi.carAccessDelete(params).then(() => {
		tableRef.value.clearRefreshSelected()
	})
}

// 导出数据
const exportCarAccess = () => {
	message.info('导出功能待实现')
}

// 页面加载完成后自动发起请求
onMounted(() => {
	tableRef.value.refresh(true)
})

// 字典数据
const statusOptions = ref([
	{label: '未出厂', value: '0'},
	{label: '已出厂', value: '1'}
])
</script>

<style scoped>
.advanced-search-row {
	margin-top: 16px;
}
</style>
