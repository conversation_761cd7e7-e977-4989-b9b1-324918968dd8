<template>
	<a-modal
		title="小车进出记录详情"
		:width="680"
		:visible="visible"
		:footer="null"
		:maskClosable="false"
		@cancel="onClose"
	>
		<a-descriptions bordered :column="1">
			<a-descriptions-item label="车牌号码">{{ detail.plateNumber }}</a-descriptions-item>
			<a-descriptions-item label="状态">
				<a-tag :color="detail.status === '1' ? 'success' : 'warning'">
					{{ detail.status === '1' ? '已出厂' : '未出厂' }}
				</a-tag>
			</a-descriptions-item>

			<!-- 进厂信息 -->
			<a-descriptions-item label="进厂时间">{{ detail.entryTime }}</a-descriptions-item>
			<a-descriptions-item label="进厂道闸">{{ detail.entryGate }}</a-descriptions-item>
			<a-descriptions-item label="进厂抓拍图片">
				<a-image
					v-if="detail.entryImageUrl"
					:width="200"
					:src="detail.entryImageUrl"
					:preview="{
            src: detail.entryImageUrl,
            mask: () => h(EyeOutlined)
          }"
				/>
				<span v-else>-</span>
			</a-descriptions-item>

			<!-- 出厂信息 -->
			<a-descriptions-item label="出厂时间">{{ detail.exitTime || '-' }}</a-descriptions-item>
			<a-descriptions-item label="出厂道闸">{{ detail.exitGate || '-' }}</a-descriptions-item>
			<a-descriptions-item label="出厂抓拍图片">
				<a-image
					v-if="detail.exitImageUrl"
					:width="200"
					:src="detail.exitImageUrl"
					:preview="{
            src: detail.exitImageUrl,
            mask: () => h(EyeOutlined)
          }"
				/>
				<span v-else>-</span>
			</a-descriptions-item>

			<!-- 其他信息 -->
			<a-descriptions-item label="停留时长(分钟)">{{ detail.stayDuration || '-' }}</a-descriptions-item>
			<a-descriptions-item label="创建时间">{{ detail.createTime }}</a-descriptions-item>
			<a-descriptions-item label="更新时间">{{ detail.updateTime }}</a-descriptions-item>
			<a-descriptions-item label="备注">{{ detail.remark }}</a-descriptions-item>
		</a-descriptions>
	</a-modal>
</template>

<script setup>
import {ref, reactive, h} from 'vue'
import {EyeOutlined} from '@ant-design/icons-vue'
import tool from '@/utils/tool'

// 弹窗控制
const visible = ref(false)

// 详情数据
const detail = reactive({
	id: '',
	plateNumber: '',
	status: '',
	entryTime: '',
	entryGate: '',
	entryImageUrl: '',
	exitTime: '',
	exitGate: '',
	exitImageUrl: '',
	stayDuration: '',
	createTime: '',
	updateTime: ''
})

// 打开弹窗
const onOpen = (record) => {
	if (!record || !record.id) {
		return
	}

	visible.value = true

	// 直接使用传入的记录数据，不再调用接口
	detail.id = record.id
	detail.plateNumber = record.plateNumber
	detail.status = record.status
	detail.entryTime = record.entryTime
	detail.entryGate = record.entryGate
	detail.entryImageUrl = record.entryImageUrl
	detail.exitTime = record.exitTime
	detail.exitGate = record.exitGate
	detail.exitImageUrl = record.exitImageUrl
	detail.stayDuration = record.stayDuration
	detail.createTime = record.createTime
	detail.updateTime = record.updateTime
	detail.remark = record.remark
}

// 关闭弹窗
const onClose = () => {
	visible.value = false
}

// 暴露方法
defineExpose({
	onOpen
})
</script>
