<template>
  <a-card :bordered="false">
    <a-form ref="searchFormRef" name="advanced_search" :model="searchFormState" class="ant-advanced-search-form">
      <a-row :gutter="16">
        <a-col :span="5">
          <a-form-item label="车牌号码" name="plateNumber">
            <a-input v-model:value="searchFormState.plateNumber" placeholder="请输入车牌号码" allowClear />
          </a-form-item>
        </a-col>
        <a-col :span="5">
          <a-form-item label="车牌颜色" name="plateColor">
            <a-select v-model:value="searchFormState.plateColor" placeholder="请选择车牌颜色" :options="plateColorOptions"
              allowClear />
          </a-form-item>
        </a-col>
        <a-col :span="5">
          <a-form-item label="状态" name="status">
            <a-select v-model:value="searchFormState.status" placeholder="请选择状态" :options="statusOptions" allowClear />
          </a-form-item>
        </a-col>
        <a-col :span="9">
          <div class="table-page-search-submitButtons">
            <a-button type="primary" @click="tableRef.refresh()">查询</a-button>
            <a-button style="margin: 0 8px" @click="reset">重置</a-button>
            <a @click="toggleAdvanced">
              {{ advanced ? '收起' : '展开' }}
              <component :is="advanced ? 'up-outlined' : 'down-outlined'" />
            </a>
          </div>
        </a-col>
      </a-row>
      <div v-if="advanced" class="advanced-search-row">
        <a-row :gutter="16" class="advanced">
          <a-col :span="5">
            <a-form-item label="车主姓名" name="ownerName">
              <a-input v-model:value="searchFormState.ownerName" placeholder="请输入车主姓名" allowClear />
            </a-form-item>
          </a-col>
          <a-col :span="5">
            <a-form-item label="车主电话" name="ownerPhone">
              <a-input v-model:value="searchFormState.ownerPhone" placeholder="请输入车主电话" allowClear />
            </a-form-item>
          </a-col>
          <a-col :span="5">
            <a-form-item label="有效期" name="validityPeriodTimeRange">
              <a-input v-model:value="searchFormState.validityPeriodTimeRange" placeholder="请输入有效期" allowClear />
            </a-form-item>
          </a-col>
          <a-col :span="5">
            <a-form-item label="所属部门" name="orgId">
              <a-input-group compact>
                <a-input v-model:value="orgNames" placeholder="请选择所属部门" style="width: calc(100% - 40px)" readOnly />
                <a-button type="primary" @click="showOrgSelect" style="width: 40px">
                  <template #icon><search-outlined /></template>
                </a-button>
              </a-input-group>
            </a-form-item>
          </a-col>
        </a-row>
      </div>
    </a-form>
  </a-card>
  <a-card :bordered="false" class="mt-2">
    <s-table ref="tableRef" :columns="columns" :data="loadData" :alert="options.alert.show" bordered
      :row-key="(record) => record.id" :tool-config="toolConfig" :row-selection="options.rowSelection">
      <template #operator class="table-operator">
        <a-space>
          <a-button type="primary" @click="formRef.onOpen()">
            <template #icon><plus-outlined /></template>
            新增
          </a-button>
          <a-button type="primary" @click="exportCarInfo">
            <template #icon><download-outlined /></template>
            导出
          </a-button>
          <a-button type="primary" @click="showImportModal">
            <template #icon><upload-outlined /></template>
            导入
          </a-button>
          <xn-batch-delete :selectedRowKeys="selectedRowKeys" @batchDelete="deleteBatchCarInfo" />
        </a-space>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'plateColor'">
          {{ $TOOL.dictTypeData('PLATE_COLOR', record.plateColor) }}
        </template>
        <template v-if="column.dataIndex === 'status'">
          <a-tag :color="record.status === '1' ? 'success' : 'error'">
            {{ record.status === '1' ? '启用' : '禁用' }}
          </a-tag>
        </template>
        <template v-if="column.dataIndex === 'action'">
          <a @click="formRef.onOpen(record)">编辑</a>
          <a-divider type="vertical" />
          <a @click="showRuleConfig(record)">配置规则</a>
          <a-divider type="vertical" />
          <a-popconfirm title="确定要删除吗？" @confirm="deleteCarInfo(record)">
            <a-button type="link" danger size="small">删除</a-button>
          </a-popconfirm>
        </template>
      </template>
    </s-table>
  </a-card>
  <Form ref="formRef" @successful="tableRef.refresh()" />

  <!-- 添加规则配置组件 -->
  <RuleConfig v-model:visible="ruleConfigVisible" :car-id="currentCarId" @successful="tableRef.refresh()" />

  <!-- 添加组织选择组件 -->
  <OrgSelect v-model:visible="orgSelectVisible" title="选择所属部门"
    :value="searchFormState.orgId ? [searchFormState.orgId] : []" :multiple="false" @confirm="handleOrgSelect" />

  <!-- 导入模态框 -->
  <a-modal v-model:visible="importModalVisible" title="导入车辆信息" :width="600" :footer="null" @cancel="handleImportCancel">
    <div class="import-modal-content">
      <a-alert type="info" show-icon message="请先下载导入模板，按照模板格式填写数据后上传" style="margin-bottom: 16px" />

      <div class="import-actions">
        <a-row :gutter="16" align="middle">
          <a-col :span="12">
            <a-button type="primary" @click="downloadTemplate" block>
              <template #icon><download-outlined /></template>
              下载导入模板
            </a-button>
          </a-col>
          <a-col :span="12">
            <a-upload name="file" :multiple="false" :show-upload-list="true" :before-upload="beforeUpload"
              :custom-request="handleImportUpload" accept=".xlsx,.xls">
              <a-button type="primary" block>
                <template #icon><upload-outlined /></template>
                选择文件并上传
              </a-button>
            </a-upload>
          </a-col>
        </a-row>
      </div>

      <a-divider>导入说明</a-divider>

      <div class="import-rules">
        <a-card size="small" title="文件要求" style="margin-bottom: 8px">
          <p>导入文件必须是Excel格式(.xlsx或.xls)，大小不超过2MB</p>
        </a-card>

        <a-card size="small" title="字段说明">
          <a-row :gutter="[16, 8]">
            <a-col :span="12">
              <div class="field-item">
                <span class="field-name">车牌号码：</span>
                <span class="field-desc">必填项，例如“京A12345”</span>
              </div>
            </a-col>
            <a-col :span="12">
              <div class="field-item">
                <span class="field-name">车牌颜色：</span>
                <span class="field-desc">请参照下拉选项填写</span>
              </div>
            </a-col>
            <a-col :span="12">
              <div class="field-item">
                <span class="field-name">所在部门ID：</span>
                <span class="field-desc">必须是系统中已存在的部门ID</span>
              </div>
            </a-col>
            <a-col :span="12">
              <div class="field-item">
                <span class="field-name">身份证号码：</span>
                <span class="field-desc">必须是有效的身份证号码格式</span>
              </div>
            </a-col>
            <a-col :span="12">
              <div class="field-item">
                <span class="field-name">通行规则ID：</span>
                <span class="field-desc">必须是系统中已存在的规则ID</span>
              </div>
            </a-col>
            <a-col :span="12">
              <div class="field-item">
                <span class="field-name">有效期：</span>
                <span class="field-desc">yyyy-MM-dd HH:mm:ss ~ yyyy-MM-dd HH:mm:ss</span>
              </div>
            </a-col>
          </a-row>
        </a-card>
      </div>
    </div>
  </a-modal>
</template>

<script setup name="car">
import { ref, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { PlusOutlined, DownloadOutlined, UploadOutlined, SearchOutlined, UpOutlined, DownOutlined } from '@ant-design/icons-vue'
import { carInfoApi } from '@/api/biz/carApi'
import Form from './form.vue'
import RuleConfig from './ruleConfig.vue'
import OrgSelect from '../components/OrgSelect.vue'
import tool from '@/utils/tool'
import { cloneDeep } from 'lodash-es'
import downloadUtil from '@/utils/downloadUtil'

// 表格配置
const toolConfig = { refresh: true, height: true, columnSetting: true, striped: false }

// 表格选择配置
const options = {
  // columns数字类型字段加入 needTotal: true 可以勾选自动算账
  alert: {
    show: true,
    clear: () => {
      selectedRowKeys.value = []
    }
  },
  rowSelection: {
    onChange: (selectedRowKey) => {
      selectedRowKeys.value = selectedRowKey
    }
  }
}

// 表格选中行
const selectedRowKeys = ref([])

// 表单引用
const formRef = ref(null)
const tableRef = ref(null)
const searchFormRef = ref(null)

// 高级搜索
const advanced = ref(false)
const toggleAdvanced = () => {
  advanced.value = !advanced.value
}

// 搜索表单状态
const searchFormState = ref({
  plateNumber: '',
  plateColor: undefined,
  status: undefined,
  ownerName: '',
  ownerPhone: '',
  orgId: '',
  validityPeriodTimeRange: ''
})

// 组织机构选择相关
const orgSelectVisible = ref(false)
const orgNames = ref('')

// 规则配置相关
const ruleConfigVisible = ref(false)
const currentCarId = ref('')

// 显示组织机构选择器
const showOrgSelect = () => {
  orgSelectVisible.value = true
}

// 处理组织机构选择结果
const handleOrgSelect = (keys, rows) => {
  // 判断是单选还是多选模式
  if (Array.isArray(keys) && keys.length > 0) {
    // 多选模式
    searchFormState.value.orgId = keys[0]
    orgNames.value = rows[0].name
  } else if (keys) {
    // 单选模式
    searchFormState.value.orgId = keys
    orgNames.value = rows.name
  } else {
    // 清空选择
    searchFormState.value.orgId = ''
    orgNames.value = ''
  }
}

// 重置搜索
const reset = () => {
  searchFormRef.value.resetFields()
  orgNames.value = ''
  tableRef.value.refresh(true)
}

// 表格列配置
const columns = [
  {
    title: '车牌号码',
    dataIndex: 'plateNumber',
    ellipsis: true,
  },
  {
    title: '车牌颜色',
    dataIndex: 'plateColor',
    ellipsis: true,
  },
  {
    title: '车主姓名',
    dataIndex: 'ownerName',
    ellipsis: true,
  },
  {
    title: '车主电话',
    dataIndex: 'ownerPhone',
    ellipsis: true,
  },
  {
    title: '有效期',
    dataIndex: 'validityPeriodTimeRange',
    ellipsis: true,
    width: 300
  },
  {
    title: '规则名称',
    dataIndex: 'ruleName',
    ellipsis: true
  },
  {
    title: '所属部门',
    dataIndex: 'orgName',
    ellipsis: true,
  },
  {
    title: '状态',
    dataIndex: 'status',
    ellipsis: true,
  },
  {
    title: '备注',
    dataIndex: 'remark',
    ellipsis: true
  },
   {
    title: '更新时间',
    dataIndex: 'updateTime',
    ellipsis: true
  },
   {
    title: '录入时间',
    dataIndex: 'createTime',
    ellipsis: true
  },
  {
    title: '操作',
    dataIndex: 'action',
    width: 250,
    fixed: 'right',
    align: 'center'
  }
]

// 加载表格数据
const loadData = async (parameter) => {
  const searchFormParam = cloneDeep(searchFormState.value)
  const data = await carInfoApi.carInfoPage(Object.assign(parameter, searchFormParam))
  return data
}

// 删除单条数据
const deleteCarInfo = (record) => {
  let params = [
    {
      id: record.id
    }
  ]
  carInfoApi.carInfoDelete(params).then(() => {
    tableRef.value.refresh(true)
  })
}

// 显示规则配置
const showRuleConfig = (record) => {
  currentCarId.value = record.id
  ruleConfigVisible.value = true
}

// 页面加载完成后自动发起请求
onMounted(() => {
  // 刷新表格数据
  tableRef.value.refresh(true)
})

// 批量删除
const deleteBatchCarInfo = (params) => {
  carInfoApi.carInfoDelete(params).then(() => {
    tableRef.value.clearRefreshSelected()
  })
}

// 导出数据
const exportCarInfo = () => {
  message.info('导出功能待实现')
}

// 导入相关状态
const importModalVisible = ref(false)

// 显示导入模态框
const showImportModal = () => {
  importModalVisible.value = true
}

// 关闭导入模态框
const handleImportCancel = () => {
  importModalVisible.value = false
}

// 下载导入模板
const downloadTemplate = () => {
  carInfoApi.carInfoDownloadTemplate().then(res => {
    downloadUtil.resultDownload(res)
  }).catch(error => {
    console.error('下载模板失败:', error)
    message.error('下载模板失败')
  })
}

// 上传前验证
const beforeUpload = (file) => {
  const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
    file.type === 'application/vnd.ms-excel'
  if (!isExcel) {
    message.error('只能上传Excel文件!')
  }
  const isLt2M = file.size / 1024 / 1024 < 2
  if (!isLt2M) {
    message.error('文件大小不能超过2MB!')
  }
  return isExcel && isLt2M
}

// 处理导入上传
const handleImportUpload = (options) => {
  const { file, onSuccess, onError } = options

  // 创建FormData
  const formData = new FormData()
  formData.append('file', file)

  // 调用导入API
  carInfoApi.carInfoImport(formData).then(res => {
    message.success('导入成功')
    onSuccess(res, file)
    importModalVisible.value = false
    tableRef.value.refresh(true)
  }).catch(error => {
    console.error('导入失败:', error)
    message.error('导入失败: ' + (error.message || '请检查文件格式'))
    onError(error)
  })
}

// 字典数据
const plateColorOptions = tool.dictList('PLATE_COLOR')
const statusOptions = [
  { label: '启用', value: '1' },
  { label: '禁用', value: '0' }
]
</script>

<style scoped>
.advanced-search-row {
  margin-top: 16px;
}

.import-modal-content {
  padding: 8px;
}

.import-actions {
  margin-bottom: 16px;
}

.field-item {
  display: flex;
  flex-direction: column;
}

.field-name {
  font-weight: 500;
  margin-bottom: 4px;
}

.field-desc {
  color: rgba(0, 0, 0, 0.65);
  font-size: 13px;
}
</style>
