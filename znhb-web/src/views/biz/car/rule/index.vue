<template>
  <a-card :bordered="false">
    <a-form ref="searchFormRef" name="advanced_search" :model="searchFormState" class="ant-advanced-search-form">
      <!-- 基本搜索项 -->
      <a-row :gutter="16">
        <a-col :span="5">
          <a-form-item label="规则名称" name="ruleName">
            <a-input v-model:value="searchFormState.ruleName" placeholder="请输入规则名称" allowClear/>
          </a-form-item>
        </a-col>
        <a-col :span="5">
          <a-form-item label="状态" name="status">
            <a-select v-model:value="searchFormState.status" placeholder="请选择状态" :options="statusOptions" allowClear />
          </a-form-item>
        </a-col>
        <a-col :span="9">
          <div class="table-page-search-submitButtons">
            <a-button type="primary" @click="tableRef.refresh()">查询</a-button>
            <a-button style="margin: 0 8px" @click="reset">重置</a-button>
            <a @click="toggleAdvanced">
              {{ advanced ? '收起' : '展开' }}
              <component :is="advanced ? UpOutlined : DownOutlined"/>
            </a>
          </div>
        </a-col>
      </a-row>

      <!-- 高级搜索项 -->
      <div v-show="advanced" class="advanced-search-row">
        <a-row :gutter="16" class="advanced">
          <a-col :span="5">
            <a-form-item label="规则优先级" name="priority">
              <a-input-number v-model:value="searchFormState.priority" placeholder="请输入规则优先级" style="width: 100%" allowClear/>
            </a-form-item>
          </a-col>
          <a-col :span="5">
            <a-form-item label="适用日期" name="applyDays">
              <a-select v-model:value="searchFormState.applyDays" placeholder="请选择适用日期" mode="multiple" :options="applyDaysOptions" allowClear />
            </a-form-item>
          </a-col>
        </a-row>
      </div>
    </a-form>
  </a-card>
  <a-card :bordered="false" class="mt-2">
    <s-table
      ref="tableRef"
      :columns="columns"
      :data="loadData"
      :alert="options.alert.show"
      bordered
      :row-key="(record) => record.id"
      :tool-config="toolConfig"
      :row-selection="options.rowSelection"
    >
      <template #operator class="table-operator">
        <a-space>
          <a-button type="primary" @click="formRef.onOpen()" v-if="hasPerm('carAccessRuleAdd')">
            <template #icon><plus-outlined /></template>
            新增
          </a-button>
          <a-button type="primary" @click="exportRule" v-if="hasPerm('carAccessRuleExport')">
            <template #icon><download-outlined /></template>
            导出
          </a-button>
          <xn-batch-delete
            v-if="hasPerm('carAccessRuleDelete')"
            :selectedRowKeys="selectedRowKeys"
            @batchDelete="deleteBatchRule"
          />
        </a-space>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'status'">
          <a-tag :color="record.status === 'ENABLE' ? 'green' : 'red'">
            {{ record.status === 'ENABLE' ? '启用' : '禁用' }}
          </a-tag>
        </template>
        <template v-if="column.dataIndex === 'maxDuration'">
          {{ formatDuration(record.maxDuration) }}
        </template>
        <template v-if="column.dataIndex === 'action'">
          <a @click="viewDetail(record)" v-if="hasPerm('carAccessRuleDetail')">查看</a>
          <a-divider type="vertical" v-if="hasPerm(['carAccessRuleDetail', 'carAccessRuleEdit'], 'and')" />
          <a @click="formRef.onOpen(record)" v-if="hasPerm('carAccessRuleEdit')">编辑</a>
          <a-divider type="vertical" v-if="hasPerm(['carAccessRuleEdit', 'carAccessRuleDelete'], 'and')" />
          <a-popconfirm title="确定要删除吗？" @confirm="deleteRule(record)">
            <a-button type="link" danger size="small" v-if="hasPerm('carAccessRuleDelete')">删除</a-button>
          </a-popconfirm>
        </template>
      </template>
    </s-table>
  </a-card>
  <rule-form ref="formRef" @successful="tableRef.refresh()" />
</template>

<script setup name="carRule">
import { ref, reactive, computed, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { PlusOutlined, DownloadOutlined, UpOutlined, DownOutlined } from '@ant-design/icons-vue'
import { carAccessRuleApi } from '@/api/biz/carApi'
import { hasPerm } from '@/utils/permission'
import { cloneDeep } from 'lodash-es'
import ruleForm from './form.vue'
import tool from '@/utils/tool'

// 表格配置
const toolConfig = { refresh: true, height: true, columnSetting: true, striped: false }

// 表格选择配置
const options = {
  alert: {
    show: true,
    clear: () => {
      selectedRowKeys.value = []
    }
  },
  rowSelection: {
    onChange: (selectedRowKey) => {
      selectedRowKeys.value = selectedRowKey
    }
  }
}

// 表格选中行
const selectedRowKeys = ref([])

// 表单引用
const formRef = ref(null)
const tableRef = ref(null)
const searchFormRef = ref(null)

// 高级搜索
const advanced = ref(false)
const toggleAdvanced = () => {
  advanced.value = !advanced.value
}

// 搜索表单状态
const searchFormState = reactive({
  ruleName: '',
  status: undefined,
  priority: undefined,
  applyDays: undefined,
  sortField: 'priority',
  sortOrder: 'ascend'
})

// 处理搜索参数
const searchParams = computed(() => {
  const params = cloneDeep(searchFormState)

  // 处理适用日期
  if (params.applyDays && params.applyDays.length > 0) {
    params.applyDays = params.applyDays.join(',')
  }

  return params
})

// 重置搜索
const reset = () => {
  searchFormRef.value.resetFields()
  tableRef.value.refresh(true)
}

// 表格列定义
const columns = [
  {
    title: '规则名称',
    dataIndex: 'ruleName',
    ellipsis: true,
  },
  {
    title: '适用日期',
    dataIndex: 'applyDays',
    ellipsis: true,
    customRender: ({ text }) => {
      if (!text) return '-'
      // 将逗号分隔的字符串转换为数组
      const days = text.split(',')
      // 将每个值转换为对应的字典标签
      return days.map(day => tool.dictTypeData('WEEK_DATE', day)).join(', ')
    }
  },
  {
    title: '允许进入时间',
    dataIndex: 'timeRange',
    ellipsis: true,
    customRender: ({ record }) => {
      if (record.startTime && record.endTime) {
        return `${record.startTime} - ${record.endTime}`
      }
      return '-'
    }
  },
  {
    title: '每日最大进入次数',
    dataIndex: 'maxCount',
    ellipsis: true,
    customRender: ({ record }) => {
      if (!record.maxCount || record.maxCount == 0) return '-'
      return record.maxCount
    }
  },
  {
    title: '最大停留时长',
    dataIndex: 'maxDuration',
    ellipsis: true
  },
  {
    title: '状态',
    dataIndex: 'status',
    ellipsis: true,
  },
  {
    title: '备注',
    dataIndex: 'remark',
    ellipsis: true
  },
  {
    title: '操作',
    dataIndex: 'action',
    width: 180,
    fixed: 'right',
    align: 'center'
  }
]

// 加载表格数据
const loadData = async (parameter) => {
  const params = cloneDeep(searchParams.value)
  const data = await carAccessRuleApi.carAccessRulePage(Object.assign(parameter, params))
  return data
}

// 查看详情
const viewDetail = (record) => {
  formRef.value.onOpen(record, true)
}

// 删除单条数据
const deleteRule = (record) => {
  let params = [
    {
      id: record.id
    }
  ]
  carAccessRuleApi.carAccessRuleDelete(params).then(() => {
    tableRef.value.refresh(true)
  })
}

// 批量删除
const deleteBatchRule = (params) => {
  carAccessRuleApi.carAccessRuleBatchDelete(params).then(() => {
    tableRef.value.clearRefreshSelected()
  })
}

// 导出数据
const exportRule = () => {
  message.info('导出功能待实现')
}

// 格式化时长
const formatDuration = (minutes) => {
  if (!minutes && minutes !== 0) return '-'

  // 将分钟数转换为小时和分钟
  const hours = Math.floor(minutes / 60)
  const remainingMinutes = minutes % 60

  // 根据小时数决定显示格式
  if (hours > 0) {
    if (remainingMinutes === 0) {
      return `${hours}小时`
    }
    return `${hours}小时${remainingMinutes}分钟`
  } else {
    return `${remainingMinutes}分钟`
  }
}

// 页面加载完成后自动发起请求
// onMounted 移动到字典初始化部分

// 字典数据
const statusOptions = ref([
  { label: '启用', value: 'ENABLE' },
  { label: '禁用', value: 'DISABLE' }
])

// 使用字典获取适用日期选项
const applyDaysOptions = ref([])

// 初始化字典数据
onMounted(() => {
  tableRef.value.refresh(true)
  // 加载周期字典
  applyDaysOptions.value = tool.dictList('WEEK_DATE')
})
</script>

<style scoped>
.advanced-search-row {
  margin-top: 16px;
}
</style>
