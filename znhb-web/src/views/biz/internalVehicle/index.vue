<template>
	<!-- 搜索区域 -->
	<a-card :bordered="false">
		<a-form ref="searchFormRef" name="advanced_search" :model="searchFormState" class="ant-advanced-search-form">
			<a-row :gutter="16">
				<!-- 基本搜索项 -->
				<a-col :span="5">
					<a-form-item label="环保编码" name="envRegCode">
						<a-input v-model:value="searchFormState.envRegCode" placeholder="请输入环保登记编码" allow-clear />
					</a-form-item>
				</a-col>
				<a-col :span="5">
					<a-form-item label="车牌号码" name="plateNumber">
						<a-input v-model:value="searchFormState.plateNumber" placeholder="请输入车牌号码" allow-clear />
					</a-form-item>
				</a-col>
				<a-col :span="5">
					<div class="search-buttons">
						<a-button type="primary" @click="tableRef.refresh()">查询</a-button>
						<a-button @click="reset">重置</a-button>
						<a @click="toggleAdvanced" class="expand-button">
							{{ advanced ? '收起' : '展开' }}
							<component :is="advanced ? 'up-outlined' : 'down-outlined'" />
						</a>
					</div>
				</a-col>
			</a-row>

			<!-- 高级搜索项，可展开/收起 -->
			<div v-show="advanced" class="advanced-search-row">
				<a-row :gutter="16" class="advanced">
					<a-col :span="5">
						<a-form-item label="燃料类型" name="fuelType">
							<a-select v-model:value="searchFormState.fuelType" placeholder="请选择燃料类型"
								:options="fuelTypeOptions" allow-clear />
						</a-form-item>
					</a-col>
					<a-col :span="5">
						<a-form-item label="排放标准" name="emissionStandard">
							<a-select v-model:value="searchFormState.emissionStandard" placeholder="请选择排放标准"
								:options="emissionOptions" allow-clear />
						</a-form-item>
					</a-col>
					<a-col :span="5">
						<a-form-item label="联网状态" name="networkStatus">
							<a-select v-model:value="searchFormState.networkStatus" placeholder="请选择联网状态"
								:options="networkStatusOptions" allow-clear />
						</a-form-item>
					</a-col>
				</a-row>
			</div>
		</a-form>
	</a-card>

	<!-- 表格区域 -->
	<a-card :bordered="false" class="mt-2">
		<s-table ref="tableRef" :columns="columns" :data="loadData" :alert="options.alert.show" bordered
			:row-key="(record) => record.id" :tool-config="toolConfig" :row-selection="options.rowSelection"
			>
			<template #operator class="table-operator">
				<a-space>
					<a-button type="primary" @click="formRef.onOpen()" v-if="hasPerm('internalVehicleAdd')">
						<template #icon><plus-outlined /></template>
						新增
					</a-button>
					<xn-batch-delete v-if="hasPerm('internalVehicleBatchDelete')" :selectedRowKeys="selectedRowKeys"
						@batchDelete="deleteBatchInternalVehicle" />
				</a-space>
			</template>
			<template #bodyCell="{ column, record }">
				<template v-if="column.dataIndex === 'action'">
					<a @click="formRef.onOpen(record)" v-if="hasPerm('internalVehicleEdit')">编辑</a>
					<a-divider type="vertical"
						v-if="hasPerm(['internalVehicleEdit', 'internalVehicleDelete'], 'and')" />
					<a-popconfirm title="确定要删除吗？" @confirm="deleteInternalVehicle(record)">
						<a-button type="link" danger size="small" v-if="hasPerm('internalVehicleDelete')">删除</a-button>
					</a-popconfirm>
				</template>
				<template v-else-if="column.dataIndex === 'fuelType'">
					{{ $TOOL.dictTypeData('FUEL_TYPE', record.fuelType) }}
				</template>
				<template v-else-if="column.dataIndex === 'emissionStandard'">
					{{ $TOOL.dictTypeData('EMISSION_STAGE', record.emissionStandard) }}
				</template>
				<template v-else-if="column.dataIndex === 'networkStatus'">
					{{ $TOOL.dictTypeData('NET_STATUS', record.networkStatus) }}
				</template>
				<template v-else-if="column.dataIndex === 'productionDate' || column.dataIndex === 'registrationDate'">
					{{ record[column.dataIndex] ? formatDate(record[column.dataIndex]) : '-' }}
				</template>
				<template v-else-if="column.dataIndex === 'syncStatus'">
					<a-tag :color="record.syncStatus === '1' ? 'success' : 'warning'">
						{{ record.syncStatus ? $TOOL.dictTypeData('YES_NO', record.syncStatus) : '-' }}
					</a-tag>
				</template>
				<!-- <template v-else-if="column.dataIndex === 'vehicleListImage' || column.dataIndex === 'drivingLicenseImage' || column.dataIndex === 'environmentalListElectronicImgUrl' || column.dataIndex === 'heavyVehicleEmissionStageImgUrl'">
					<a-image
						v-if="record[column.dataIndex]"
						:src="record[column.dataIndex]"
						:width="80"
						:height="60"
						style="object-fit: cover; cursor: pointer;"
						:preview="{
							src: record[column.dataIndex]
						}"
					/>
					<span v-else>-</span>
				</template> -->
			</template>
		</s-table>
	</a-card>
	<Form ref="formRef" @successful="tableRef.refresh()" />
</template>

<script setup name="internalVehicle">
import { cloneDeep } from 'lodash-es'
import Form from './form.vue'
import internalVehicleApi from '@/api/biz/internalVehicleApi'
import tool from '@/utils/tool'
import { PlusOutlined } from '@ant-design/icons-vue'
import dayjs from 'dayjs'

const searchFormState = ref({})
const searchFormRef = ref()
const tableRef = ref()
const formRef = ref()
const toolConfig = { refresh: true, height: true, columnSetting: true, striped: false }
const advanced = ref(false) // 控制高级搜索的显示/隐藏

// 切换高级搜索显示状态
const toggleAdvanced = () => {
	advanced.value = !advanced.value
}

// 获取字典数据
const fuelTypeOptions = tool.dictList('FUEL_TYPE')
const emissionOptions = tool.dictList('EMISSION_STAGE')
const networkStatusOptions = tool.dictList('NET_STATUS')

// 格式化日期
const formatDate = (dateStr) => {
	if (!dateStr) return '-'
	return dayjs(dateStr).format('YYYY-MM-DD')
}

const columns = [
	{
		title: '车牌号码',
		dataIndex: 'plateNumber',
		width: 120
	},
	{
		title: '环保登记编码',
		dataIndex: 'envRegCode',
		width: 160
	},
	{
		title: '车辆识别代码',
		dataIndex: 'vin',
		width: 160
	},
	{
		title: '生产日期',
		dataIndex: 'productionDate',
		width: 120
	},
	{
		title: '注册登记日期',
		dataIndex: 'registrationDate',
		width: 120
	},
	{
		title: '车辆品牌型号',
		dataIndex: 'vehicleModel',
		width: 160
	},
	{
		title: '燃料类型',
		dataIndex: 'fuelType',
		width: 120
	},
	{
		title: '排放标准',
		dataIndex: 'emissionStandard',
		width: 100
	},
	{
		title: '联网状态',
		dataIndex: 'networkStatus',
		width: 100
	},
	{
		title: '同步状态',
		dataIndex: 'syncStatus',
		width: 100
	},
	// {
	// 	title: '随车清单照片',
	// 	dataIndex: 'vehicleListImage',
	// 	width: 120
	// },
	// {
	// 	title: '行驶证照片',
	// 	dataIndex: 'drivingLicenseImage',
	// 	width: 120
	// },
	// {
	// 	title: '电子环保清单照片',
	// 	dataIndex: 'environmentalListElectronicImgUrl',
	// 	width: 120
	// },
	// {
	// 	title: '排放阶段照片',
	// 	dataIndex: 'heavyVehicleEmissionStageImgUrl',
	// 	width: 120
	// },
	{
		title: '车辆所有人',
		dataIndex: 'ownerInfo',
		width: 160
	}
]
// 操作栏通过权限判断是否显示
if (hasPerm(['internalVehicleEdit', 'internalVehicleDelete'])) {
	columns.push({
		title: '操作',
		dataIndex: 'action',
		align: 'center',
		fixed: 'right',
		width: 150
	})
}
const selectedRowKeys = ref([])
// 列表选择配置
const options = {
	// columns数字类型字段加入 needTotal: true 可以勾选自动算账
	alert: {
		show: true,
		clear: () => {
			selectedRowKeys.value = ref([])
		}
	},
	rowSelection: {
		onChange: (selectedRowKey, selectedRows) => {
			selectedRowKeys.value = selectedRowKey
		}
	}
}
const loadData = (parameter) => {
	const searchFormParam = cloneDeep(searchFormState.value)
	return internalVehicleApi.internalVehiclePage(Object.assign(parameter, searchFormParam)).then((data) => {
		return data
	})
}
// 重置
const reset = () => {
	searchFormRef.value.resetFields()
	tableRef.value.refresh(true)
}
// 删除
const deleteInternalVehicle = (record) => {
	let params = [
		{
			id: record.id
		}
	]
	internalVehicleApi.internalVehicleDelete(params).then(() => {
		tableRef.value.refresh(true)
	})
}
// 批量删除
const deleteBatchInternalVehicle = (params) => {
	internalVehicleApi.internalVehicleDelete(params).then(() => {
		tableRef.value.clearRefreshSelected()
	})
}
</script>
