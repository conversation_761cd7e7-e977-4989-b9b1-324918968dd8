<template>
	<a-row :gutter="10">
		<a-col :xs="24" :sm="24" :md="24" :lg="5" :xl="5">
			<a-card :bordered="false" :loading="cardLoading">
				<a-tree
					v-if="treeData.length > 0"
					v-model:expandedKeys="defaultExpandedKeys"
					:tree-data="treeData"
					:field-names="treeFieldNames"
					@select="treeSelect"
				>
				</a-tree>
				<a-empty v-else :image="Empty.PRESENTED_IMAGE_SIMPLE" />
			</a-card>
		</a-col>
		<a-col :xs="24" :sm="24" :md="24" :lg="19" :xl="19">
			<a-card :bordered="false" style="margin-bottom: 10px">
				<a-form ref="searchFormRef" name="advanced_search" class="ant-advanced-search-form" :model="searchFormState">
					<a-row :gutter="24">
						<a-col :span="8">
							<a-form-item name="searchKey" label="名称关键词">
								<a-input v-model:value="searchFormState.searchKey" placeholder="请输入机构名称关键词" />
							</a-form-item>
						</a-col>
						<a-col :span="8">
							<a-button type="primary" @click="tableRef.refresh(true)">
								<template #icon><SearchOutlined /></template>
								查询
							</a-button>
							<a-button class="snowy-buttom-left" @click="reset">
								<template #icon><redo-outlined /></template>
								重置
							</a-button>
						</a-col>
					</a-row>
				</a-form>
			</a-card>
			<a-card :bordered="false">
				<s-table
					ref="tableRef"
					:columns="columns"
					:data="loadData"
					:expand-row-by-click="true"
					:alert="options.alert.show"
					bordered
					:row-key="(record) => record.id"
					:tool-config="toolConfig"
					:row-selection="options.rowSelection"
				>
					<template #operator class="table-operator">
						<a-space>
							<a-button
								type="primary"
								@click="formRef.onOpen(undefined, searchFormState.parentId)"
								v-if="hasPerm('bizOrgAdd')"
							>
								<template #icon><plus-outlined /></template>
								新增
							</a-button>
							<xn-batch-delete
								v-if="hasPerm('bizOrgBatchDelete')"
								:selectedRowKeys="selectedRowKeys"
								@batchDelete="deleteBatchOrg"
							/>
						</a-space>
					</template>
					<template #bodyCell="{ column, record }">
						<template v-if="column.dataIndex === 'category'">
							{{ $TOOL.dictTypeData('ORG_CATEGORY', record.category) }}
						</template>
						<template v-if="column.dataIndex === 'action'">
							<a-space>
								<a @click="formRef.onOpen(record)" v-if="hasPerm('bizOrgEdit')">编辑</a>
								<a-divider type="vertical" v-if="hasPerm(['bizOrgEdit', 'bizOrgDelete'], 'and')" />
								<a-popconfirm title="删除此机构与下级机构吗？" @confirm="removeOrg(record)">
									<a-button type="link" danger size="small" v-if="hasPerm('bizOrgDelete')">删除</a-button>
								</a-popconfirm>
								<a-divider type="vertical" />
								<a @click="generateOrgQRCode(record)">备案二维码</a>
							</a-space>
						</template>
					</template>
				</s-table>
			</a-card>
		</a-col>
	</a-row>
	<Form ref="formRef" @successful="tableRef.refresh()" />
	
	<!-- 二维码模态框 -->
	<a-modal
		v-model:visible="qrcodeVisible"
		title="车辆备案二维码"
		:footer="null"
		width="360px"
	>
		<div class="qrcode-container">
			<div class="base-url-form">
				<a-input-group compact>
					<a-input 
						v-model:value="baseUrl" 
						placeholder="请输入可外网访问的URL" 
						style="width: calc(100% - 70px);"
					/>
					<a-button type="primary" @click="regenerateQRCode">更新</a-button>
				</a-input-group>
				<div class="url-tip">默认使用当前地址，如无法访问请输入外网可访问的URL</div>
			</div>
			<div class="qrcode-image">
				<img :src="qrcodeImage" alt="车辆备案二维码" v-if="qrcodeImage" />
				<a-spin v-else />
			</div>
			<div class="full-url">
				<a-input v-model:value="fullUrl" readonly>
					<template #addonAfter>
						<copy-outlined @click="copyUrl" title="复制链接" />
					</template>
				</a-input>
			</div>
			<div class="qrcode-tips">请车主扫描此二维码或直接访问上方链接进行车辆备案</div>
			<div class="qrcode-actions">
				<a-button type="primary" @click="downloadQRCode">下载二维码</a-button>
				<a-button @click="qrcodeVisible = false">关闭</a-button>
			</div>
		</div>
	</a-modal>
</template>

<script setup name="bizOrg">
	import { Empty } from 'ant-design-vue'
	import { isEmpty } from 'lodash-es'
	import bizOrgApi from '@/api/biz/bizOrgApi'
	import Form from './form.vue'
	import { log } from '@antv/g2plot/lib/utils'
	import { message, Modal } from 'ant-design-vue'
	import { SearchOutlined, CopyOutlined } from '@ant-design/icons-vue'
	import qrcodeUtil from '@/utils/qrcodeUtil'

	const columns = [
		{
			title: '机构名称',
			dataIndex: 'name'
		},
		{
			title: '分类',
			dataIndex: 'category'
		},
		{
			title: '排序',
			dataIndex: 'sortCode',
			width: 100
		}
	]
	if (hasPerm(['bizOrgEdit', 'bizOrgDelete'])) {
		columns.push({
			title: '操作',
			dataIndex: 'action',
			align: 'center',
			width: '300px'
		})
	}
	const selectedRowKeys = ref([])
	// 列表选择配置
	const options = {
		alert: {
			show: false,
			clear: () => {
				selectedRowKeys.value = ref([])
			}
		},
		rowSelection: {
			onChange: (selectedRowKey, selectedRows) => {
				selectedRowKeys.value = selectedRowKey
			}
		}
	}
	const toolConfig = { refresh: true, height: true, columnSetting: true }
	// 定义tableDOM
	const tableRef = ref(null)
	const formRef = ref()
	const searchFormRef = ref()
	const searchFormState = ref({})
	// 默认展开的节点
	const defaultExpandedKeys = ref([])
	const treeData = ref([])
	// 替换treeNode 中 title,key,children
	const treeFieldNames = { children: 'children', title: 'name', key: 'id' }
	const cardLoading = ref(true)

	// 二维码相关状态
	const qrcodeVisible = ref(false)
	const qrcodeImage = ref('')
	const baseUrl = ref('')
	const fullUrl = ref('')
	const currentOrgId = ref('')

	// 表格查询 返回 Promise 对象
	const loadData = (parameter) => {
		loadTreeData()
		return bizOrgApi.orgPage(Object.assign(parameter, searchFormState.value)).then((res) => {
			console.log(res)
			return res
		})
	}
	// 重置
	const reset = () => {
		searchFormRef.value.resetFields()
		tableRef.value.refresh(true)
	}
	// 加载左侧的树
	const loadTreeData = () => {
		bizOrgApi
			.orgTree()
			.then((res) => {
				cardLoading.value = false
				if (res !== null) {
					treeData.value = res
					if (isEmpty(defaultExpandedKeys.value)) {
						// 默认展开2级
						treeData.value.forEach((item) => {
							// 因为0的顶级
							if (item.parentId === '0') {
								defaultExpandedKeys.value.push(item.id)
								// 取到下级ID
								if (item.children) {
									item.children.forEach((items) => {
										defaultExpandedKeys.value.push(items.id)
									})
								}
							}
						})
					}
				}
			})
			.finally(() => {
				cardLoading.value = false
			})
	}
	// 点击树查询
	const treeSelect = (selectedKeys) => {
		if (selectedKeys.length > 0) {
			searchFormState.value.parentId = selectedKeys.toString()
		} else {
			delete searchFormState.value.parentId
		}
		tableRef.value.refresh(true)
	}
	// 删除
	const removeOrg = (record) => {
		let params = [
			{
				id: record.id
			}
		]
		bizOrgApi.orgDelete(params).then(() => {
			tableRef.value.refresh(true)
		})
	}
	// 批量删除
	const deleteBatchOrg = (params) => {
		bizOrgApi.orgDelete(params).then(() => {
			tableRef.value.clearRefreshSelected()
		})
	}

	// 生成组织机构二维码
	const generateOrgQRCode = async (record) => {
		try {
			currentOrgId.value = record.id
			
			// 构建上传页面的URL
			const urlBase = baseUrl.value || window.location.origin
			const uploadUrl = `${urlBase}/h5/vehicle/upload?orgId=${record.id}`
			
			// 生成二维码
			const imageResult = await qrcodeUtil.generateQRCode(uploadUrl)
			
			// 显示二维码
			qrcodeImage.value = imageResult
			qrcodeVisible.value = true
			fullUrl.value = uploadUrl
		} catch (error) {
			console.error('生成二维码失败：:', error)
		}
	}

	// 下载二维码
	const downloadQRCode = () => {
		if (!qrcodeImage.value) return
		
		const link = document.createElement('a')
		link.href = qrcodeImage.value
		link.download = `车辆备案二维码.png`
		document.body.appendChild(link)
		link.click()
		document.body.removeChild(link)
	}

	// 复制链接
	const copyUrl = () => {
		const input = document.createElement('input')
		input.value = fullUrl.value
		document.body.appendChild(input)
		input.select()
		document.execCommand('copy')
		document.body.removeChild(input)
		message.success('链接已复制到剪贴板')
	}

	// 重新生成二维码
	const regenerateQRCode = async () => {
		try {
			if (!currentOrgId.value) {
				message.error('缺少组织机构ID')
				return
			}
			
			// 构建上传页面的URL
			const urlBase = baseUrl.value || window.location.origin
			const uploadUrl = `${urlBase}/h5/vehicle/upload?orgId=${currentOrgId.value}`
			
			// 生成二维码
			const imageResult = await qrcodeUtil.generateQRCode(uploadUrl)
			
			// 显示二维码
			qrcodeImage.value = imageResult
			fullUrl.value = uploadUrl
			message.success('二维码已更新')
		} catch (error) {
			message.error('生成二维码失败：' + (error.message || '未知错误'))
		}
	}
</script>

<style scoped>
	.ant-form-item {
		margin-bottom: 0 !important;
	}
	.primaryAdd {
		margin-right: 10px;
	}
	.snowy-buttom-left {
		margin-left: 8px;
	}
	
	.qrcode-container {
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 10px;
	}

	.qrcode-title {
		font-size: 16px;
		font-weight: bold;
		margin-bottom: 15px;
	}

	.qrcode-image {
		width: 200px;
		height: 200px;
		margin-bottom: 15px;
		display: flex;
		justify-content: center;
		align-items: center;
	}

	.qrcode-image img {
		max-width: 100%;
		max-height: 100%;
	}

	.qrcode-tips {
		color: #666;
		margin-bottom: 15px;
		font-size: 14px;
	}

	.qrcode-actions {
		display: flex;
		gap: 10px;
	}

	.base-url-form {
		margin-bottom: 15px;
		width: 100%;
	}

	.url-tip {
		color: #666;
		font-size: 14px;
		margin-top: 5px;
	}

	.full-url {
		margin-bottom: 15px;
		width: 100%;
	}
</style>
