<template>
	<a-modal
		:title="'上传行驶证'"
		:width="550"
		:visible="visible"
		:confirm-loading="confirmLoading"
		@cancel="onClose"
		:footer="null"
	>
		<a-spin :spinning="confirmLoading">
			
			<a-alert
				message="请上传行驶证图片，系统将自动识别车辆信息"
				type="info"
				show-icon
				style="margin-bottom: 16px"
			/>
			
			<a-radio-group v-model:value="uploadMode" @change="handleModeChange" style="margin-bottom: 16px">
				<a-radio value="single">单页识别</a-radio>
				<a-radio value="double">双页识别</a-radio>
			</a-radio-group>
			
			<!-- 单页识别（分开上传正副页） -->
			<div v-show="uploadMode === 'single'">
				<div style="margin-bottom: 16px">
					<div style="margin-bottom: 8px; font-weight: bold">行驶证首页：</div>
					<a-upload-dragger
						v-model:file-list="frontFileList"
						name="frontFile"
						:multiple="false"
						:before-upload="(file) => beforeUploadFront(file)"
						@change="handleFrontUploadChange"
						:show-upload-list="true"
						:max-count="1"
					>
						<p class="ant-upload-text">上传行驶证首页</p>
						<p class="ant-upload-hint">支持jpg, jpeg, png格式的图片文件</p>
					</a-upload-dragger>
				</div>
				
				<div>
					<div style="margin-bottom: 8px; font-weight: bold">行驶证副页：</div>
					<a-upload-dragger
						v-model:file-list="backFileList"
						name="backFile"
						:multiple="false"
						:before-upload="(file) => beforeUploadBack(file)"
						@change="handleBackUploadChange"
						:show-upload-list="true"
						:max-count="1"
					>
						<p class="ant-upload-text">上传行驶证副页</p>
						<p class="ant-upload-hint">支持jpg, jpeg, png格式的图片文件</p>
					</a-upload-dragger>
				</div>
			</div>
			
			<!-- 双页识别（一张图片包含首页和副页） -->
			<div v-show="uploadMode === 'double'">
				<a-upload-dragger
					v-model:file-list="fileList"
					name="file"
					:multiple="false"
					:before-upload="beforeUploadDouble"
					@change="handleDoubleUploadChange"
					:show-upload-list="true"
					:max-count="1"
				>
					<p class="ant-upload-drag-icon">
						<inbox-outlined />
					</p>
					<p class="ant-upload-text">点击或拖拽文件到此区域上传</p>
					<p class="ant-upload-hint">请上传包含行驶证首页和副页的单张图片，支持jpg, jpeg, png格式</p>
				</a-upload-dragger>
			</div>

			<div style="margin-top: 24px; text-align: right;">
				<a-button @click="onClose">取消</a-button>
				<a-button
					type="primary"
					:disabled="uploadDisabled"
					:loading="confirmLoading"
					style="margin-left: 8px"
					@click="handleUpload"
				>
					识别
				</a-button>
			</div>
		</a-spin>
	</a-modal>
</template>

<script setup name="licenseUpload">
	import { message } from 'ant-design-vue'
	import vehicleApi from '@/api/biz/vehicleApi'
	import { ref, reactive, computed, watch } from 'vue'
	import fileApi from '@/api/dev/fileApi'
	import { InboxOutlined } from '@ant-design/icons-vue'
	import vehicleLicenseUtils from '@/utils/vehicleLicenseUtils'

	const emit = defineEmits(['success', 'close'])
	const visible = ref(false)
	const confirmLoading = ref(false)
	const uploadMode = ref('single')
	const fileList = ref([])
	const frontFileList = ref([])
	const backFileList = ref([])
	
	// 上传后的文件URL
	const doubleFileUrl = ref('')
	const frontFileUrl = ref('')
	const backFileUrl = ref('')
	
	// 上传状态对象
	const doubleUploadStatus = reactive({
		uploading: false,
		success: false,
		error: false,
		message: ''
	})
	
	const frontUploadStatus = reactive({
		uploading: false,
		success: false,
		error: false,
		message: ''
	})
	
	const backUploadStatus = reactive({
		uploading: false,
		success: false,
		error: false,
		message: ''
	})
	
	// 计算上传按钮是否禁用
	const uploadDisabled = computed(() => {
		if (uploadMode.value === 'single') {
			return frontFileList.value.length === 0 || backFileList.value.length === 0
		} else {
			return fileList.value.length === 0
		}
	})

	// 打开模态框
	const onOpen = (orgId, orgName) => {
		visible.value = true
		fileList.value = []
		frontFileList.value = []
		backFileList.value = []
		uploadMode.value = 'single'
	}

	// 关闭模态框
	const onClose = () => {
		visible.value = false
		fileList.value = []
		frontFileList.value = []
		backFileList.value = []
		emit('close')
	}

	// 单页首页上传处理
	const beforeUploadFront = (file) => {
		const isValid = vehicleLicenseUtils.beforeUpload(file)
		if (!isValid) return false
		
		// 上传文件
		vehicleLicenseUtils.uploadImage(file, 'license_front', '行驶证首页').then(url => {
			frontFileUrl.value = url
			
			// 设置文件显示
			frontFileList.value = [{
				uid: '-1',
				name: file.name,
				status: 'done',
				url: URL.createObjectURL(file),
				originFileObj: file
			}]
			
			// 设置上传状态为成功
			frontUploadStatus.uploading = false
			frontUploadStatus.success = true
		}).catch(error => {
			// 设置上传状态为失败
			frontUploadStatus.uploading = false
			frontUploadStatus.error = true
			frontUploadStatus.message = error.message || '上传失败'
		})
		
		return false // 阻止默认上传行为
	}

	// 单页副页上传处理
	const beforeUploadBack = (file) => {
		const isValid = vehicleLicenseUtils.beforeUpload(file)
		if (!isValid) return false
		
		// 上传文件
		vehicleLicenseUtils.uploadImage(file, 'license_back', '行驶证副页').then(url => {
			backFileUrl.value = url
			
			// 设置文件显示
			backFileList.value = [{
				uid: '-1',
				name: file.name,
				status: 'done',
				url: URL.createObjectURL(file),
				originFileObj: file
			}]
			
			// 设置上传状态为成功
			backUploadStatus.uploading = false
			backUploadStatus.success = true
		}).catch(error => {
			// 设置上传状态为失败
			backUploadStatus.uploading = false
			backUploadStatus.error = true
			backUploadStatus.message = error.message || '上传失败'
		})
		
		return false // 阻止默认上传行为
	}

	// 双页上传处理
	const beforeUploadDouble = (file) => {
		const isValid = vehicleLicenseUtils.beforeUpload(file)
		if (!isValid) return false
		
		// 上传文件
		vehicleLicenseUtils.uploadImage(file, 'license_double', '行驶证两页图片').then(url => {
			doubleFileUrl.value = url
			
			// 设置文件显示
			fileList.value = [{
				uid: '-1',
				name: file.name,
				status: 'done',
				url: URL.createObjectURL(file),
				originFileObj: file
			}]
			
			// 设置上传状态为成功
			doubleUploadStatus.uploading = false
			doubleUploadStatus.success = true
		}).catch(error => {
			// 设置上传状态为失败
			doubleUploadStatus.uploading = false
			doubleUploadStatus.error = true
			doubleUploadStatus.message = error.message || '上传失败'
		})
		
		return false // 阻止默认上传行为
	}

	// 处理单页首页上传变更
	const handleFrontUploadChange = (info) => {
		// 这里只处理UI相关的变化，上传逻辑已经在beforeUpload中处理
	}

	// 处理单页副页上传变更
	const handleBackUploadChange = (info) => {
		// 这里只处理UI相关的变化，上传逻辑已经在beforeUpload中处理
	}

	// 处理双页上传变更
	const handleDoubleUploadChange = (info) => {
		// 这里只处理UI相关的变化，上传逻辑已经在beforeUpload中处理
	}

	// 将文件转换为Base64
	const getBase64 = (file) => {
		return new Promise((resolve, reject) => {
			const reader = new FileReader()
			reader.readAsDataURL(file)
			reader.onload = () => {
				// 移除base64的前缀部分 (如 "data:image/jpeg;base64,")
				const base64String = reader.result.split(',')[1]
				resolve(base64String)
			}
			reader.onerror = (error) => reject(error)
		})
	}

	// 处理上传识别
	const handleUpload = async () => {
		// 验证是否选择了文件
		if (uploadMode.value === 'single' && (frontFileList.value.length === 0 || backFileList.value.length === 0)) {
			message.error('请选择行驶证首页和副页图片')
			return
		}
		
		if (uploadMode.value === 'double' && fileList.value.length === 0) {
			message.error('请先选择行驶证图片')
			return
		}

		// 验证是否已上传成功
		if (uploadMode.value === 'single' && (!frontUploadStatus.success || !backUploadStatus.success)) {
			message.error('请等待图片上传完成')
			return
		}
		
		if (uploadMode.value === 'double' && !doubleUploadStatus.success) {
			message.error('请等待图片上传完成')
			return
		}

		confirmLoading.value = true
		try {
			// 准备识别参数
			const params = {
				mode: uploadMode.value,
				frontFile: frontFileList.value.length > 0 ? frontFileList.value[0].originFileObj : null,
				backFile: backFileList.value.length > 0 ? backFileList.value[0].originFileObj : null,
				doubleFile: fileList.value.length > 0 ? fileList.value[0].originFileObj : null,
				frontUrl: frontFileUrl.value,
				backUrl: backFileUrl.value,
				doubleUrl: doubleFileUrl.value
			}
			
			// 识别行驶证
			const result = await vehicleLicenseUtils.recognizeLicense(params)
			
			if (result) {
				message.success('行驶证识别成功')
				emit('success', result)
				onClose()
			}
		} catch (error) {
			console.error('行驶证识别失败:', error)
		} finally {
			confirmLoading.value = false
		}
	}

	// 模式切换处理函数
	const handleModeChange = (e) => {
		const newMode = e.target.value
		// 切换模式时清空之前的上传文件
		if (newMode === 'single') {
			fileList.value = []
		} else {
			frontFileList.value = []
			backFileList.value = []
		}
	}

	// 模式切换时重置状态
	watch(uploadMode, (newMode) => {
		if (newMode === 'single') {
			doubleUploadStatus.success = false;
			doubleUploadStatus.uploading = false;
			doubleUploadStatus.error = false;
			fileList.value = [];
		} else {
			frontUploadStatus.success = false;
			frontUploadStatus.uploading = false;
			frontUploadStatus.error = false;
			backUploadStatus.success = false;
			backUploadStatus.uploading = false;
			backUploadStatus.error = false;
			frontFileList.value = [];
			backFileList.value = [];
		}
	})

	// 重置状态
	const resetStatus = () => {
		doubleUploadStatus.uploading = false
		doubleUploadStatus.success = false
		doubleUploadStatus.error = false
		doubleUploadStatus.message = ''
		
		frontUploadStatus.uploading = false
		frontUploadStatus.success = false
		frontUploadStatus.error = false
		frontUploadStatus.message = ''
		
		backUploadStatus.uploading = false
		backUploadStatus.success = false
		backUploadStatus.error = false
		backUploadStatus.message = ''
		
		doubleFileUrl.value = ''
		frontFileUrl.value = ''
		backFileUrl.value = ''
	}

	// 暴露方法
	defineExpose({
		onOpen
	})
</script> 