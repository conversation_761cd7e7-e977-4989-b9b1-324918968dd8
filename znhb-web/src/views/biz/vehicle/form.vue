<template>
	<!-- 先添加一个最小化后显示的浮动按钮 -->
	<div v-if="isMinimized" class="form-minimized-btn" @click="restoreForm">
		<car-outlined />
		<span class="minimize-text">{{ formData.id ? '编辑车辆信息' : '增加车辆信息' }}</span>
	</div>

	<!-- 修改表单容器，添加自定义头部 -->
	<xn-form-container :title="formData.id ? '编辑车辆信息' : '增加车辆信息'" :width="800" :visible="visible && !isMinimized"
		:destroy-on-close="true" @close="onClose" class="vehicle-form-container">
		<!-- 添加绝对定位的最小化按钮 -->
		<div class="title-minimize-btn" @click="minimizeForm">
			<minus-outlined />
		</div>

		<a-form ref="formRef" :model="formData" :rules="formRules" layout="vertical">
			<!-- 基础信息卡片 -->
			<a-card title="基本信息" class="form-card" :bordered="false">
				<a-row :gutter="16">
					<a-col :span="8">
						<a-form-item label="车牌号码" name="plateNumber">
							<a-input v-model:value="formData.plateNumber" placeholder="请输入车牌号码" allow-clear />
						</a-form-item>
					</a-col>
					<a-col :span="8">
						<a-form-item label="排放标准" name="emissionStage">
							<a-select v-model:value="formData.emissionStage" placeholder="请选择排放标准"
								:options="emissionStageOptions" />
						</a-form-item>
					</a-col>
					<a-col :span="8">
						<a-form-item label="车牌颜色" name="plateColor">
							<a-select v-model:value="formData.plateColor" placeholder="请选择车牌颜色"
								:options="plateColorOptions" />
						</a-form-item>
					</a-col>
				</a-row>
				<a-row :gutter="16">
					<a-col :span="8">
						<a-form-item label="能源类型" name="energyType">
							<a-input v-model:value="formData.energyType" placeholder="请输入能源类型" allow-clear />
						</a-form-item>
					</a-col>
					<a-col :span="8">
						<a-form-item label="车辆类型" name="vehicleType">
							<a-input v-model:value="formData.vehicleType" placeholder="请输入车辆类型" allow-clear />
						</a-form-item>
					</a-col>
					<a-col :span="8">
						<a-form-item label="车辆识别代号" name="vin">
							<a-input v-model:value="formData.vin" placeholder="请输入车辆识别代号" allow-clear />
						</a-form-item>
					</a-col>
				</a-row>
				<a-row :gutter="24">
					<a-col :span="8">
						<a-form-item label="条码号" name="codeNumber">
							<a-input v-model:value="formData.codeNumber" placeholder="请输入条码号" allow-clear />
						</a-form-item>
					</a-col>
					<a-col :span="8">
						<a-form-item label="联网状态" name="netStatus">
							<a-select v-model:value="formData.netStatus" placeholder="请选择联网状态"
								:options="netStatusOptions" />
						</a-form-item>
					</a-col>
					<a-col :span="8">
						<a-form-item label="使用性质" name="useCharacter">
							<a-input v-model:value="formData.useCharacter" placeholder="请输入使用性质" allow-clear />
						</a-form-item>
					</a-col>
				</a-row>
			</a-card>

			<!-- 联系人信息 -->
			<a-card title="联系人信息" class="form-card" :bordered="false">
				<a-row :gutter="24">
					<a-col :span="12">
						<a-form-item label="联系人">
							<a-input v-model:value="formData.contactName" placeholder="请输入联系人" allow-clear />
						</a-form-item>
					</a-col>
					<a-col :span="12">
						<a-form-item label="联系电话">
							<a-input v-model:value="formData.contactPhone" placeholder="请输入联系电话" allow-clear />
						</a-form-item>
					</a-col>
				</a-row>
			</a-card>

			<!-- 所有人信息 -->
			<a-card title="所有人信息" class="form-card" :bordered="false">
				<a-row :gutter="24">
					<a-col :span="12">
						<a-form-item label="所有人" name="name">
							<a-input v-model:value="formData.name" placeholder="请输入所有人" allow-clear />
						</a-form-item>
					</a-col>
					<a-col :span="12">
						<a-form-item label="住址" name="address">
							<a-input v-model:value="formData.address" placeholder="请输入住址" allow-clear />
						</a-form-item>
					</a-col>
				</a-row>
			</a-card>

			<!-- 车辆参数 -->
			<a-card title="车辆参数" class="form-card" :bordered="false">
				<a-row :gutter="16">
					<a-col :span="8">
						<a-form-item label="核定载人数" name="approvedPassengers">
							<a-input v-model:value="formData.approvedPassengers" placeholder="请输入核定载人数" allow-clear />
						</a-form-item>
					</a-col>
					<a-col :span="8">
						<a-form-item label="总质量(kg)" name="grossMass">
							<a-input v-model:value="formData.grossMass" placeholder="请输入总质量" allow-clear />
						</a-form-item>
					</a-col>
					<a-col :span="8">
						<a-form-item label="整备质量(kg)" name="unladenMass">
							<a-input v-model:value="formData.unladenMass" placeholder="请输入整备质量" allow-clear />
						</a-form-item>
					</a-col>
					<a-col :span="8">
						<a-form-item label="品牌型号" name="model">
							<a-input v-model:value="formData.model" placeholder="请输入品牌型号" allow-clear />
						</a-form-item>
					</a-col>
					<a-col :span="8">
						<a-form-item label="发动机号码" name="engineNo">
							<a-input v-model:value="formData.engineNo" placeholder="请输入发动机号码" allow-clear />
						</a-form-item>
					</a-col>
					<a-col :span="8">
						<a-form-item label="核定载质量(kg)" name="approvedLoad">
							<a-input v-model:value="formData.approvedLoad" placeholder="请输入核定载质量" allow-clear />
						</a-form-item>
					</a-col>
				</a-row>
				<a-row :gutter="16">
					<a-col :span="8">
						<a-form-item label="外廓尺寸" name="dimension">
							<a-input v-model:value="formData.dimension" placeholder="请输入外廓尺寸" allow-clear />
						</a-form-item>
					</a-col>
					<a-col :span="8">
						<a-form-item label="准牵引总质量(kg)" name="tractionMass">
							<a-input v-model:value="formData.tractionMass" placeholder="请输入准牵引总质量" allow-clear />
						</a-form-item>
					</a-col>
				</a-row>
				<a-row :gutter="16">
					<a-col :span="12">
						<a-form-item label="备注" name="remarks">
							<a-input v-model:value="formData.remarks" placeholder="请输入备注" allow-clear />
						</a-form-item>
					</a-col>
					<a-col :span="12">
						<a-form-item label="检验记录" name="inspectionRecord">
							<a-input v-model:value="formData.inspectionRecord" placeholder="请输入检验记录" allow-clear />
						</a-form-item>
					</a-col>
				</a-row>
			</a-card>

			<!-- 其他信息 -->
			<a-card title="其他信息" class="form-card" :bordered="false">
				<a-row :gutter="16">
					<a-col :span="8">
						<a-form-item label="注册日期" name="registerDate">
							<a-date-picker v-model:value="formData.registerDate" value-format="YYYY-MM-DD"
								placeholder="请选择注册日期" style="width: 100%" />
						</a-form-item>
					</a-col>
					<a-col :span="8">
						<a-form-item label="发证日期" name="issueDate">
							<a-date-picker v-model:value="formData.issueDate" value-format="YYYY-MM-DD"
								placeholder="请选择发证日期" style="width: 100%" />
						</a-form-item>
					</a-col>
				</a-row>
				<a-row :gutter="16">
					<a-col :span="24">
						<a-form-item label="发证机关" name="issuingAuthority">
							<a-input v-model:value="formData.issuingAuthority" placeholder="请输入发证机关" allow-clear />
						</a-form-item>
					</a-col>
				</a-row>
			</a-card>

			<!-- 图片上传 -->
			<a-card title="证件信息" class="form-card" :bordered="false">
				<template #extra>
					<a-radio-group v-model:value="formData.uploadMode" button-style="solid" size="small"
						@change="handleModeChange">
						<a-radio-button value="single">单页模式</a-radio-button>
						<a-radio-button value="double">双页模式</a-radio-button>
					</a-radio-group>
				</template>

				<!-- 双页模式显示 -->
				<div v-if="formData.uploadMode === 'double'">
					<a-form-item label="行驶证两页合照" name="drivingLicenseImgUrl">
						<div class="image-container">
							<div class="image-display-area" @click="handleLicenseClick('double')">
								<template v-if="formData.drivingLicenseImgUrl">
									<div class="image-wrapper">
										<a-image :src="formData.drivingLicenseImgUrl" :preview="{
											src: formData.drivingLicenseImgUrl
										}" alt="行驶证两页合照" class="displayed-image" />
										<div class="custom-overlay">
											<div class="image-hover-actions">
												<eye-outlined class="hover-icon preview-icon"
													@click.stop="(e) => handlePreview(e, formData.drivingLicenseImgUrl)" />
												<delete-outlined class="hover-icon delete-icon"
													@click.stop="(e) => handleLicenseDelete(e, 'double')" />
												<scan-outlined v-if="licenseDoubleFile" class="hover-icon scan-icon"
													@click.stop="(e) => handleLicenseScan(e)" />
											</div>
										</div>
									</div>
								</template>
								<div v-else class="empty-image-placeholder">
									<upload-outlined />
									<div class="placeholder-text">点击上传行驶证两页合照</div>
								</div>
							</div>
						</div>
					</a-form-item>
				</div>

				<!-- 单页模式显示 -->
				<div v-else>
					<a-row :gutter="16">
						<a-col :span="12">
							<a-form-item label="行驶证首页" name="drivingLicenseFrontImgUrl">
								<div class="image-container">
									<div class="image-display-area" @click="handleLicenseClick('front')">
										<template v-if="formData.drivingLicenseFrontImgUrl">
											<div class="image-wrapper">
												<a-image :src="formData.drivingLicenseFrontImgUrl" :preview="{
													src: formData.drivingLicenseFrontImgUrl
												}" alt="行驶证首页" class="displayed-image" />
												<div class="custom-overlay">
													<div class="image-hover-actions">
														<eye-outlined class="hover-icon preview-icon"
															@click.stop="(e) => handlePreview(e, formData.drivingLicenseFrontImgUrl)" />
														<delete-outlined class="hover-icon delete-icon"
															@click.stop="(e) => handleLicenseDelete(e, 'front')" />
													</div>
												</div>
											</div>
										</template>
										<div v-else class="empty-image-placeholder">
											<upload-outlined />
											<div class="placeholder-text">点击上传行驶证首页</div>
										</div>
									</div>
								</div>
							</a-form-item>
						</a-col>

						<a-col :span="12">
							<a-form-item label="行驶证副页" name="drivingLicenseBackImgUrl">
								<div class="image-container">
									<div class="image-display-area" @click="handleLicenseClick('back')">
										<template v-if="formData.drivingLicenseBackImgUrl">
											<div class="image-wrapper">
												<a-image :src="formData.drivingLicenseBackImgUrl" :preview="{
													src: formData.drivingLicenseBackImgUrl
												}" alt="行驶证副页" class="displayed-image" />
												<div class="custom-overlay">
													<div class="image-hover-actions">
														<eye-outlined class="hover-icon preview-icon"
															@click.stop="(e) => handlePreview(e, formData.drivingLicenseBackImgUrl)" />
														<delete-outlined class="hover-icon delete-icon"
															@click.stop="(e) => handleLicenseDelete(e, 'back')" />
														<scan-outlined v-if="licenseFrontFile && licenseBackFile"
															class="hover-icon scan-icon"
															@click.stop="(e) => handleLicenseScan(e)" />
													</div>
												</div>
											</div>
										</template>
										<div v-else class="empty-image-placeholder">
											<upload-outlined />
											<div class="placeholder-text">点击上传行驶证副页</div>
										</div>
									</div>
								</div>
							</a-form-item>
						</a-col>
					</a-row>

					<!-- 单页模式识别按钮 -->
					<div class="recognition-action"
						v-if="formData.drivingLicenseFrontImgUrl && formData.drivingLicenseBackImgUrl">
						<a-button type="primary" @click="recognizeLicense" :loading="recognizeLoading">
							<template #icon><scan-outlined /></template>
							识别图片
						</a-button>
					</div>
				</div>

				<!-- 车头照片上传 -->
				<a-form-item label="车头照片" name="carImgUrl">
					<div class="image-container">
						<div class="image-display-area" @click="handleImageClick('carImgUrl')">
							<template v-if="formData.carImgUrl">
								<div class="image-wrapper">
									<a-image :src="formData.carImgUrl" :preview="{
										src: formData.carImgUrl
									}" alt="车头照片" class="displayed-image" />
									<div class="custom-overlay">
										<div class="image-hover-actions">
											<eye-outlined class="hover-icon preview-icon"
												@click.stop="(e) => handlePreview(e, formData.carImgUrl)" />
											<delete-outlined class="hover-icon delete-icon"
												@click.stop="(e) => handleImageDelete(e, 'carImgUrl')" />
										</div>
									</div>
								</div>
							</template>
							<div v-else class="empty-image-placeholder">
								<upload-outlined />
								<div class="placeholder-text">点击上传车头照片</div>
							</div>
						</div>
					</div>
				</a-form-item>

				<!-- 环保清单图片上传 -->
				<a-form-item label="环保清单纸质图片" name="environmentalListImgUrl">
					<div class="image-container">
						<div class="image-display-area" @click="handleImageClick('environmentalListImgUrl')">
							<template v-if="formData.environmentalListImgUrl">
								<div class="image-wrapper">
									<a-image :src="formData.environmentalListImgUrl" :preview="{
										src: formData.environmentalListImgUrl
									}" alt="环保清单纸质图片" class="displayed-image" />
									<div class="custom-overlay">
										<div class="image-hover-actions">
											<eye-outlined class="hover-icon preview-icon"
												@click.stop="(e) => handlePreview(e, formData.environmentalListImgUrl)" />
											<delete-outlined class="hover-icon delete-icon"
												@click.stop="(e) => handleImageDelete(e, 'environmentalListImgUrl')" />
										</div>
									</div>
								</div>
							</template>
							<div v-else class="empty-image-placeholder">
								<upload-outlined />
								<div class="placeholder-text">点击上传环保清单纸质图片</div>
							</div>
						</div>
					</div>
				</a-form-item>

				<!-- 环保清单电子图片上传 -->
				<a-form-item name="environmentalListElectronicImgUrl">
					<template #label>
						<div class="custom-label">
							<span>环保清单电子图片</span>
							<a-button type="primary" size="small" @click="openEnvironmentalQuery"
								:disabled="!formData.vin" class="label-button">
								<template #icon><search-outlined /></template>
								环保清单电子截图查询
							</a-button>
						</div>
					</template>
					<div class="image-container">
						<div class="image-display-area" @click="handleImageClick('environmentalListElectronicImgUrl')">
							<template v-if="formData.environmentalListElectronicImgUrl">
								<div class="image-wrapper">
									<a-image :src="formData.environmentalListElectronicImgUrl" :preview="{
										src: formData.environmentalListElectronicImgUrl
									}" alt="环保清单电子图片" class="displayed-image" />
									<div class="custom-overlay">
										<div class="image-hover-actions">
											<eye-outlined class="hover-icon preview-icon"
												@click.stop="(e) => handlePreview(e, formData.environmentalListElectronicImgUrl)" />
											<delete-outlined class="hover-icon delete-icon"
												@click.stop="(e) => handleImageDelete(e, 'environmentalListElectronicImgUrl')" />
										</div>
									</div>
								</div>
							</template>
							<div v-else class="empty-image-placeholder">
								<upload-outlined />
								<div class="placeholder-text">点击上传环保清单电子图片</div>
							</div>
						</div>
					</div>
				</a-form-item>

				<!-- 重型柴油车排放阶段查询截图上传 -->
				<a-form-item name="heavyVehicleEmissionStageImgUrl">
					<template #label>
						<div class="custom-label">
							<span>重型柴油车排放阶段电子图片</span>
							<a-button type="primary" size="small" @click="openEmissionStageQuery" class="label-button">
								<template #icon><search-outlined /></template>
								重型柴油车排放阶段截图查询
							</a-button>
						</div>
					</template>
					<div class="image-container">
						<div class="image-display-area" @click="handleImageClick('heavyVehicleEmissionStageImgUrl')">
							<template v-if="formData.heavyVehicleEmissionStageImgUrl">
								<div class="image-wrapper">
									<a-image :src="formData.heavyVehicleEmissionStageImgUrl" :preview="{
										src: formData.heavyVehicleEmissionStageImgUrl
									}" alt="重型柴油车排放阶段电子图片" class="displayed-image" />
									<div class="custom-overlay">
										<div class="image-hover-actions">
											<eye-outlined class="hover-icon preview-icon"
												@click.stop="(e) => handlePreview(e, formData.heavyVehicleEmissionStageImgUrl)" />
											<delete-outlined class="hover-icon delete-icon"
												@click.stop="(e) => handleImageDelete(e, 'heavyVehicleEmissionStageImgUrl')" />
										</div>
									</div>
								</div>
							</template>
							<div v-else class="empty-image-placeholder">
								<upload-outlined />
								<div class="placeholder-text">点击上传重型柴油车排放阶段电子图片</div>
							</div>
						</div>
					</div>
				</a-form-item>
			</a-card>

			<!-- 组织信息 -->
			<a-card title="对接部门" class="form-card" :bordered="false">
				<a-row :gutter="16">
					<a-col :span="24">
						<a-form-item label="对接部门" name="orgId">
							<a-input-group compact>
								<a-input v-model:value="orgNames" placeholder="请选择对接部门" style="width: calc(100% - 40px)"
									readOnly />
								<a-button type="primary" @click="showOrgSelect" style="width: 40px">
									<template #icon><search-outlined /></template>
								</a-button>
							</a-input-group>
						</a-form-item>
					</a-col>
				</a-row>
			</a-card>

			<!-- 添加组织选择组件 -->
			<OrgSelect v-model:visible="orgSelectVisible" title="选择对接部门"
				:value="formData.orgId ? formData.orgId.split(',') : []" :multiple="false" @confirm="handleOrgSelect" />
		</a-form>
		<template #footer>
			<a-button style="margin-right: 8px" @click="onClose">关闭</a-button>
			<a-button type="primary" @click="onSubmit" :loading="submitLoading">保存</a-button>
		</template>
	</xn-form-container>

</template>

<script setup name="vehicleForm">
import tool from '@/utils/tool'
import { cloneDeep } from 'lodash-es'
import vehicleApi from '@/api/biz/vehicleApi'
import fileApi from '@/api/dev/fileApi'
import OrgSelect from '../components/OrgSelect.vue'
import { SearchOutlined, PlusOutlined, EyeOutlined, MinusOutlined, CarOutlined, UploadOutlined, DeleteOutlined, ScanOutlined } from '@ant-design/icons-vue'
import { ref, computed, watch, nextTick } from 'vue'
import { message, Image, Modal } from 'ant-design-vue'
import { validateForm } from '@/utils/formUtils'
import vehicleLicenseUtils from '@/utils/vehicleLicenseUtils'

// 抽屉状态
const visible = ref(false)
// 添加最小化状态
const isMinimized = ref(false)
const emit = defineEmits({ successful: null })
const formRef = ref()
// 表单数据
const initFormData = () => {
	return {
		drivingLicenseImgUrl: '',
		drivingLicenseFrontImgUrl: '',
		drivingLicenseBackImgUrl: '',
		carImgUrl: '',
		environmentalListImgUrl: '',
		environmentalListElectronicImgUrl: '',
		heavyVehicleEmissionStageImgUrl: '',
		uploadMode: 'single', // 默认为单页识别模式
		verifyStatus: '0',     // 确保默认为未审核状态
		netStatus: '1',       // 默认联网状态为已联网
	}
}
const formData = ref(initFormData())
const submitLoading = ref(false)
const netStatusOptions = ref([])
const plateColorOptions = ref([])
const verifyStatusOptions = ref([])
const emissionStageOptions = ref([])

// 组织机构选择相关
const orgSelectVisible = ref(false)
const orgNames = ref('')
const selectedOrgRows = ref([])

// 环保清单上传相关
const uploadAction = ''  // 置空，因为我们将使用自定义上传方法
const uploadHeaders = computed(() => ({
	token: tool.data.get('TOKEN')
}))

// 环保清单电子截图查询相关

// 打开环保清单电子截图查询
const openEnvironmentalQuery = () => {
	if (!formData.value.vin) {
		message.warning('请先填写车辆识别代号(VIN码)')
		return
	}
	const url = `http://xxgk.vecc.org.cn:8090/vin/${formData.value.vin}`
	window.open(url, '_blank')
}

// 打开重型柴油车排放阶段查询
const openEmissionStageQuery = () => {
	const url = 'http://hdvquery.vecc.org.cn/'
	window.open(url, '_blank')
}

// 创建隐藏的文件上传输入
const fileInput = ref(null)
// 当前操作的字段名
const currentField = ref('')

// 创建隐藏的文件上传输入元素
onMounted(() => {
	const input = document.createElement('input')
	input.type = 'file'
	input.accept = 'image/*'
	input.style.display = 'none'
	input.addEventListener('change', handleFileSelected)
	document.body.appendChild(input)
	fileInput.value = input
})

// 点击图片区域处理
const handleImageClick = (field) => {
	if (formData.value[field]) {
		// 如果已有图片，不触发上传
		return
	}
	currentField.value = field
	fileInput.value.click()
}

// 文件选择后处理
const handleFileSelected = async (event) => {
	const file = event.target.files[0]
	if (!file) return

	// 验证文件类型
	const isImage = file.type.startsWith('image/')
	if (!isImage) {
		message.error('请选择图片文件')
		fileInput.value.value = null
		return
	}

	// 验证文件大小 (限制为2MB)
	if (file.size > 2 * 1024 * 1024) {
		message.error('图片大小不能超过2MB')
		fileInput.value.value = null
		return
	}

	try {
		const uploadData = new FormData()
		uploadData.append('file', file)

		// 显示上传中提示
		const uploadKey = `${currentField.value}Upload`
		message.loading({ content: '图片上传中...', key: uploadKey })

		// 上传图片
		const result = await fileApi.fileUploadLocalReturnUrl(uploadData)
		if (result) {
			// 更新表单数据
			formData.value[currentField.value] = result
			message.success({ content: '上传成功', key: uploadKey })
		} else {
			message.error({ content: '上传失败', key: uploadKey })
		}
	} catch (error) {
		console.error('上传图片失败:', error)
		message.error('上传失败')
	} finally {
		// 重置文件输入
		fileInput.value.value = null
	}
}

// 手动预览图片
const handlePreview = (e, url) => {
	e.stopPropagation()
	const image = e.target.closest('.image-wrapper').querySelector('.ant-image')
	if (image) {
		image.click()
	}
}

// 删除图片
const handleImageDelete = (e, field) => {
	e.stopPropagation()

	// 显示确认对话框
	Modal.confirm({
		title: '确认删除',
		content: '确定要删除该图片吗？',
		okText: '确认',
		cancelText: '取消',
		onOk: () => {
			// 删除图片
			formData.value[field] = ''
			message.success('已删除图片')
		}
	})
}

// 组件销毁时移除文件输入
onUnmounted(() => {
	if (fileInput.value) {
		fileInput.value.removeEventListener('change', handleFileSelected)
		document.body.removeChild(fileInput.value)
	}
})

// 自定义上传处理
const customUploadEnvironmental = (options, type = 'paper') => {
	const { file } = options;

	// 验证文件类型和大小
	const isValidFile = beforeUpload(file);
	if (!isValidFile) {
		return;
	}

	// 显示上传中提示
	const uploadKey = `env${type}Upload`;
	const typeText = type === 'paper' ? '纸质' : '电子';
	message.loading({ content: `环保清单${typeText}图片上传中...`, key: uploadKey });

	// 准备表单数据
	const uploadFormData = new FormData();
	uploadFormData.append('file', file);

	// 调用API进行上传
	fileApi.fileUploadLocalReturnUrl(uploadFormData)
		.then(url => {
			console.log(url)
			// 关闭加载提示
			message.destroy(uploadKey);

			if (url) {
				// 更新表单数据
				if (type === 'paper') {
					formData.value.environmentalListImgUrl = url;
				} else {
					formData.value.environmentalListElectronicImgUrl = url;
				}
				// 显示成功提示
				message.success(`环保清单${typeText}图片上传成功`);
			}
		})
		.catch(error => {
			// 关闭加载提示
			message.destroy(uploadKey);
			// 显示错误提示
			console.error('上传异常:', error);
		});
};

// 自定义上传重型柴油车排放阶段查询截图
const customUploadEmissionStage = (options) => {
	const { file } = options;

	// 验证文件类型和大小
	const isValidFile = beforeUpload(file);
	if (!isValidFile) {
		return;
	}

	// 显示上传中提示
	const uploadKey = 'emissionStageUpload';
	message.loading({ content: '重型柴油车排放阶段查询截图上传中...', key: uploadKey });

	// 准备表单数据
	const uploadFormData = new FormData();
	uploadFormData.append('file', file);

	// 调用API进行上传
	fileApi.fileUploadLocalReturnUrl(uploadFormData)
		.then(url => {
			console.log(url)
			// 关闭加载提示
			message.destroy(uploadKey);

			if (url) {
				// 更新表单数据
				formData.value.heavyVehicleEmissionStageImgUrl = url;
				// 显示成功提示
				message.success('重型柴油车排放阶段查询截图上传成功');
			}
		})
		.catch(error => {
			// 关闭加载提示
			message.destroy(uploadKey);
			// 显示错误提示
			console.error('上传异常:', error);
		});
};

// 上传前校验
const beforeUpload = (file) => {
	const isImage = file.type.startsWith('image/')
	if (!isImage) {
		message.error('只能上传图片文件!')
		return false
	}

	const isLt2M = file.size / 1024 / 1024 < 2
	if (!isLt2M) {
		message.error('图片大小不能超过2MB!')
		return false
	}

	return true
}

// 重新上传环保清单图片
const reuploadEnvironmental = (type = 'paper') => {
	if (type === 'paper') {
		formData.value.environmentalListImgUrl = ''
	} else {
		formData.value.environmentalListElectronicImgUrl = ''
	}
}

// 删除环保清单图片
const removeEnvironmentalImage = (type = 'paper') => {
	if (type === 'paper') {
		formData.value.environmentalListImgUrl = ''
	} else {
		formData.value.environmentalListElectronicImgUrl = ''
	}
}

// 重新上传重型柴油车排放阶段查询截图
const reuploadEmissionStage = () => {
	formData.value.heavyVehicleEmissionStageImgUrl = ''
}

// 删除重型柴油车排放阶段查询截图
const removeEmissionStageImage = () => {
	formData.value.heavyVehicleEmissionStageImgUrl = ''
}

// 添加上传文件相关的状态变量
const recognizeLoading = ref(false)
const licenseFrontFile = ref(null)
const licenseBackFile = ref(null)
const licenseDoubleFile = ref(null)

// 打开行驶证上传识别组件
const openLicenseUpload = () => {
	licenseUploadRef.value.onOpen()
}

// 行驶证上传前校验
const beforeUploadLicense = (file) => {
	return vehicleLicenseUtils.beforeUpload(file)
}

// 处理行驶证图片上传
const handleLicenseUploadChange = async (info, type) => {
	if (!info.file || !info.file.originFileObj) return

	const file = info.file.originFileObj
	const messageKey = `license_${type}`

	try {
		let fileTypeText = ''
		if (type === 'front') fileTypeText = '行驶证首页'
		else if (type === 'back') fileTypeText = '行驶证副页'
		else fileTypeText = '行驶证两页图片'

		// 上传图片获取URL
		const url = await vehicleLicenseUtils.uploadImage(file, messageKey, fileTypeText)

		// 更新对应的图片URL和文件对象
		if (type === 'front') {
			formData.value.drivingLicenseFrontImgUrl = url
			licenseFrontFile.value = file
		} else if (type === 'back') {
			formData.value.drivingLicenseBackImgUrl = url
			licenseBackFile.value = file
		} else if (type === 'double') {
			formData.value.drivingLicenseImgUrl = url
			licenseDoubleFile.value = file
		}
	} catch (error) {
		console.error('上传失败:', error)
	}
}

// 点击行驶证图片区域
const handleLicenseClick = (type) => {
	// 如果已有图片，不触发上传
	if ((type === 'front' && formData.value.drivingLicenseFrontImgUrl) ||
		(type === 'back' && formData.value.drivingLicenseBackImgUrl) ||
		(type === 'double' && formData.value.drivingLicenseImgUrl)) {
		return
	}

	// 模拟点击上传按钮
	const fileInput = document.createElement('input')
	fileInput.type = 'file'
	fileInput.accept = 'image/*'
	fileInput.style.display = 'none'
	fileInput.addEventListener('change', (event) => {
		const file = event.target.files[0]
		if (file) {
			// 验证文件
			if (vehicleLicenseUtils.beforeUpload(file)) {
				// 创建一个模拟的上传事件对象
				const info = {
					file: {
						originFileObj: file
					}
				}
				// 调用原有的上传处理函数
				handleLicenseUploadChange(info, type)
			}
		}
		// 清理临时创建的元素
		document.body.removeChild(fileInput)
	})
	document.body.appendChild(fileInput)
	fileInput.click()
}

// 处理行驶证扫描识别
const handleLicenseScan = (e) => {
	e.stopPropagation()
	// 调用原有的识别函数
	recognizeLicense()
}

// 删除行驶证图片
const handleLicenseDelete = (e, type) => {
	e.stopPropagation()

	// 显示确认对话框
	Modal.confirm({
		title: '确认删除',
		content: '确定要删除该图片吗？',
		okText: '确认',
		cancelText: '取消',
		onOk: () => {
			// 删除图片
			if (type === 'front') {
				formData.value.drivingLicenseFrontImgUrl = ''
				licenseFrontFile.value = null
			} else if (type === 'back') {
				formData.value.drivingLicenseBackImgUrl = ''
				licenseBackFile.value = null
			} else if (type === 'double') {
				formData.value.drivingLicenseImgUrl = ''
				licenseDoubleFile.value = null
			}
			message.success('已删除图片')
		}
	})
}

// 重新上传行驶证图片 (保留原有函数作为兼容处理)
const reuploadLicense = (type) => {
	if (type === 'front') {
		formData.value.drivingLicenseFrontImgUrl = ''
		licenseFrontFile.value = null
	} else if (type === 'back') {
		formData.value.drivingLicenseBackImgUrl = ''
		licenseBackFile.value = null
	} else if (type === 'double') {
		formData.value.drivingLicenseImgUrl = ''
		licenseDoubleFile.value = null
	}
}

// 识别行驶证
const recognizeLicense = async () => {
	// 检查是否有图片
	if (formData.value.uploadMode === 'single') {
		if (!licenseFrontFile.value || !licenseBackFile.value) {
			message.warning('请先上传行驶证首页和副页图片')
			return
		}
	} else {
		if (!licenseDoubleFile.value) {
			message.warning('请先上传行驶证图片')
			return
		}
	}

	recognizeLoading.value = true
	try {
		// 准备识别参数
		const params = {
			mode: formData.value.uploadMode,
			frontFile: licenseFrontFile.value,
			backFile: licenseBackFile.value,
			doubleFile: licenseDoubleFile.value,
			frontUrl: formData.value.drivingLicenseFrontImgUrl,
			backUrl: formData.value.drivingLicenseBackImgUrl,
			doubleUrl: formData.value.drivingLicenseImgUrl
		}

		// 识别行驶证
		const result = await vehicleLicenseUtils.recognizeLicense(params)

		// 更新表单数据
		if (result) {
			// 保留图片URL信息
			const imageData = {
				uploadMode: formData.value.uploadMode,
				drivingLicenseFrontImgUrl: formData.value.drivingLicenseFrontImgUrl,
				drivingLicenseBackImgUrl: formData.value.drivingLicenseBackImgUrl,
				drivingLicenseImgUrl: formData.value.drivingLicenseImgUrl
			}

			// 合并识别结果和图片信息
			const mergedData = { ...result, ...imageData }

			// 设置表单数据
			Object.keys(mergedData).forEach(key => {
				if (key in formData.value) {
					formData.value[key] = mergedData[key]
				}
			})

			message.success('行驶证识别成功')
		}
	} catch (error) {
		console.error('识别失败:', error)
	} finally {
		recognizeLoading.value = false
	}
}

// 打开抽屉
const onOpen = (record) => {
	visible.value = true
	isMinimized.value = false

	// 重置为初始值
	formData.value = initFormData()

	// 重置文件对象
	licenseFrontFile.value = null
	licenseBackFile.value = null
	licenseDoubleFile.value = null

	if (record) {
		let recordData = cloneDeep(record)
		formData.value = Object.assign(formData.value, recordData)

		// 根据现有图片判断上传模式
		if (recordData.drivingLicenseImgUrl) {
			formData.value.uploadMode = 'double'
		} else if (recordData.drivingLicenseFrontImgUrl || recordData.drivingLicenseBackImgUrl) {
			formData.value.uploadMode = 'single'
		}

		// 设置组织机构名称
		orgNames.value = record.orgName || ''
	} else {
		orgNames.value = ''
		// 确保新记录使用默认的单页模式
		formData.value.uploadMode = 'single'
		// 确保联网状态默认为已联网
		formData.value.netStatus = '1'
	}

	netStatusOptions.value = tool.dictList('NET_STATUS')
	plateColorOptions.value = tool.dictList('PLATE_COLOR')
	verifyStatusOptions.value = tool.dictList('VERIFY_STATUS')
	emissionStageOptions.value = tool.dictList('EMISSION_STAGE')

	// 输出表单数据以便调试
	console.log('表单数据:', formData.value)
}

// 显示组织机构选择
const showOrgSelect = () => {
	orgSelectVisible.value = true
}

// 处理组织机构选择结果
const handleOrgSelect = (keys, rows) => {
	// 判断是单选还是多选模式
	if (Array.isArray(keys)) {
		// 多选模式
		selectedOrgRows.value = rows
		formData.value.orgId = keys.join(',')
		orgNames.value = rows.map(item => item.name).join(', ')
	} else {
		// 单选模式
		selectedOrgRows.value = [rows]
		formData.value.orgId = keys
		orgNames.value = rows.name
	}
}

// 关闭抽屉
const onClose = () => {
	formRef.value.resetFields()
	formData.value = initFormData()
	visible.value = false
	isMinimized.value = false
	orgNames.value = ''
	selectedOrgRows.value = []
}

// 行驶证验证函数 - 根据上传模式验证不同的字段
const validateDrivingLicense = (rule, value, callback) => {
	// 获取当前字段名
	const fieldName = rule.field;

	// 双页模式 - 验证两页合照
	if (formData.value.uploadMode === 'double') {
		// 只验证 drivingLicenseImgUrl 字段
		if (fieldName === 'drivingLicenseImgUrl' && !formData.value.drivingLicenseImgUrl) {
			callback(new Error('请上传行驶证两页合照'));
		} else {
			callback();
		}
	}
	// 单页模式 - 验证首页和副页
	else if (formData.value.uploadMode === 'single') {
		// 验证首页
		if (fieldName === 'drivingLicenseFrontImgUrl' && !formData.value.drivingLicenseFrontImgUrl) {
			callback(new Error('请上传行驶证首页'));
		}
		// 验证副页
		// else if (fieldName === 'drivingLicenseBackImgUrl' && !formData.value.drivingLicenseBackImgUrl) {
		// 	callback(new Error('请上传行驶证副页'));
		// }
		 else {
			callback();
		}
	} else {
		callback();
	}
};

// 默认要校验的
const formRules = {
	plateColor: [{ required: true, message: '请选择车牌颜色', trigger: 'change' }],
	emissionStage: [{ required: true, message: '请选择排放阶段', trigger: 'change' }],
	orgId: [{ required: true, message: '请选择对接部门', trigger: 'change' }],
	environmentalListImgUrl: [{ required: true, message: '请上传环保清单纸质图片', trigger: 'change' }],
	contact_name: [{ required: true, message: '请输入联系人', trigger: 'change' }],
	contact_phone: [{ required: true, message: '请输入联系电话', trigger: 'change' }],
	// environmentalListElectronicImgUrl: [{ required: true, message: '请上传环保清单电子图片', trigger: 'change' }],
	// heavyVehicleEmissionStageImgUrl: [{ required: true, message: '请上传重型柴油车排放阶段查询截图', trigger: 'change' }],

	// 行驶证验证 - 使用自定义验证函数
	drivingLicenseImgUrl: [{ validator: validateDrivingLicense, trigger: 'change' }],
	drivingLicenseFrontImgUrl: [{ validator: validateDrivingLicense, trigger: 'change' }],
	drivingLicenseBackImgUrl: [{ validator: validateDrivingLicense, trigger: 'change' }]
}

// 修改 onSubmit 方法，确保 verifyStatus 被包含在提交数据中
const onSubmit = () => {
	// 在提交前手动验证行驶证字段
	if (formData.value.uploadMode === 'single') {
		// 单页模式需要验证首页和副页
		if (!formData.value.drivingLicenseFrontImgUrl) {
			message.error('请上传行驶证首页')
			return
		}
		// if (!formData.value.drivingLicenseBackImgUrl) {
		// 	message.error('请上传行驶证副页')
		// 	return
		// }
	} else if (formData.value.uploadMode === 'double') {
		// 双页模式只验证两页合照
		if (!formData.value.drivingLicenseImgUrl) {
			message.error('请上传行驶证两页合照')
			return
		}
	}

	validateForm(formRef.value, () => {
		submitLoading.value = true
		const formDataParam = cloneDeep(formData.value)

		// 确保新增数据包含审核状态
		if (!formDataParam.id && !formDataParam.verifyStatus) {
			formDataParam.verifyStatus = '0' // 新增数据默认未审核
		}

		if (formDataParam.plateColor) {
			const plateColorOption = plateColorOptions.value.find(option => option.value === formDataParam.plateColor)
			if (plateColorOption) {
				formDataParam.plateColorName = plateColorOption.label
			}
		}

		console.log('提交的数据:', formDataParam)

		vehicleApi
			.vehicleSubmitForm(formDataParam, formDataParam.id)
			.then(() => {
				onClose()
				emit('successful')
			})
			.finally(() => {
				submitLoading.value = false
			})
	})
}

// 设置表单数据(供外部组件调用)
const setFormData = (data) => {
	if (data) {
		// 设置表单数据
		formData.value = { ...data }
	}
}

// 预览环保清单图片
const previewEnvironmentalFile = (file) => {
	return new Promise((resolve) => {
		// 如果文件已经有URL，直接返回
		if (file.url) {
			resolve(file.url);
			return;
		}

		// 否则读取文件内容
		const reader = new FileReader();
		reader.readAsDataURL(file);
		reader.onload = () => {
			resolve(reader.result);
		};
	});
};

// 点击预览图片（如果需要自定义预览逻辑）
const previewImage = (src) => {
	// 使用Image.preview方法
	Image.preview({
		src: src,
		mask: '点击关闭',
		current: 0
	});
};

// 最小化表单
const minimizeForm = () => {
	isMinimized.value = true
}

// 恢复表单
const restoreForm = () => {
	isMinimized.value = false
}

// 添加模式切换处理函数
const handleModeChange = (e) => {
	const newMode = e.target.value

	// 切换模式时清除另一种模式的数据
	if (newMode === 'single') {
		// 切换到单页模式，清除双页数据
		formData.value.drivingLicenseImgUrl = ''
		licenseDoubleFile.value = null
	} else {
		// 切换到双页模式，清除单页数据
		formData.value.drivingLicenseFrontImgUrl = ''
		formData.value.drivingLicenseBackImgUrl = ''
		licenseFrontFile.value = null
		licenseBackFile.value = null
	}

	// 在模式切换后触发验证
	nextTick(() => {
		// 验证行驶证字段
		if (formRef.value) {
			if (newMode === 'single') {
				// 单页模式需要验证首页和副页
				formRef.value.validateFields(['drivingLicenseFrontImgUrl', 'drivingLicenseBackImgUrl'])
			} else {
				// 双页模式只验证两页合照
				formRef.value.validateFields(['drivingLicenseImgUrl'])
			}
		}
	})
}

// 抛出函数
defineExpose({
	onOpen,
	setFormData,
	formData
})
</script>

<style scoped>
.form-card {
	margin-bottom: 16px;
}

.form-card :deep(.ant-card-head) {
	min-height: 40px;
	padding: 0 12px;
	background-color: #f5f7fa;
}

.form-card :deep(.ant-card-head-title) {
	padding: 8px 0;
	font-size: 14px;
	font-weight: 500;
}

.form-card :deep(.ant-card-body) {
	padding: 16px;
}

.image-preview {
	width: 100%;
	border: 1px dashed #d9d9d9;
	border-radius: 4px;
	padding: 8px;
	text-align: center;
}

.preview-image {
	max-width: 100%;
	max-height: 200px;
	cursor: pointer;
}

.preview-mask {
	font-size: 14px;
	font-weight: bold;
}

.mt-2 {
	margin-top: 8px;
}

.image-preview :deep(.ant-image) {
	display: inline-block;
}

.image-preview :deep(.ant-image-img) {
	max-width: 100%;
	max-height: 200px;
	object-fit: contain;
}

.image-preview :deep(.ant-image-mask) {
	display: flex;
	align-items: center;
	justify-content: center;
}

.no-image {
	width: 100%;
	height: 100px;
	border: 1px dashed #d9d9d9;
	border-radius: 4px;
	display: flex;
	justify-content: center;
	align-items: center;
	color: #999;
}

.image-preview :deep(.ant-image-mask-info) {
	display: none;
	/* 隐藏默认的预览提示 */
}

.image-preview :deep(.ant-image-mask):after {
	content: '点击预览';
	display: inline-block;
	color: #fff;
	font-size: 14px;
	font-weight: bold;
}

/* 添加最小化相关样式 */
.minimize-btn-container {
	display: flex;
	justify-content: flex-end;
	padding: 0 0 8px 0;
	margin-top: -8px;
}

.minimize-button {
	font-size: 12px;
	padding: 0 4px;
}

.form-minimized-btn {
	position: fixed;
	bottom: 20px;
	right: 20px;
	background-color: #1890ff;
	color: white;
	padding: 8px 16px;
	border-radius: 24px;
	cursor: pointer;
	display: flex;
	align-items: center;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
	z-index: 1000;
	transition: all 0.3s;
}

.form-minimized-btn:hover {
	background-color: #40a9ff;
	transform: translateY(-2px);
}

.minimize-text {
	margin-left: 8px;
	max-width: 150px;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

/* 添加新的样式 */
.vehicle-form-container {
	position: relative;
	/* 确保内部绝对定位元素相对于它定位 */
}

.title-minimize-btn {
	position: absolute;
	top: 16px;
	/* 根据你的UI调整位置 */
	right: 50px;
	/* 位于关闭按钮左侧 */
	width: 32px;
	height: 32px;
	display: flex;
	justify-content: center;
	align-items: center;
	cursor: pointer;
	z-index: 10;
	color: rgba(0, 0, 0, 0.45);
	border-radius: 4px;
	background-color: transparent;
	transition: all 0.3s;
}

.title-minimize-btn:hover {
	background-color: rgba(0, 0, 0, 0.06);
}

/* 添加错误高亮动画 */
@keyframes errorHighlight {

	0%,
	100% {
		box-shadow: 0 0 0 rgba(255, 77, 79, 0);
	}

	50% {
		box-shadow: 0 0 8px rgba(255, 77, 79, 0.8);
	}
}

/* 错误高亮类 */
:deep(.form-error-highlight) {
	animation: errorHighlight 1s ease-in-out 3;
}

/* 增强错误状态的表单项显示 */
:deep(.ant-form-item-has-error) {
	position: relative;
}

:deep(.ant-form-item-has-error .ant-form-item-label > label) {
	color: #ff4d4f;
	font-weight: 500;
}

.image-upload-container {
	width: 100%;
}

.upload-button-container {
	display: flex;
	justify-content: center;
	padding: 20px;
	border: 1px dashed #d9d9d9;
	border-radius: 4px;
	background-color: #fafafa;
	cursor: pointer;
	transition: border-color 0.3s;
}

.upload-button-container:hover {
	border-color: #1890ff;
}

.image-actions {
	display: flex;
	justify-content: center;
	gap: 8px;
	margin-top: 8px;
}

.recognition-action {
	display: flex;
	justify-content: center;
	margin-top: 16px;
}

.custom-label {
	display: flex;
	align-items: center;
}

.label-button {
	margin-left: 8px;
}

/* 新的图片上传区域样式 */
.image-container {
	display: flex;
	flex-direction: column;
	width: 100%;
	margin-bottom: 16px;
}

.image-display-area {
	width: 100%;
	height: 200px;
	background-color: #fafafa;
	border: 1px dashed #d9d9d9;
	border-radius: 4px;
	display: flex;
	justify-content: center;
	align-items: center;
	overflow: hidden;
	position: relative;
	transition: all 0.3s;
	cursor: pointer;
}

.image-display-area:hover {
	border-color: #1890ff;
}

.empty-image-placeholder {
	display: flex;
	flex-direction: column;
	align-items: center;
	color: #d9d9d9;
}

.placeholder-text {
	margin-top: 8px;
	font-size: 14px;
}

.image-wrapper {
	width: 100%;
	height: 100%;
	position: relative;
	display: flex;
	justify-content: center;
	align-items: center;
}

.displayed-image {
	max-width: 100%;
	max-height: 100%;
	object-fit: contain;
}

.custom-overlay {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background-color: rgba(0, 0, 0, 0.5);
	display: flex;
	justify-content: center;
	align-items: center;
	opacity: 0;
	transition: opacity 0.3s;
}

.image-wrapper:hover .custom-overlay {
	opacity: 1;
}

.image-hover-actions {
	display: flex;
	gap: 16px;
}

.hover-icon {
	font-size: 20px;
	color: white;
	cursor: pointer;
	transition: transform 0.2s;
}

.hover-icon:hover {
	transform: scale(1.2);
}

.preview-icon:hover {
	color: #1890ff;
}

.delete-icon:hover {
	color: #ff4d4f;
}

.scan-icon:hover {
	color: #52c41a;
}
</style>
