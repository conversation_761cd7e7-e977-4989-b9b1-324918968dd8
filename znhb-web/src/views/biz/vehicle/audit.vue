<template>
  <xn-form-container title="车辆信息审核" :width="800" :visible="visible" :destroy-on-close="true" @close="onClose">
    <a-form ref="formRef" :model="formData" layout="vertical">
      <!-- 基础信息卡片 -->
      <a-card title="基本信息" class="form-card" :bordered="false">
        <a-row :gutter="16">
          <a-col :span="8">
            <a-form-item label="车牌号码">
              <a-input v-model:value="formData.plateNumber" disabled />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="排放标准">
              <a-input v-model:value="emissionStageText" disabled />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="车牌颜色">
              <a-input v-model:value="plateColorText" disabled />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="车辆类型">
              <a-input v-model:value="formData.vehicleType" disabled />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="能源类型">
              <a-input v-model:value="formData.energyType" disabled />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="车辆识别代号">
              <a-input v-model:value="formData.vin" disabled />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="16">

          <a-col :span="8">
            <a-form-item label="品牌型号">
              <a-input v-model:value="formData.model" disabled />
            </a-form-item>
          </a-col>
          
          <a-col :span="8">
            <a-form-item label="条码号">
              <a-input v-model:value="formData.codeNumber" disabled />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="联网状态">
              <a-input v-model:value="netStatusText" disabled />
            </a-form-item>
          </a-col>
        </a-row>
      </a-card>

      <!-- 联系人信息 -->
      <a-card title="联系人信息" class="form-card" :bordered="false">
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="联系人">
              <a-input v-model:value="formData.contactName" disabled />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="联系电话">
              <a-input v-model:value="formData.contactPhone" disabled />
            </a-form-item>
          </a-col>
        </a-row>
      </a-card>

      <!-- 所有人信息 -->
      <a-card title="所有人信息" class="form-card" :bordered="false">
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="所有人">
              <a-input v-model:value="formData.name" disabled />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="使用性质">
              <a-input v-model:value="formData.useCharacter" disabled />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="16">
          <a-col :span="24">
            <a-form-item label="住址">
              <a-input v-model:value="formData.address" disabled />
            </a-form-item>
          </a-col>
        </a-row>
      </a-card>

      <!-- 车辆参数 -->
      <a-card title="车辆参数" class="form-card" :bordered="false">
        <a-row :gutter="16">
          <a-col :span="8">
            <a-form-item label="核定载人数">
              <a-input v-model:value="formData.approvedPassengers" disabled />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="总质量(kg)">
              <a-input v-model:value="formData.grossMass" disabled />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="整备质量(kg)">
              <a-input v-model:value="formData.unladenMass" disabled />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="16">
          <a-col :span="8">
            <a-form-item label="发动机号码">
              <a-input v-model:value="formData.engineNo" disabled />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="核定载质量(kg)">
              <a-input v-model:value="formData.approvedLoad" disabled />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="外廓尺寸">
              <a-input v-model:value="formData.dimension" disabled />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="准牵引总质量(kg)">
              <a-input v-model:value="formData.tractionMass" disabled />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="备注">
              <a-input v-model:value="formData.remarks" disabled />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="检验记录">
              <a-input v-model:value="formData.inspectionRecord" disabled />
            </a-form-item>
          </a-col>
        </a-row>
      </a-card>

      <!-- 其他信息 -->
      <a-card title="其他信息" class="form-card" :bordered="false">
        <a-row :gutter="16">
          <a-col :span="8">
            <a-form-item label="注册日期">
              <a-input v-model:value="formData.registerDate" disabled />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="发证日期">
              <a-input v-model:value="formData.issueDate" disabled />
            </a-form-item>
          </a-col>

        </a-row>
        <a-row :gutter="16">
          <a-col :span="24">
            <a-form-item label="发证机关">
              <a-input v-model:value="formData.issuingAuthority" disabled />
            </a-form-item>
          </a-col>
        </a-row>
      </a-card>

      <!-- 图片展示 -->
      <a-card title="证件信息" class="form-card" :bordered="false">
        <!-- 双页模式且有双页图片时显示 -->
        <div v-if="formData.drivingLicenseImgUrl">
          <a-form-item label="行驶证图片">
            <div class="image-display-area">
              <div class="image-wrapper">
                <a-image :src="formData.drivingLicenseImgUrl" :preview="{
                  src: formData.drivingLicenseImgUrl
                }" alt="行驶证图片" class="displayed-image" />
                <div class="custom-overlay">
                  <div class="image-hover-actions">
                    <EyeOutlined class="hover-icon preview-icon" @click="previewImage(formData.drivingLicenseImgUrl)" />
                  </div>
                </div>
              </div>
            </div>
          </a-form-item>
        </div>

        <!-- 单页模式且有对应图片时才显示 -->
        <div v-else>
          <a-row :gutter="16">
            <a-col :span="12" v-if="formData.drivingLicenseFrontImgUrl">
              <a-form-item label="行驶证首页">
                <div class="image-display-area">
                  <div class="image-wrapper">
                    <a-image :src="formData.drivingLicenseFrontImgUrl" :preview="{
                      src: formData.drivingLicenseFrontImgUrl
                    }" alt="行驶证首页" class="displayed-image" />
                    <div class="custom-overlay">
                      <div class="image-hover-actions">
                        <EyeOutlined class="hover-icon preview-icon"
                          @click="previewImage(formData.drivingLicenseFrontImgUrl)" />
                      </div>
                    </div>
                  </div>
                </div>
              </a-form-item>
            </a-col>
            <a-col :span="12" v-if="formData.drivingLicenseBackImgUrl">
              <a-form-item label="行驶证副页">
                <div class="image-display-area">
                  <div class="image-wrapper">
                    <a-image :src="formData.drivingLicenseBackImgUrl" :preview="{
                      src: formData.drivingLicenseBackImgUrl
                    }" alt="行驶证副页" class="displayed-image" />
                    <div class="custom-overlay">
                      <div class="image-hover-actions">
                        <EyeOutlined class="hover-icon preview-icon"
                          @click="previewImage(formData.drivingLicenseBackImgUrl)" />
                      </div>
                    </div>
                  </div>
                </div>
              </a-form-item>
            </a-col>
          </a-row>
        </div>

        <!-- 车头照片显示 -->
        <a-form-item label="车头照片">
          <div class="image-display-area" v-if="formData.carImgUrl">
            <div class="image-wrapper">
              <a-image :src="formData.carImgUrl" :preview="{
                src: formData.carImgUrl
              }" alt="车头照片" class="displayed-image" />
              <div class="custom-overlay">
                <div class="image-hover-actions">
                  <EyeOutlined class="hover-icon preview-icon" @click="previewImage(formData.carImgUrl)" />
                </div>
              </div>
            </div>
          </div>
          <div class="no-image" v-else>无</div>
        </a-form-item>

        <!-- 环保清单图片显示 -->
        <a-form-item label="环保清单纸质图片">
          <div class="image-display-area" v-if="formData.environmentalListImgUrl">
            <div class="image-wrapper">
              <a-image :src="formData.environmentalListImgUrl" :preview="{
                src: formData.environmentalListImgUrl
              }" alt="环保清单纸质图片" class="displayed-image" />
              <div class="custom-overlay">
                <div class="image-hover-actions">
                  <EyeOutlined class="hover-icon preview-icon"
                    @click="previewImage(formData.environmentalListImgUrl)" />
                </div>
              </div>
            </div>
          </div>
          <div class="no-image" v-else>无</div>
        </a-form-item>

        <!-- 环保清单电子图片显示 -->
        <a-form-item label="环保清单电子图片">
          <div class="image-display-area" v-if="formData.environmentalListElectronicImgUrl">
            <div class="image-wrapper">
              <a-image :src="formData.environmentalListElectronicImgUrl" :preview="{
                src: formData.environmentalListElectronicImgUrl
              }" alt="环保清单电子图片" class="displayed-image" />
              <div class="custom-overlay">
                <div class="image-hover-actions">
                  <EyeOutlined class="hover-icon preview-icon"
                    @click="previewImage(formData.environmentalListElectronicImgUrl)" />
                </div>
              </div>
            </div>
          </div>
          <div class="no-image" v-else>无</div>
        </a-form-item>

        <!-- 重型柴油车排放阶段查询截图 -->
        <a-form-item label="重型柴油车排放阶段查询截图">
          <div class="image-display-area" v-if="formData.heavyVehicleEmissionStageImgUrl">
            <div class="image-wrapper">
              <a-image :src="formData.heavyVehicleEmissionStageImgUrl" :preview="{
                src: formData.heavyVehicleEmissionStageImgUrl
              }" alt="重型柴油车排放阶段查询截图" class="displayed-image" />
              <div class="custom-overlay">
                <div class="image-hover-actions">
                  <EyeOutlined class="hover-icon preview-icon"
                    @click="previewImage(formData.heavyVehicleEmissionStageImgUrl)" />
                </div>
              </div>
            </div>
          </div>
          <div class="no-image" v-else>无</div>
        </a-form-item>

      </a-card>

      <!-- 审核信息 -->
      <a-card title="审核信息" class="form-card" :bordered="false">
        <a-form-item label="审核备注" name="verifyRemark">
          <a-textarea v-model:value="formData.verifyRemark" placeholder="请输入审核备注..." :rows="4" :maxLength="500"
            show-count />
        </a-form-item>
      </a-card>
    </a-form>
    <template #footer>
      <a-space>
        <a-button @click="onClose">取消</a-button>
        <a-button type="primary" @click="onApprove" :loading="submitLoading">审核通过</a-button>
        <a-button type="danger" @click="onReject" :loading="submitLoading">审核驳回</a-button>
      </a-space>
    </template>
  </xn-form-container>
</template>

<script setup name="vehicleAudit">
import tool from '@/utils/tool'
import { cloneDeep } from 'lodash-es'
import vehicleApi from '@/api/biz/vehicleApi'
import { ref, computed } from 'vue'
import { message } from 'ant-design-vue'
import { EyeOutlined } from '@ant-design/icons-vue'

// 抽屉状态
const visible = ref(false)
const emit = defineEmits({ successful: null })
const formRef = ref()

// 表单数据
const formData = ref({
  drivingLicenseImgUrl: '',
  drivingLicenseFrontImgUrl: '',
  drivingLicenseBackImgUrl: '',
  carImgUrl: '',
  environmentalListImgUrl: '',
  environmentalListElectronicImgUrl: '',
  heavyVehicleEmissionStageImgUrl: '',
  uploadMode: 'single',
  verifyRemark: ''
})

const submitLoading = ref(false)

// 计算属性 - 字典值文本显示
const plateColorText = computed(() => tool.dictTypeData('PLATE_COLOR', formData.value.plateColor) || '')
const netStatusText = computed(() => tool.dictTypeData('NET_STATUS', formData.value.netStatus) || '')
const emissionStageText = computed(() => tool.dictTypeData('EMISSION_STAGE', formData.value.emissionStage) || '')

// 打开抽屉
const onOpen = (record) => {
  visible.value = true

  if (record) {
    let recordData = cloneDeep(record)
    formData.value = Object.assign({}, recordData)

    // 不再清空审核备注，保留原有备注以便回显
    // formData.value.verifyRemark = ''
  }
}

// 关闭抽屉
const onClose = () => {
  visible.value = false
  formData.value = {
    drivingLicenseImgUrl: '',
    drivingLicenseFrontImgUrl: '',
    drivingLicenseBackImgUrl: '',
    carImgUrl: '',
    environmentalListImgUrl: '',
    environmentalListElectronicImgUrl: '',
    heavyVehicleEmissionStageImgUrl: '',
    uploadMode: 'single',
    verifyRemark: ''
  }
}

// 审核通过
const onApprove = () => {
  submitLoading.value = true

  // 格式化日期为标准格式
  const currentDate = new Date();
  const formattedDate = formatDate(currentDate);

  const params = {
    id: formData.value.id,
    verifyStatus: '1', // 审核通过状态
    verifyRemark: formData.value.verifyRemark,
    verifyTime: formattedDate
  }

  // 审核API调用
  vehicleApi.vehicleAudit(params)
    .then(() => {
      message.success('审核通过成功')
      onClose()
      emit('successful')
    })
    .catch(error => {
      console.error('审核失败:', error)
    })
    .finally(() => {
      submitLoading.value = false
    })
}

// 审核驳回
const onReject = () => {
  if (!formData.value.verifyRemark) {
    message.warning('驳回时必须填写审核备注')
    return
  }

  submitLoading.value = true
  const params = {
    id: formData.value.id,
    verifyStatus: '2', // 审核驳回状态
    verifyRemark: formData.value.verifyRemark,
    verifyTime: formatDate(new Date())
  }

  // 假设这里是审核驳回的API
  vehicleApi.vehicleAudit(params)
    .then(() => {
      message.success('审核驳回成功')
      onClose()
      emit('successful')
    })
    .catch(error => {
      console.error('审核驳回失败:', error)
    })
    .finally(() => {
      submitLoading.value = false
    })
}

// 图片预览方法
const previewImage = (src) => {
  if (!src) return;

  // 使用Ant Design Vue的图片预览功能
  const images = [{ src }];
  const index = 0;

  // 触发图片的预览功能
  const imageElement = document.querySelector(`img[src="${src}"]`);
  if (imageElement) {
    imageElement.click();
  }
}

// 格式化日期函数
const formatDate = (date) => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');

  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}

// 暴露函数
defineExpose({
  onOpen
})
</script>

<style scoped>
.form-card {
  margin-bottom: 16px;
}

.form-card :deep(.ant-card-head) {
  min-height: 40px;
  padding: 0 12px;
  background-color: #f5f7fa;
}

.form-card :deep(.ant-card-head-title) {
  padding: 8px 0;
  font-size: 14px;
  font-weight: 500;
}

.form-card :deep(.ant-card-body) {
  padding: 16px;
}

/* 图片容器样式 */
.image-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  margin-bottom: 16px;
}

.image-display-area {
  width: 100%;
  height: 200px;
  background-color: #fafafa;
  border: 1px dashed #d9d9d9;
  border-radius: 4px;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  position: relative;
  transition: all 0.3s;
}

.image-wrapper {
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
}

.displayed-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.custom-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  opacity: 0;
  transition: opacity 0.3s;
}

.image-wrapper:hover .custom-overlay {
  opacity: 1;
}

.image-hover-actions {
  display: flex;
  gap: 16px;
}

.hover-icon {
  font-size: 20px;
  color: white;
  cursor: pointer;
  transition: transform 0.2s;
}

.hover-icon:hover {
  transform: scale(1.2);
}

.preview-icon:hover {
  color: #1890ff;
}

/* 兼容旧样式 */
.image-preview {
  width: 100%;
  height: 200px;
  background-color: #fafafa;
  border: 1px dashed #d9d9d9;
  border-radius: 4px;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  position: relative;
}

.preview-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.no-image {
  width: 100%;
  height: 100px;
  border: 1px dashed #d9d9d9;
  border-radius: 4px;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #999;
}
</style>