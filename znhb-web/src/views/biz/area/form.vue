<template>
	<a-drawer
		:title="formData.id ? '编辑区域' : '新增区域'"
		:width="500"
		:visible="visible"
		:body-style="{ paddingBottom: '80px' }"
		@close="onClose"
	>
		<a-form
			ref="formRef"
			:model="formData"
			:rules="rules"
			layout="vertical"
		>
			<a-form-item name="areaType" label="区域类型" required>
				<a-select v-model:value="formData.areaType" placeholder="请选择区域类型" :options="areaTypeOptions" />
			</a-form-item>

			<a-form-item name="areaName" label="区域名称" required>
				<a-input v-model:value="formData.areaName" placeholder="请输入区域名称" />
			</a-form-item>

			<a-form-item name="areaCode" label="区域编码">
				<a-input v-model:value="formData.areaCode" placeholder="请输入区域编码" />
			</a-form-item>

			<a-form-item name="parentId" label="上级区域">
				<a-tree-select
					v-model:value="formData.parentId"
					:dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
					:tree-data="areaTreeData"
					placeholder="请选择上级区域"
					tree-default-expand-all
					allow-clear
					:field-names="{ children: 'children', label: 'areaName', value: 'id' }"
				/>
				<div v-if="areaTreeData.length > 0 && areaTreeData[0].children && areaTreeData[0].children.length === 0" class="tree-empty-tip">
					暂无区域数据，请先创建区域
				</div>
			</a-form-item>

			<a-form-item name="manager" label="区域负责人">
				<a-input-group compact>
					<a-input v-model:value="formData.managerName" placeholder="请选择负责人" style="width: 80%" readonly />
					<a-button type="primary" style="width: 20%" @click="showUserSelect">选择</a-button>
				</a-input-group>
			</a-form-item>

			<a-form-item name="sortCode" label="排序码">
				<a-input-number v-model:value="formData.sortCode" :min="1" :max="999" style="width: 100%" />
			</a-form-item>

			<a-form-item name="remark" label="备注">
				<a-textarea v-model:value="formData.remark" placeholder="请输入备注" :rows="4" />
			</a-form-item>
		</a-form>

		<div class="drawer-footer">
			<a-space>
				<a-button @click="onClose">取消</a-button>
				<a-button type="primary" @click="onSubmit" :loading="submitLoading">提交</a-button>
			</a-space>
		</div>

		<!-- 用户选择器 -->
		<user-select 
			v-model:visible="userSelectVisible"
			title="选择负责人"
			:multiple="false"
			@confirm="handleUserSelected"
		/>
	</a-drawer>
</template>

<script setup>
import { ref } from 'vue'
import { message } from 'ant-design-vue'
import areaApi from '@/api/biz/area'
import tool from '@/utils/tool'
import UserSelect from '@/views/biz/components/UserSelect.vue'

// 表单属性
const visible = ref(false)
const formRef = ref()
const submitLoading = ref(false)
const emit = defineEmits(['successful'])

// 用户选择器相关
const userSelectVisible = ref(false)

// 表单数据
const formData = ref({
	id: '',
	areaType: '',
	areaName: '',
	areaCode: '',
	managerId: '',
	managerName: '',
	parentId: '',
	sortCode: 99,
	remark: ''
})

// 表单校验规则
const rules = {
	areaType: [{ required: true, message: '请选择区域类型', trigger: 'change' }],
	areaName: [{ required: true, message: '请输入区域名称', trigger: 'blur' }]
}

// 区域类型选项
const areaTypeOptions = tool.dictList('AREA_TYPE')

// 区域树形数据
const areaTreeData = ref([])

// 加载区域树形数据
const loadAreaTree = async () => {
	try {
		const res = await areaApi.areaTree()
		console.log('区域树原始数据:', res.data)

		// 添加一个根节点
		areaTreeData.value = [
			{
				id: '0',
				areaName: '顶级区域',
				children: res || []
			}
		]

		console.log('区域树处理后数据:', areaTreeData.value)
	} catch (error) {
		console.error('加载区域树形数据失败', error)
		// 出错时设置一个默认的根节点
		areaTreeData.value = [
			{
				id: '0',
				areaName: '顶级区域',
				children: []
			}
		]
	}
}

// 打开表单
const onOpen = async (record) => {
	// 显示表单
	visible.value = true

	// 重置表单
	resetForm()

	// 加载区域树
	try {
		await loadAreaTree()
		console.log('区域树加载完成')
	} catch (error) {
		console.error('区域树加载失败:', error)
	}

	// 如果是编辑模式，直接使用传入的记录数据
	if (record && record.id) {
		// 直接使用传入的记录数据
		formData.value = { ...record }
		console.log('设置表单数据:', formData.value)
	}
}

// 关闭表单
const onClose = () => {
	visible.value = false
	resetForm()
}

// 显示用户选择器
const showUserSelect = () => {
	userSelectVisible.value = true
}

// 处理用户选择结果
const handleUserSelected = (userId, userData) => {
	console.log('选择的用户:', userId, userData)
	formData.value.managerId = userId
	formData.value.managerName = userData.name
}

// 重置表单
const resetForm = () => {
	formData.value = {
		id: '',
		areaType: undefined,
		areaName: '',
		areaCode: '',
		managerId: undefined,
		managerName: '',
		parentId: undefined,
		sortCode: 99,
		remark: ''
	}

	if (formRef.value) {
		formRef.value.resetFields()
	}
}

// 提交表单
const onSubmit = () => {
	formRef.value.validate().then(async () => {
		submitLoading.value = true
		try {
			if (formData.value.id) {
				// 编辑
				await areaApi.areaEdit(formData.value)
				message.success('编辑区域成功')
			} else {
				// 新增
				await areaApi.areaAdd(formData.value)
				message.success('新增区域成功')
			}

			// 关闭表单并通知父组件刷新
			onClose()
			emit('successful')
		} catch (error) {
			console.error('提交区域数据失败', error)
		} finally {
			submitLoading.value = false
		}
	})
}

// 暴露方法给父组件
defineExpose({
	onOpen
})
</script>

<style scoped>
.drawer-footer {
	position: absolute;
	bottom: 0;
	width: 100%;
	border-top: 1px solid #e8e8e8;
	padding: 10px 16px;
	text-align: right;
	left: 0;
	background: #fff;
}

.tree-empty-tip {
	color: #ff4d4f;
	font-size: 12px;
	margin-top: 5px;
}
</style>
