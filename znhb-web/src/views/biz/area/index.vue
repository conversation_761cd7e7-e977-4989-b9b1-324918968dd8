<template>
	<a-card :bordered="false">
		<a-form ref="searchFormRef" name="advanced_search" :model="searchFormState" class="ant-advanced-search-form">
			<a-row :gutter="16">
				<a-col :span="5">
					<a-form-item label="区域名称" name="areaName">
						<a-input v-model:value="searchFormState.areaName" placeholder="请输入区域名称" />
					</a-form-item>
				</a-col>
				<a-col :span="5">
					<a-form-item label="区域类型" name="areaType">
						<a-select v-model:value="searchFormState.areaType" placeholder="请选择区域类型"
							:options="areaTypeOptions" />
					</a-form-item>
				</a-col>
				<a-col :span="5">
					<a-form-item label="区域编码" name="areaCode">
						<a-input v-model:value="searchFormState.areaCode" placeholder="请输入区域编码" />
					</a-form-item>
				</a-col>
				<a-col>
					<div class="table-page-search-submitButtons">
						<a-button type="primary" @click="tableRef.refresh()">查询</a-button>
						<a-button style="margin: 0 8px" @click="reset">重置</a-button>
						<a @click="toggleAdvanced">
							{{ advanced ? '收起' : '展开' }}
							<component :is="advanced ? 'up-outlined' : 'down-outlined'" />
						</a>
					</div>
				</a-col>
			</a-row>
			<div v-if="advanced" class="advanced-search-row">
				<a-row :gutter="16" class="advanced">
					<a-col :span="5">
						<a-form-item label="上级区域" name="parentId">
							<a-tree-select v-model:value="searchFormState.parentId"
								:dropdown-style="{ maxHeight: '400px', overflow: 'auto' }" :tree-data="areaTreeData"
								placeholder="请选择上级区域" tree-default-expand-all allow-clear
								:field-names="{ children: 'children', label: 'areaName', value: 'id' }" />
						</a-form-item>
					</a-col>
					<a-col :span="5">
						<a-form-item label="负责人姓名" name="managerName">
							<a-input v-model:value="searchFormState.managerName" placeholder="请输入负责人姓名" />
						</a-form-item>
					</a-col>
				</a-row>
			</div>
		</a-form>
	</a-card>
	<a-card :bordered="false" class="mt-2">
		<s-table ref="tableRef" :columns="columns" :data="loadData" :alert="options.alert.show" bordered
			:row-key="(record) => record.id" :tool-config="toolConfig" :row-selection="options.rowSelection">
			<template #operator class="table-operator">
				<a-space>
					<a-button type="primary" @click="formRef.onOpen()">
						<template #icon><plus-outlined /></template>
						新增区域
					</a-button>
					<xn-batch-delete :selectedRowKeys="selectedRowKeys" @batchDelete="deleteBatchArea" />
				</a-space>
			</template>
			<template #bodyCell="{ column, record }">
				<template v-if="column.dataIndex === 'areaType'">
					{{ $TOOL.dictTypeData('AREA_TYPE', record.areaType) }}
				</template>
				<template v-if="column.dataIndex === 'action'">
					<a @click="formRef.onOpen(record)">编辑</a>
					<a-divider type="vertical" />
					<a-popconfirm title="确定要删除吗？" @confirm="deleteArea(record)">
						<a-button type="link" danger size="small">删除</a-button>
					</a-popconfirm>
				</template>
			</template>
		</s-table>
	</a-card>
	<Form ref="formRef" @successful="tableRef.refresh()" />
</template>

<script setup name="area">
import tool from '@/utils/tool'
import { cloneDeep } from 'lodash-es'
import Form from './form.vue'
import areaApi from '@/api/biz/area'
import {PlusOutlined } from '@ant-design/icons-vue'
import { ref, onMounted } from 'vue'
const searchFormState = ref({})
const searchFormRef = ref()
const tableRef = ref()
const formRef = ref()
const toolConfig = { refresh: true, height: true, columnSetting: true, striped: false }

// 查询区域显示更多控制
const advanced = ref(false)
const toggleAdvanced = () => {
	advanced.value = !advanced.value
}

const columns = [
	{
		title: '区域名称',
		dataIndex: 'areaName'
	},
	{
		title: '区域类型',
		dataIndex: 'areaType'
	},
	{
		title: '区域编码',
		dataIndex: 'areaCode'
	},
	{
		title: '负责人',
		dataIndex: 'managerName'
	},
	{
		title: '排序',
		dataIndex: 'sortCode'
	},
	{
		title: '备注',
		dataIndex: 'remark',
		ellipsis: true
	},
	{
		title: '操作',
		dataIndex: 'action',
		align: 'center',
		width: 150
	}
]

const selectedRowKeys = ref([])
// 列表选择配置
const options = {
	// columns数字类型字段加入 needTotal: true 可以勾选自动算账
	alert: {
		show: true,
		clear: () => {
			selectedRowKeys.value = ref([])
		}
	},
	rowSelection: {
		onChange: (selectedRowKey, selectedRows) => {
			selectedRowKeys.value = selectedRowKey
		}
	}
}

// 加载区域树形数据
const areaTreeData = ref([])
const loadAreaTree = async () => {
	try {
		const res = await areaApi.areaTree()
		console.log(res)
		// 添加一个根节点
		areaTreeData.value = [
			{
				id: '0',
				areaName: '顶级区域',
				children: res
			}
		]
	} catch (error) {
		console.error('加载区域树形数据失败', error)
	}
}

// 加载数据
const loadData = (parameter) => {
	const searchFormParam = cloneDeep(searchFormState.value)
	return areaApi.areaPage(Object.assign(parameter, searchFormParam)).then((data) => {
		// 加载区域树
		loadAreaTree()
		return data
	})
}

// 重置
const reset = () => {
	searchFormRef.value.resetFields()
	tableRef.value.refresh(true)
}

// 删除
const deleteArea = (record) => {
	let params = [
		{
			id: record.id
		}
	]
	areaApi.areaDelete(params).then(() => {
		tableRef.value.refresh(true)
	})
}

// 批量删除
const deleteBatchArea = (params) => {
	areaApi.areaDelete(params).then(() => {
		tableRef.value.clearRefreshSelected()
	})
}

// 区域类型选项
const areaTypeOptions = tool.dictList('AREA_TYPE')

// 初始化
onMounted(() => {
	loadAreaTree()
})
</script>

<style lang="less" scoped>
.ant-advanced-search-form {
	padding: 24px;
	background-color: #fbfbfb;
	border: 1px solid #d9d9d9;
	border-radius: 2px;
}

.table-page-search-submitButtons {
	display: flex;
	justify-content: flex-end;
	align-items: center;
}

.advanced-search-row {
	margin-top: 16px;
}

.mt-2 {
	margin-top: 16px;
}
</style>
