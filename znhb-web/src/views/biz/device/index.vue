<template>
	<a-card :bordered="false">
		<a-form ref="searchFormRef" name="advanced_search" :model="searchFormState" class="ant-advanced-search-form">
			<!-- 基本搜索项 -->
			<a-row :gutter="16">
				<a-col :span="5">
					<a-form-item label="设备名称" name="name">
						<a-input v-model:value="searchFormState.name" placeholder="请输入设备名称" />
					</a-form-item>
				</a-col>
				<a-col :span="9">
					<div class="search-buttons">
						<a-button type="primary" @click="tableRef.refresh()">查询</a-button>
						<a-button @click="reset">重置</a-button>
						<a @click="toggleAdvanced" class="expand-button">
							{{ advanced ? '收起' : '展开' }}
							<component :is="advanced ? 'up-outlined' : 'down-outlined'"/>
						</a>
					</div>
				</a-col>
			</a-row>

			<!-- 高级搜索项 -->
			<div v-show="advanced" class="advanced-search-row">
				<a-row :gutter="16" class="advanced">
					<a-col :span="5">
						<a-form-item label="设备类型" name="deviceType">
							<a-select v-model:value="searchFormState.deviceType" placeholder="请选择设备类型" :options="deviceTypeOptions" allowClear />
						</a-form-item>
					</a-col>
					<a-col :span="5">
						<a-form-item label="所属区域" name="areaId">
							<a-select 
								v-model:value="searchFormState.areaId" 
								placeholder="请选择所属区域" 
								:options="formatAreaOptions(areaList)" 
								allowClear 
								:getPopupContainer="trigger => trigger.parentNode"
							/>
						</a-form-item>
					</a-col>
					<a-col :span="5">
						<a-form-item label="布防状态" name="defendStatus">
							<a-select v-model:value="searchFormState.defendStatus" placeholder="请选择布防状态" :options="defendStatusOptions" allowClear />
						</a-form-item>
					</a-col>
					<a-col :span="5">
						<a-form-item label="ISC摄像机编码" name="cameraIndexCode">
							<a-input v-model:value="searchFormState.cameraIndexCode" placeholder="请输入ISC摄像机编码" allowClear />
						</a-form-item>
					</a-col>
				</a-row>
			</div>
		</a-form>
	</a-card>
	<a-card :bordered="false" class="mt-2">
		<s-table
			ref="tableRef"
			:columns="columns"
			:data="loadData"
			:alert="options.alert.show"
			bordered
			:row-key="(record) => record.id"
			:tool-config="toolConfig"
			:row-selection="options.rowSelection"
		>
			<template #operator class="table-operator">
				<a-space>
					<a-button type="primary" @click="formRef.onOpen()" v-if="hasPerm('deviceAdd')">
						<template #icon><plus-outlined /></template>
						新增
					</a-button>
					<xn-batch-delete
						v-if="hasPerm('deviceBatchDelete')"
						:selectedRowKeys="selectedRowKeys"
						@batchDelete="deleteBatchDevice"
					/>
				</a-space>
			</template>
			<template #bodyCell="{ column, record }">
				<template v-if="column.dataIndex === 'action'">
					<a @click="formRef.onOpen(record)" v-if="hasPerm('deviceEdit')">编辑</a>
						<a-divider type="vertical" v-if="hasPerm(['deviceEdit', 'deviceDelete'], 'and')" />
						<a-popconfirm title="确定要删除吗？" @confirm="deleteDevice(record)">
							<a-button type="link" danger size="small" v-if="hasPerm('deviceDelete')">删除</a-button>
						</a-popconfirm>
						<a-divider type="vertical" v-if="record.defendStatus !== 'NOT_NEED_DEFEND'" />
						<a-dropdown v-if="record.defendStatus !== 'NOT_NEED_DEFEND'">
							<a class="ant-dropdown-link" @click.prevent>更多<down-outlined />
							</a>
							<template #overlay>
								<a-menu>
									<a-menu-item key="1" @click="defendDevice(record)" v-if="record.defendStatus !== 'DEFEND_SUCCESS'">
										<template #icon><safety-outlined style="color: #52c41a" /></template>
										<span style="color: #52c41a; font-weight: 500">布防</span>
									</a-menu-item>
									<a-menu-item key="2" @click="closeDefend(record)" v-else>
										<template #icon><close-square-outlined style="color: red" /></template>
										<span style="color: red; font-weight: 500">撤防</span>
									</a-menu-item>
								</a-menu>

							</template>
						</a-dropdown>
				</template>
				<template v-if="column.dataIndex === 'areaName'">
					{{ record.areaName || '-' }}
				</template>
			</template>
		</s-table>
	</a-card>
	<Form ref="formRef" @successful="tableRef.refresh()" />
</template>

<script setup name="device">
	import { cloneDeep } from 'lodash-es'
	import { h } from 'vue'
	import { Tag, message } from 'ant-design-vue'
	import Form from './form.vue'
	import deviceApi from '@/api/biz/deviceApi'
	import areaApi from '@/api/biz/area'
	import tool from '@/utils/tool'
	import { DownOutlined, PlusOutlined, SafetyOutlined, UpOutlined, CloseSquareOutlined } from '@ant-design/icons-vue'
	import { onMounted } from 'vue'

	const searchFormState = ref({})
	const searchFormRef = ref()
	const tableRef = ref()
	const formRef = ref()
	const toolConfig = { refresh: true, height: true, columnSetting: true, striped: false }

	// 查询区域显示更多控制
	const advanced = ref(false)
	const toggleAdvanced = () => {
		advanced.value = !advanced.value
	}

	// 字典选项
	const deviceTypeOptions = ref([])
	const defendStatusOptions = ref([])
	const areaList = ref([])

	// 初始化区域列表
	const loadAreaList = async () => {
		try {
			const result = await areaApi.areaPage({size: 500})
			areaList.value = result.records || []
		} catch (error) {
			console.error('加载区域列表失败:', error)
		}
	}
	
	// 根据区域ID获取区域名称
	const getAreaName = (areaId) => {
		if (!areaId) return '-'
		const area = areaList.value.find(item => item.id === areaId)
		return area ? area.areaName : '-'
	}

	// 初始化字典数据
	onMounted(() => {
		deviceTypeOptions.value = tool.dictList('DEVICE_TYPE')
		defendStatusOptions.value = tool.dictList('DEFEND_STATUS')
		loadAreaList()
	})

	const columns = [
		{
			title: '设备名称',
			dataIndex: 'name'
		},
		{
			title: '设备ip',
			dataIndex: 'ip'
		},
		{
			title: '设备端口',
			dataIndex: 'port'
		},
		{
			title: '设备类型',
			dataIndex: 'deviceType',
			customRender: ({ text }) => {
				return tool.dictTypeData('DEVICE_TYPE', text)
			}
		},
		{
			title: '所属区域',
			dataIndex: 'areaName'
		},
		{
			title: '账号',
			dataIndex: 'username'
		},
		{
			title: '密码',
			dataIndex: 'password'
		},
		{
			title: '驱动类路径',
			dataIndex: 'driverClassPath'
		},
		{
			title: 'ISC摄像机编码',
			dataIndex: 'cameraIndexCode'
		},
		{
			title: '布防状态',
			dataIndex: 'defendStatus',
			customRender: ({ text }) => {
				const status = text;
				let color = '';
				if (status === 'NOT_DEFEND') {
					color = 'default';
				}else if (status === 'DEFEND_SUCCESS') {
					color = 'success';
				} else if (status === 'NOT_NEED_DEFEND') {
					color = 'default';
				} else if (status === 'DEFEND_FAIL') {
					color = 'error';
				}else if (status === 'NOT_LOGIN') {
					color = 'default';
				}else if (status === 'LOGIN_FAIL') {
					color = 'error';
				}
				return h(Tag, { color: color }, () => tool.dictTypeData('DEFEND_STATUS', text));
			}
		},
	]
	// 操作栏通过权限判断是否显示
	if (hasPerm(['deviceEdit', 'deviceDelete', 'deviceDefend'])) {
		columns.push({
			title: '操作',
			dataIndex: 'action',
			align: 'center',
			width: 220
		})
	}
	const selectedRowKeys = ref([])
	// 列表选择配置
	const options = {
		// columns数字类型字段加入 needTotal: true 可以勾选自动算账
		alert: {
			show: true,
			clear: () => {
				selectedRowKeys.value = ref([])
			}
		},
		rowSelection: {
			onChange: (selectedRowKey, selectedRows) => {
				selectedRowKeys.value = selectedRowKey
			}
		}
	}
	const loadData = (parameter) => {
		const searchFormParam = cloneDeep(searchFormState.value)
		return deviceApi.devicePage(Object.assign(parameter, searchFormParam)).then((data) => {
			return data
		})
	}
	// 重置
	const reset = () => {
		searchFormRef.value.resetFields()
		tableRef.value.refresh(true)
	}
	// 删除
	const deleteDevice = (record) => {
		let params = [
			{
				id: record.id
			}
		]
		deviceApi.deviceDelete(params).then(() => {
			tableRef.value.refresh(true)
		})
	}
	// 批量删除
	const deleteBatchDevice = (params) => {
		deviceApi.deviceDelete(params).then(() => {
			tableRef.value.clearRefreshSelected()
		})
	}
	// 布防设备
	const defendDevice = (params) => {
		deviceApi.deviceDefend(params).then((data) => {
			if(data){
				message.success('布防成功')
			}else{
				message.error('布防失败')
			}
			tableRef.value.refresh(true)
		})
	}
	// 撤防设备
	const closeDefend = (params) => {
		deviceApi.closeDefend(params).then((data) => {
			if(data){
				message.success('撤防成功')
			}else{
				message.error('撤防失败')
			}
			tableRef.value.refresh(true)
		})
	}

	// 将区域数据转换为下拉选项
	const formatAreaOptions = (areaList) => {
		if (!areaList || areaList.length === 0) return []
		
		return areaList.map(area => ({
			value: area.id,
			label: area.areaName
		}))
	}
</script>
