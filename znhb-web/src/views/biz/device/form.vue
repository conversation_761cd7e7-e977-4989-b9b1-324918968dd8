<template>
	<xn-form-container
		type="modal"
		:title="formData.id ? '编辑设备信息' : '新增设备信息'"
		:width="720"
		:visible="visible"
		:destroy-on-close="true"
		@close="onClose"
	>
		<a-form ref="formRef" :model="formData" :rules="formRules" layout="horizontal" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
			<!-- 基本信息 -->
			<a-divider orientation="left">
				<template #plain>
					<laptop-outlined />
					<span class="divider-title">基本信息</span>
				</template>
			</a-divider>

			<a-row :gutter="16">
				<a-col :span="12">
					<a-form-item label="设备名称" name="name">
						<a-input v-model:value="formData.name" placeholder="请输入设备名称" allow-clear>
							<template #prefix><tag-outlined /></template>
						</a-input>
					</a-form-item>
				</a-col>
				<a-col :span="12">
					<a-form-item label="设备类型" name="deviceType">
						<a-select v-model:value="formData.deviceType" placeholder="请选择设备类型" :options="deviceTypeOptions" allow-clear />
					</a-form-item>
				</a-col>
			</a-row>

			<a-row :gutter="16">
				<a-col :span="12">
					<a-form-item label="所属区域" name="areaId">
						<a-select v-model:value="formData.areaId" placeholder="请选择所属区域" :options="areaOptions" allow-clear @change="onAreaChange" />
					</a-form-item>
				</a-col>
			</a-row>

			<!-- 网络配置 -->
			<a-divider orientation="left">
				<template #plain>
					<global-outlined />
					<span class="divider-title">网络配置</span>
				</template>
			</a-divider>

			<a-row :gutter="16">
				<a-col :span="12">
					<a-form-item label="设备IP" name="ip">
						<IpInput v-model:value="formData.ip" size="small" />

					</a-form-item>
				</a-col>
				<a-col :span="12">
					<a-form-item label="设备端口" name="port">
						<a-input-number v-model:value="formData.port" placeholder="请输入设备端口" style="width: 100%" :min="1" :max="65535">
						</a-input-number>
					</a-form-item>
				</a-col>
			</a-row>

			<a-row :gutter="16">
				<a-col :span="12">
					<a-form-item label="账号" name="username">
						<a-input v-model:value="formData.username" placeholder="请输入设备访问账号" allow-clear>
							<template #prefix><user-outlined /></template>
						</a-input>
					</a-form-item>
				</a-col>
				<a-col :span="12">
					<a-form-item label="密码" name="password">
						<a-input-password v-model:value="formData.password" placeholder="请输入设备访问密码" allow-clear>
							<template #prefix><lock-outlined /></template>
						</a-input-password>
					</a-form-item>
				</a-col>
			</a-row>

			<!-- 驱动配置 -->
			<a-divider orientation="left">
				<template #plain>
					<api-outlined />
					<span class="divider-title">驱动配置</span>
				</template>
			</a-divider>

			<a-row :gutter="16">
				<a-col :span="24">
					<a-form-item label="驱动类路径" name="driverClassPath" :label-col="{ span: 34}" :wrapper-col="{ span: 21 }">
						<a-select v-model:value="formData.driverClassPath" placeholder="请选择驱动类完整路径" :options="driverClassOptions" />
					</a-form-item>
				</a-col>
			</a-row>

			<!-- ISC摄像机编码 -->
			<a-row :gutter="16">
				<a-col :span="24">
					<a-form-item label="ISC摄像机编码" name="cameraIndexCode" :label-col="{ span: 34}" :wrapper-col="{ span: 21 }">
						<a-input v-model:value="formData.cameraIndexCode" placeholder="请输入ISC摄像机编码" allow-clear>
							<template #prefix><code-outlined /></template>
						</a-input>
					</a-form-item>
				</a-col>
			</a-row>
		</a-form>
		<template #footer>
			<a-button style="margin-right: 8px" @click="onClose">取消</a-button>
			<a-button type="primary" @click="onSubmit" :loading="submitLoading">
				<template #icon><save-outlined /></template>
				保存
			</a-button>
		</template>
	</xn-form-container>
</template>

<script setup name="deviceForm">
	import { cloneDeep } from 'lodash-es'
	import { required } from '@/utils/formRules'
	import deviceApi from '@/api/biz/deviceApi'
	import areaApi from '@/api/biz/area'
	import IpInput from '@/components/IpInput/index.vue'
	import tool from '@/utils/tool'
	import {
		LaptopOutlined,
		GlobalOutlined,
		CloudOutlined,
		UserOutlined,
		LockOutlined,
		TagOutlined,
		ApiOutlined,
		CodeOutlined,
		NumberOutlined,
		SaveOutlined
	} from '@ant-design/icons-vue'

	// 抽屉状态
	const visible = ref(false)
	const emit = defineEmits({ successful: null })
	const formRef = ref()
	// 表单数据
	const formData = ref({})
	const submitLoading = ref(false)
	const driverClassOptions = ref([])
	const deviceTypeOptions = ref([])
	const statusOptions = ref([])
	const areaOptions = ref([])

	// 打开表单
	const onOpen = (record) => {
		visible.value = true
		// 初始化默认值
		formData.value = {
			status: '1',  // 默认在线
			port: 8000    // 默认端口
		}

		// 加载设备类型字典
		deviceTypeOptions.value = tool.dictList('DEVICE_TYPE')

		// 加载设备状态字典
		statusOptions.value = tool.dictList('DEVICE_STATUS')
		
		// 加载区域列表
		areaApi.areaPage({size: 500}).then((result) => {
			// 将分页结果中的记录转为下拉选项
			if (result && result.records) {
				areaOptions.value = result.records.map(area => ({
					value: area.id,
					label: area.areaName
				}))
			}
		})

		if (record) {
			let recordData = cloneDeep(record)
			formData.value = Object.assign({}, formData.value, recordData)
		}

		deviceApi.getDriverClass().then((data) => {
			driverClassOptions.value = data.map((item) => {
				return {
					value: item.classpath,
					label: item.classname
				}
			})
		})
	}

	// 关闭表单
	const onClose = () => {
		formRef.value.resetFields()
		formData.value = {}
		visible.value = false
	}

	// 表单验证规则
	const formRules = {
		name: [required('请输入设备名称')],
		ip: [required('请输入设备IP地址')],
		port: [required('请输入设备端口')],
		driverClassPath: [required('请选择驱动类完整路径')],
		deviceType: [required('请选择设备类型')],
		areaId: [required('请选择所属区域')],
	}

	// 验证并提交数据
	const onSubmit = () => {
		formRef.value.validate().then(() => {
			submitLoading.value = true
			const formDataParam = cloneDeep(formData.value)
			
			// 确保区域名称也被提交
			if (formDataParam.areaId && !formDataParam.areaName) {
				const selectedArea = areaOptions.value.find(area => area.value === formDataParam.areaId)
				if (selectedArea) {
					formDataParam.areaName = selectedArea.label
				}
			}
			
			deviceApi
				.deviceSubmitForm(formDataParam, formDataParam.id)
				.then(() => {
					onClose()
					emit('successful')
				})
				.finally(() => {
					submitLoading.value = false
				})
		})
	}

	// 区域选择变更时，同步设置区域名称
	const onAreaChange = (value) => {
		if (value) {
			const selectedArea = areaOptions.value.find(area => area.value === value)
			if (selectedArea) {
				formData.value.areaName = selectedArea.label
			}
		} else {
			formData.value.areaName = undefined
		}
	}

	// 抛出函数
	defineExpose({
		onOpen
	})
</script>

<style scoped>
.divider-title {
	margin-left: 8px;
	font-weight: 500;
	font-size: 15px;
}

:deep(.ant-form-item-label) {
	text-align: right;
}

:deep(.ant-divider) {
	margin: 16px 0;
}
</style>
