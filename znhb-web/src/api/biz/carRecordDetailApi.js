import { baseRequest } from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/biz/carRecordDetail/` + url, ...arg)

/**
 * 识别记录Api接口管理器
 *
 * <AUTHOR>
 * @date  2025/03/21 23:33
 **/
export default {
	// 获取识别记录分页
	carRecordDetailPage(data) {
		return request('page', data, 'get')
	},
	// 提交识别记录表单 edit为true时为编辑，默认为新增
	carRecordDetailSubmitForm(data, edit = false) {
		return request(edit ? 'edit' : 'add', data)
	},
	// 删除识别记录
	carRecordDetailDelete(data) {
		return request('delete', data)
	},
	// 获取识别记录详情
	carRecordDetailDetail(data) {
		return request('detail', data, 'get')
	},
	//登记入厂
	registerEntry(data) {
		return request('registerEntry', data, 'get')
	},
	//登记出厂
	registerExit(data) {
		return request('registerExit', data, 'get')
	}
}
