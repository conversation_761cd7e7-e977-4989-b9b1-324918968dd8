import { baseRequest } from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/biz/ledConfig/` + url, ...arg)

/**
 * LED配置信息表Api接口管理器
 *
 * <AUTHOR>
 * @date  2025/05/12 16:23
 **/
export default {
	// 获取LED配置信息表分页
	ledConfigPage(data) {
		return request('page', data, 'get')
	},
	// 提交LED配置信息表表单 edit为true时为编辑，默认为新增
	ledConfigSubmitForm(data, edit = false) {
		return request(edit ? 'edit' : 'add', data)
	},
	// 删除LED配置信息表
	ledConfigDelete(data) {
		return request('delete', data)
	},
	// 获取LED配置信息表详情
	ledConfigDetail(data) {
		return request('detail', data, 'get')
	},
	// 更新LED配置信息表状态
	ledConfigUpdate(data) {
		return request('update', data)
	}
}
