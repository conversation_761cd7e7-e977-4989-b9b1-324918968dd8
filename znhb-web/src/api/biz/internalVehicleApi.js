import { baseRequest } from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/biz/internalVehicle/` + url, ...arg)

/**
 * 厂内运输车辆Api接口管理器
 *
 * <AUTHOR>
 * @date  2025/04/03 09:24
 **/
export default {
	// 获取厂内运输车辆分页
	internalVehiclePage(data) {
		return request('page', data, 'get')
	},
	// 提交厂内运输车辆表单 edit为true时为编辑，默认为新增
	internalVehicleSubmitForm(data, edit = false) {
		return request(edit ? 'edit' : 'add', data)
	},
	// 删除厂内运输车辆
	internalVehicleDelete(data) {
		return request('delete', data)
	},
	// 获取厂内运输车辆详情
	internalVehicleDetail(data) {
		return request('detail', data, 'get')
	},
	// 根据车牌号码获取厂内运输车辆详情
	detailByPlateNumber(data) {
		return request('detailByPlateNumber', data, 'get')
	}
}
