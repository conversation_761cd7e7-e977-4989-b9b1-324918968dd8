import { baseRequest } from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/biz/barrierGate/` + url, ...arg)

/**
 * t_barrier_gateApi接口管理器
 *
 * <AUTHOR>
 * @date  2025/03/21 10:05
 **/
export default {
	// 获取t_barrier_gate分页
	barrierGatePage(data) {
		return request('page', data, 'get')
	},
	// 提交t_barrier_gate表单 edit为true时为编辑，默认为新增
	barrierGateSubmitForm(data, edit = false) {
		return request(edit ? 'edit' : 'add', data)
	},
	// 删除t_barrier_gate
	barrierGateDelete(data) {
		return request('delete', data)
	},
	// 获取t_barrier_gate详情
	barrierGateDetail(data) {
		return request('detail', data, 'get')
	}
}
