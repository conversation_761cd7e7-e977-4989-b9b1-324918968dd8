import { baseRequest } from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/biz/vehicle/` + url, ...arg)
const ocrRequest = (url, ...arg) => baseRequest(`/hwy/ocr/` + url, ...arg)

/**
 * 车辆信息Api接口管理器
 *
 * <AUTHOR>
 * @date  2025/03/13 14:34
 **/
export default {
	// 获取车辆信息分页
	vehiclePage(data) {
		return request('page', data, 'get')
	},
	// 提交车辆信息表单 edit为true时为编辑，默认为新增
	vehicleSubmitForm(data, edit = false) {
		return request(edit ? 'edit' : 'add', data)
	},
	//h5提交车辆信息
	vehicleMobileSubmitForm(data) {
		return request('mobileAdd', data)
	},
	// 删除车辆信息
	vehicleDelete(data) {
		return request('delete', data)
	},
	// 获取车辆信息详情
	vehicleDetail(data) {
		return request('detail', data, 'get')
	},
	// 扫描车辆行驶证（双页 - 一张图片中包含首页和副页）
	vehicleScanDoubleLicense(imageBase64) {
		return ocrRequest('doubleLicense', {
			image: imageBase64
		})
	},
	// 扫描车辆行驶证（单页 - 首页和副页分别上传）
	vehicleScanSingleLicense(frontImageBase64, backImageBase64) {
		return ocrRequest('singleLicense', {
			frontImage: frontImageBase64,
			backImage: backImageBase64
		})
	},
	// 审核车辆信息
	vehicleAudit(params) {
		return request('audit', params)
	},
	// 添加导出方法
	vehicleExport(data) {
		return request('export', data, 'get', {
			responseType: 'blob'
		})
	},

	//根据车牌号码和颜色查询
	loadVehicleByPlateNumber(data){
		return request('loadVehicleByPlateNumber', data, 'get')
	},
	//根据车牌号码查询车辆信息
	loadVehicleForOrder(data){
		return request('loadVehicleForOrder', data, 'get')
	}

}
