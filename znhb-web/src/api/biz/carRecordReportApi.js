import { baseRequest } from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/biz/CarRecordReport/` + url, ...arg)

/**
 * CarRecordReportApi接口管理器
 *
 * <AUTHOR>
 * @date  2025/03/27 08:50
 **/
export default {
	// 获取CarRecordReport分页
	carRecordReportPage(data) {
		return request('page', data, 'get')
	},
	// 提交CarRecordReport表单 edit为true时为编辑，默认为新增
	carRecordReportSubmitForm(data, edit = false) {
		return request(edit ? 'edit' : 'add', data)
	},
	// 删除CarRecordReport
	carRecordReportDelete(data) {
		return request('delete', data)
	},
	// 获取CarRecordReport详情
	carRecordReportDetail(data) {
		return request('detail', data, 'get')
	}
}
