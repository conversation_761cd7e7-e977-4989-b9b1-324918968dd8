import { baseRequest } from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/biz/internalVehicleRecord/` + url, ...arg)

/**
 * 厂内运输台账Api接口管理器
 *
 * <AUTHOR>
 * @date  2025/04/03 11:37
 **/
export default {
	// 获取厂内运输台账分页
	internalVehicleRecordPage(data) {
		return request('page', data, 'get')
	},
	// 提交厂内运输台账表单 edit为true时为编辑，默认为新增
	internalVehicleRecordSubmitForm(data, edit = false) {
		return request(edit ? 'edit' : 'add', data)
	},
	// 删除厂内运输台账
	internalVehicleRecordDelete(data) {
		return request('delete', data)
	},
	// 获取厂内运输台账详情
	internalVehicleRecordDetail(data) {
		return request('detail', data, 'get')
	},
	// 确认出厂
	confirmExit(data) {
		return request('confirmExit', data, 'get')
	},
}
