import { baseRequest } from '@/utils/request'

const request = (url, ...arg) => {
  return baseRequest(`/biz/car/${url}`, ...arg)
}

/**
 * 小车信息API
 *
 * <AUTHOR>
 * @date 2025/04/10 15:00
 **/
export const carInfoApi = {
  // 获取小车信息分页
  carInfoPage (parameter) {
    return request('info/page', parameter, 'get')
  },
  // 添加小车信息
  carInfoAdd (parameter) {
    return request('info/add', parameter, 'post')
  },
  // 编辑小车信息
  carInfoEdit (parameter) {
    return request('info/edit', parameter, 'post')
  },
  // 删除小车信息
  carInfoDelete (parameter) {
    return request('info/delete', parameter, 'post')
  },
  // 获取小车信息详情
  carInfoDetail (parameter) {
    return request('info/detail', parameter, 'get')
  },
  // 下载小车信息导入模板
  carInfoDownloadTemplate () {
    return request('info/downloadTemplate', {}, 'get', { responseType: 'blob' })
  },
  // 导入小车信息
  carInfoImport (parameter) {
    return request('info/import', parameter, 'post', { headers: { 'Content-Type': 'multipart/form-data' } })
  }
}

/**
 * 小车进出记录API
 *
 * <AUTHOR>
 * @date 2025/04/10 15:00
 **/
export const carAccessRecordApi = {
  // 获取小车进出记录分页
  carAccessPage (parameter) {
    return request('access/page', parameter, 'get')
  },
  // 添加小车进厂记录
  carAccessEntry (parameter) {
    return request('access/entry', parameter, 'post')
  },
  // 登记小车出厂
  carAccessExit (parameter) {
    return request('access/exit', parameter, 'post')
  },
  // 编辑小车进出记录
  carAccessEdit (parameter) {
    return request('access/edit', parameter, 'post')
  },
  // 删除小车进出记录
  carAccessDelete (parameter) {
    return request('access/delete', parameter, 'post')
  },
  // 获取小车进出记录详情
  carAccessDetail (parameter) {
    return request('access/detail', parameter, 'get')
  }
}

/**
 * 小车进出规则API
 *
 * <AUTHOR>
 * @date 2025/04/10 15:00
 **/
export const carAccessRuleApi = {
  // 获取小车进出规则分页
  carAccessRulePage (parameter) {
    return request('accessRule/page', parameter, 'get')
  },
  // 添加小车进出规则
  carAccessRuleAdd (parameter) {
    return request('accessRule/add', parameter, 'post')
  },
  // 编辑小车进出规则
  carAccessRuleEdit (parameter) {
    return request('accessRule/edit', parameter, 'post')
  },
  // 删除小车进出规则
  carAccessRuleDelete (parameter) {
    return request('accessRule/delete', parameter, 'post')
  },
  // 批量删除小车进出规则
  carAccessRuleBatchDelete (parameter) {
    return request('accessRule/batchDelete', parameter, 'post')
  },
  // 获取小车进出规则详情
  carAccessRuleDetail (parameter) {
    return request('accessRule/detail', parameter, 'get')
  },
  // 获取小车进出规则列表
  carAccessRuleList (parameter) {
    return request('accessRule/list', parameter, 'get')
  }
}

/**
 * 小车规则关联API
 *
 * <AUTHOR>
 * @date 2025/04/10 15:00
 **/
export const carRuleRelationApi = {
  // 获取小车规则关联列表
  carRuleRelationList (parameter) {
    return request('ruleRelation/list', parameter, 'get')
  },
  // 添加小车规则关联
  carRuleRelationAdd (parameter) {
    return request('ruleRelation/add', parameter, 'post')
  },
  // 删除小车规则关联
  carRuleRelationDelete (parameter) {
    return request('ruleRelation/delete', parameter, 'post')
  },
  // 批量添加小车规则关联
  carRuleRelationBatchAdd (parameter) {
    return request('ruleRelation/batchAdd', parameter, 'post')
  }
}
