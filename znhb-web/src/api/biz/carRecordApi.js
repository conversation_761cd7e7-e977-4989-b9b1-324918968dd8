import { baseRequest } from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/biz/carRecord/` + url, ...arg)

/**
 * 运输台账Api接口管理器
 *
 * <AUTHOR>
 * @date  2025/03/22 10:05
 **/
export default {
	// 获取运输台账分页
	carRecordPage(data) {
		return request('page', data, 'get')
	},
	// 提交运输台账表单 edit为true时为编辑，默认为新增
	carRecordSubmitForm(data, edit = false) {
		return request(edit ? 'edit' : 'add', data)
	},
	// 删除运输台账
	carRecordDelete(data) {
		return request('delete', data)
	},
	// 获取运输台账详情
	carRecordDetail(data) {
		return request('detail', data, 'get')
	},
	// 确认出厂
	confirmExit(data) {
		return request('confirmExit', data, 'get')
	},
	// 出厂作废
	cancelExit(data) {
		return request('cancelExit', data, 'get')
	},
	// 更新计量数据
	confirmFetchMeterData(data) {
		return request('fetchMeterData', data, 'get')
	}
}
