import { baseRequest } from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/biz/nonRoadRecord/` + url, ...arg)

/**
 * 非移机械台账Api接口管理器
 *
 * <AUTHOR>
 * @date  2025/04/03 11:32
 **/
export default {
	// 获取非移机械台账分页
	nonRoadRecordPage(data) {
		return request('page', data, 'get')
	},
	// 提交非移机械台账表单 edit为true时为编辑，默认为新增
	nonRoadRecordSubmitForm(data, edit = false) {
		return request(edit ? 'edit' : 'add', data)
	},
	// 删除非移机械台账
	nonRoadRecordDelete(data) {
		return request('delete', data)
	},
	// 获取非移机械台账详情
	nonRoadRecordDetail(data) {
		return request('detail', data, 'get')
	}
}
