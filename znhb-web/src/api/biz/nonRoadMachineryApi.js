	import { baseRequest } from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/biz/nonRoadMachinery/` + url, ...arg)

/**
 * 非道路移动机械信息表Api接口管理器
 *
 * <AUTHOR>
 * @date  2025/04/03 10:31
 **/
export default {
	// 获取非道路移动机械信息表分页
	nonRoadMachineryPage(data) {
		return request('page', data, 'get')
	},
	// 提交非道路移动机械信息表表单 edit为true时为编辑，默认为新增
	nonRoadMachinerySubmitForm(data, edit = false) {
		return request(edit ? 'edit' : 'add', data)
	},
	// 删除非道路移动机械信息表
	nonRoadMachineryDelete(data) {
		return request('delete', data)
	},
	// 获取非道路移动机械信息表详情
	nonRoadMachineryDetail(data) {
		return request('detail', data, 'get')
	},
	// 根据车牌号获取
	detailByPlateNumber(data) {
		return request('detailByPlateNumber', data, 'get')
	}
}
