import { baseRequest } from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/biz/order/` + url, ...arg)

/**
 * 车辆派单表Api接口管理器
 *
 * <AUTHOR>
 * @date  2025/03/20 10:29
 **/
export default {
	// 获取车辆派单表分页
	orderPage(data) {
		return request('page', data, 'get')
	},
	// 提交车辆派单表表单 edit为true时为编辑，默认为新增
	orderSubmitForm(data, edit = false) {
		return request(edit ? 'edit' : 'add', data)
	},
	// 删除车辆派单表
	orderDelete(data) {
		return request('delete', data)
	},
	// 获取车辆派单表详情
	orderDetail(data) {
		return request('detail', data, 'get')
	}
}
