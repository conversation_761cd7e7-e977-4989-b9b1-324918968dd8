import { baseRequest } from '@/utils/request'

const request = (url, ...arg) => {
  return baseRequest(`/biz/area/${url}`, ...arg)
}

/**
 * 区域API
 */
export default {
  /**
   * 获取区域分页列表
   *
   * @param {Object} params - 查询参数
   * @returns {Promise} - 请求Promise
   */
  areaPage(params) {
    return request('page', params, 'get')
  },

  /**
   * 添加区域
   *
   * @param {Object} data - 区域数据
   * @returns {Promise} - 请求Promise
   */
  areaAdd(data) {
    return request('add', data)
  },

  /**
   * 编辑区域
   *
   * @param {Object} data - 区域数据
   * @returns {Promise} - 请求Promise
   */
  areaEdit(data) {
    return request('edit', data)
  },

  /**
   * 删除区域
   *
   * @param {Object} data - 区域ID
   * @returns {Promise} - 请求Promise
   */
  areaDelete(data) {
    return request('delete', data)
  },

  /**
   * 获取区域详情
   *
   * @param {Object} params - 区域ID
   * @returns {Promise} - 请求Promise
   */
  areaDetail(params) {
    return request('detail', params, 'get')
  },

  /**
   * 获取区域树形列表
   *
   * @returns {Promise} - 请求Promise
   */
  areaTree() {
    return request('tree', {}, 'get')
  }
}
