import { baseRequest } from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/biz/device/` + url, ...arg)

/**
 * 设备信息Api接口管理器
 *
 * <AUTHOR>
 * @date  2025/03/20 16:10
 **/
export default {
	// 获取设备信息分页
	devicePage(data) {
		return request('page', data, 'get')
	},
	// 提交设备信息表单 edit为true时为编辑，默认为新增
	deviceSubmitForm(data, edit = false) {
		return request(edit ? 'edit' : 'add', data)
	},
	// 删除设备信息
	deviceDelete(data) {
		return request('delete', data)
	},
	// 获取设备信息详情
	deviceDetail(data) {
		return request('detail', data, 'get')
	},
	// 获取驱动类
	getDriverClass(data){
		return request('getDriverClass', data, 'get')
	},
	// 设备布防
	deviceDefend(data){
		return request('defend', data, 'post')
	},
	// 设备布防
	closeDefend(data){
		return request('clsoeDefend', data, 'post')
	},
	// 获取车牌识别机
	getPlateCameraList(data) {
		return request('getPlateCameraList', data, 'get')
	},
	// 获取车身识别机
	getBodyCameraList(data) {
		return request('getBodyCameraList', data, 'get')
	},
	// 获取车尾识别机
	getTailCameraList(data) {
		return request('getTailCameraList', data, 'get')
	},
	// 获取LED显示屏列表
	getLedList(data) {
		return request('getLedList', data, 'get')
	},
	// 获取ISC设备列表
	getIscList(data) {
		return request('getIscList', data, 'get')
	}
}
