import { baseRequest } from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/biz/materialcategory/` + url, ...arg)

/**
 * 物料分类Api接口管理器
 *
 * <AUTHOR>
 * @date  2025/03/20 11:29
 **/
export default {
	// 获取物料分类分页
	materialCategoryPage(data) {
		return request('page', data, 'get')
	},
	// 提交物料分类表单 edit为true时为编辑，默认为新增
	materialCategorySubmitForm(data, edit = false) {
		return request(edit ? 'edit' : 'add', data)
	},
	// 删除物料分类
	materialCategoryDelete(data) {
		return request('delete', data)
	},
	// 获取物料分类详情
	materialCategoryDetail(data) {
		return request('detail', data, 'get')
	}
}
