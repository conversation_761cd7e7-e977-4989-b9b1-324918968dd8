<template>
	<div :class="[props.prefixCls]">
		<slot name="subtitle">
			<div :class="[`${props.prefixCls}-subtitle`]">
				{{ typeof props.subTitle === 'string' ? props.subTitle : props.subTitle() }}
			</div>
		</slot>
		<div class="number-info-value">
			<span>{{ props.total }}</span>
			<span class="sub-total">
				{{ props.subTotal }}
				<span v-if="`${props.status}` === 'up'"><caret-up-outlined /></span>
				<span v-else><caret-down-outlined /></span>
			</span>
		</div>
	</div>
</template>

<script setup name="NumberInfo">
	import { CaretDownOutlined, CaretUpOutlined } from '@ant-design/icons-vue'

	const props = defineProps({
		prefixCls: {
			type: String,
			default: 'ant-pro-number-info'
		},
		total: {
			type: Number,
			required: true
		},
		subTotal: {
			type: Number,
			required: true
		},
		subTitle: {
			type: [String, Function],
			default: ''
		},
		status: {
			type: String,
			default: 'up'
		}
	})
</script>

<style lang="less" scoped>
	@import './index.less';
</style>
