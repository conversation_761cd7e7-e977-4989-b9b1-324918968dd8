<template>
	<a-modal v-if="isModal" :visible="visible" @cancel="cancel" v-bind="$attrs">
		<template v-for="slotKey in slotKeys" #[slotKey]>
			<slot :name="slotKey" />
		</template>
	</a-modal>
	<a-drawer v-else :visible="visible" v-bind="$attrs" @close="cancel" :footer-style="{ textAlign: 'right' }">
		<template v-for="slotKey in slotKeys" #[slotKey]>
			<slot :name="slotKey" />
		</template>
	</a-drawer>
</template>

<script setup>
	import { useSlots } from 'vue'
	import { globalStore } from '@/store'
	const slots = useSlots()

	
	const store = globalStore()
	const props = defineProps({
		visible: {
			type: Boolean,
			default: false,
			required: true
		},
		type: {
			type: String,
			default: '',
			validator: (value) => {
				// 为空表示使用全局设置，否则必须是 'modal' 或 'drawer'
				return value === '' || value === 'modal' || value === 'drawer'
			}
		}
	})

	const FormContainerTypeEnum = {
		DRAWER: 'drawer',
		MODAL: 'modal'
	}

	const formStyle = computed(() => {
		return store.formStyle
	})
	const slotKeys = computed(() => {
		return Object.keys(slots)
	})
	const isModal = computed(() => {
		// 优先使用传入的 type 属性，如果没有提供则使用全局 store 设置
		const effectiveType = props.type || formStyle.value
		return FormContainerTypeEnum.MODAL === effectiveType
	})
	const emit = defineEmits(['close'])
	const cancel = () => {
		emit('close')
	}
</script>

<script>
	// 声明额外的选项
	export default {
		inheritAttrs: false
	}
</script>
