<template>
    <div :class="inputClass">
      <ul class="ipAdress">
        <li v-for="(item, index) in ipAddress" :key="index">
          <input :ref="el => getInputRef(el, index)" v-model="item.value" type="text" class="ipInputClass"
            :disabled="disabled" @input="handleInput(item, index)" @keyup="$event => turnIpPosition(item, index, $event)"
            @blur="handleBlur" @mouseup="handleMouseUp(index)" @paste="handlePaste($event, index)" @focus="handleFocus(index)" @click="handleClick(index)" />
          <div></div>
        </li>
      </ul>
    </div>
  </template>
  
  <script lang="ts" setup name="routePage">
  import { ref, watch, computed } from 'vue'
  
  // 接收来自上层的数据
  const props = defineProps({
    value: { type: String, default: '' },
    disabled: { type: Boolean, default: false },
    size: { type: String, default: 'default' },
    status: { type: String }
  })
  
  // 更新数据
  const $emits = defineEmits(['update:value', 'blur'])
  
  // 存储四个ref
  const ipInputRefs = ref<HTMLElement[]>([]);
  // 存储左右标识位
  let markFlag = ref([
    {
      left: false,
      right: false
    },
    {
      left: false,
      right: false
    },
    {
      left: false,
      right: false
    },
    {
      left: false,
      right: false
    }
  ])
  
  // 新增：记录是否正在编辑每个输入框
  const isEditing = ref([false, false, false, false]);
  
  // 鼠标点击
  const handleMouseUp = (index: any) => {
    let input = ipInputRefs.value[index]
    // 全为false
    markFlag.value.forEach(item => {
      item.left = false
      item.right = false
    })
    // 证明在开始阶段
    if (input.selectionStart == 0) {
      markFlag.value[index].left = true
    } else {
      markFlag.value[index].left = false
    }
    // 证明在结束
    if (input.selectionStart == (input.value || '').length) {
      markFlag.value[index].right = true
    } else {
      markFlag.value[index].right = false
    }
    
    // 检查是否选中了所有文本，如果是，则设置为编辑模式
    if (input.selectionStart === 0 && input.selectionEnd === input.value.length && input.value.length > 0) {
      isEditing.value[index] = true;
    }
  }
  
  // 获取四个input refs
  const getInputRef = (el: any, index: number) => {
    if (el) {
      ipInputRefs.value[index] = el;
    }
  };
  // 声明IP存储类型
  interface IpType {
    value: string
  }
  // 定义要显示的四个ip
  let ipAddress = ref<IpType[]>([
    {
      value: "",
    },
    {
      value: "",
    },
    {
      value: "",
    },
    {
      value: "",
    },
  ])
  // 初始化显示数据
  const initShowData = () => {
    // 判断不合理行为
    if (props.value === '' || props.value === null || props.value === undefined) {
      ipAddress.value.forEach(item => {
        item.value = ''
      })
    } else {
      let ipList = props.value.split('.')
      ipAddress.value.forEach((item: IpType, index: number) => {
        item.value = ipList[index]
      })
    }
  }
  
  // 检查ip输入
  const checkIpVal = (item: any) => {
    let val = item.value;
    // 处理非数字
    val = val.toString().replace(/[^0-9]/g, "");
    val = parseInt(val, 10);
    if (isNaN(val)) {
      val = "";
    } else {
      val = val < 0 ? 0 : val;
      if (val > 255) {
        // 判断val是几位数
        let num = (val + '').length
        if (num > 3) {
          val = parseInt((val + '').substring(0, 3))
        } else {
          val = 255
        }
      }
    }
    item.value = val;
  }
  
  // 判断光标左右移动位置
  const turnIpPosition = (item: IpType, index: number, event: any) => {
    let e = event || window.event;
    if (e.keyCode === 37) {
      // 左箭头向左跳转，左一不做任何措施
      if (index == 0) {
        return
      }
      if (e.currentTarget.selectionStart === 0) {
        if (markFlag.value[index].left) {
          handleFocus(index - 1, 'toLeft')
          markFlag.value[index].left = false
          markFlag.value[index].right = false
        } else {
          markFlag.value[index].left = true
        }
      } else {
        markFlag.value[index].right = false
        markFlag.value[index].left = false
      }
    } else if (e.keyCode == 39) {
      // 右箭头向右跳转，右一不做任何措施
      markFlag.value[index].left = false
      let start = e.currentTarget.selectionStart
      if (index != 3 && start === item.value.toString().length) {
        if (markFlag.value[index].right) {
          handleFocus(index + 1, 'toRight')
          markFlag.value[index].left = false
          markFlag.value[index].right = false
        } else {
          markFlag.value[index].right = true
        }
      } else {
        markFlag.value[index].right = false
      }
    } else if (e.keyCode === 8) {
      // 删除键把当前数据删除完毕后会跳转到前一个input，左一不做任何处理
      if (index !== 0 && e.currentTarget.selectionStart === 0) {
        if (markFlag.value[index].left) {
          ipInputRefs.value[index - 1].focus();
          markFlag.value[index].left = false
        } else {
          markFlag.value[index].left = true
        }
      }
    } else if (e.keyCode === 13 || e.keyCode === 32) {
      // 回车键、空格键向右跳转
      if (index !== 3) {
        // 如果下一个输入框有内容，则清空它
        if (ipAddress.value[index + 1].value) {
          ipAddress.value[index + 1].value = '';
        }
        ipInputRefs.value[index + 1].focus();
      }
    }
    else if (e.keyCode === 110 || e.keyCode === 190) {
      // 点 . 向右跳转
      if (item.value == '') {
        return
      }
      if (index !== 3) {
        // 如果下一个输入框有内容，则清空它
        if (ipAddress.value[index + 1].value) {
          ipAddress.value[index + 1].value = '';
        }
        ipInputRefs.value[index + 1].focus();
      }
    }
    else if (item.value.toString().length === 3) {
      // 满3位，光标自动向下一个文本框
      if (index !== 3) {
        // 如果下一个输入框有内容，则清空它
        if (ipAddress.value[index + 1].value) {
          ipAddress.value[index + 1].value = '';
        }
        ipInputRefs.value[index + 1].focus();
      }
    }
  }
  
  // 处理聚焦
  const handleFocus = (index: number, direction: string = 'default') => {
    // 设置当前位置为选中状态
    let input = ipInputRefs.value[index]
    input.focus()
    let value = input.value
    
    // 如果是通过自动跳转过来的（而不是用户手动点击），则清空内容
    if (direction === 'toRight' || direction === 'toLeft') {
      // 清空当前输入框内容
      ipAddress.value[index].value = '';
    }
    
    // null 左右全部设置为true,可以直接跳转
    if ((value || '').length == 0) {
      markFlag.value[index].right = true
      markFlag.value[index].left = true
    } else {
      if (direction == 'toRight') {
        // 可以直接跳回
        markFlag.value[index].left = true
        // 设置光标为左边第一个
        ipInputRefs.value[index].setSelectionRange(0, 0)
        // 设置上一个的右边标识为false
        markFlag.value[index - 1] && (markFlag.value[index - 1].right = false)
      } else {
        // 直接跳回
        markFlag.value[index].right = true
        // 设置后一个侧边为false
        markFlag.value[index + 1] && (markFlag.value[index + 1].left = false)
      }
    }
  }
  
  
  // 格式化补零方法
  const formatter = (val: string) => {
    let value = val.toString();
    if (value.length === 2) {
      value = "0" + value;
    } else if (value.length === 1) {
      value = "00" + value;
    } else if (value.length === 0) {
      value = "000";
    }
    return value;
  }
  
  // 监听数据变化,并初始化显示四个数据
  watch(() => props.value, () => {
    initShowData()
  }, {
    immediate: true
  })
  
  // 监听ipAddress数据变化
  watch(ipAddress, () => {
    let str = ipAddress.value.map(item => {
      return (item.value !== null && item.value !== '') ? item.value : '0';
    }).join(".");
    
    $emits('update:value', str);
  }, {
    deep: true
  })
  
  const handleBlur = () => {
    // 重置所有编辑状态
    isEditing.value = [false, false, false, false];
    $emits('blur')
  }
  
  // 新增：处理每次输入变化
  const handleInput = (item: IpType, index: number) => {
    // 检查IP输入值
    checkIpVal(item);
    
    // 仅当输入第三位数字且不是在编辑模式时才自动跳转
    const valueLength = item.value.toString().length;
    
    if (valueLength === 3 && index !== 3 && !isEditing.value[index]) {
      setTimeout(() => {
        // 如果下一个输入框有内容，则清空它
        if (ipAddress.value[index + 1].value) {
          ipAddress.value[index + 1].value = '';
        }
        ipInputRefs.value[index + 1].focus();
      }, 10);
    }
    
    // 一旦用户输入了内容，取消编辑状态
    if (valueLength > 0) {
      isEditing.value[index] = false;
    }
  }
  
  // 新增：支持粘贴完整IP地址
  const handlePaste = (e: ClipboardEvent, index: number) => {
    const pastedText = e.clipboardData?.getData('text') || '';
    const ipPattern = /^(\d{1,3})\.(\d{1,3})\.(\d{1,3})\.(\d{1,3})$/;
    
    if (ipPattern.test(pastedText)) {
      e.preventDefault();
      const parts = pastedText.split('.');
      ipAddress.value.forEach((item, idx) => {
        let num = parseInt(parts[idx], 10);
        if (isNaN(num)) num = 0;
        item.value = num > 255 ? '255' : num.toString();
      });
      
      // 聚焦到最后一个输入框
      setTimeout(() => {
        ipInputRefs.value[3].focus();
      }, 10);
    }
  }
  
  // 新增：修改点击事件
  const handleClick = (index: number) => {
    // 自动选中文本，便于重写
    if (ipInputRefs.value[index] && ipAddress.value[index].value) {
      ipInputRefs.value[index].select();
      isEditing.value[index] = true;
    }
  }
  
  // 根据 size 和 status 设置样式类
  const inputClass = computed(() => {
    return {
      'disabled': props.disabled,
      [`ip-input-${props.size}`]: props.size,
      [`ip-input-${props.status}`]: props.status
    }
  })
  
  </script>
  <style lang="scss" scoped>
  .disabled {
    cursor: not-allowed;
    background-color: #f5f7fa;
    opacity: 0.7;
  
    .ipAdress {
      li {
        .ipInputClass {
          color: #c3c4cc;
          cursor: not-allowed;
        }
      }
    }
  }
  
  .ipAdress {
    display: flex;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    line-height: 40px;
    width: 100%;
    height: 40px;
    padding-inline-start: 0px;
    padding-left: 10px;
    padding-right: 10px;
    box-sizing: border-box;
    margin: 0;
    &:focus-within {
      border-color: #409eff;
      box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
    }
  }
  
  .ipAdress li {
    position: relative;
    margin: 0;
    list-style-type: none;
  }
  
  .ipInputClass {
    border: none;
    width: 50px;
    height: 23px;
    text-align: center;
    color: #606266;
    background: transparent;
    transition: all 0.3s;
    
    &:focus {
      background-color: rgba(64, 158, 255, 0.1);
    }
  }
  
  .ipAdress li div {
    position: absolute;
    bottom: 12px;
    right: 0;
    border-radius: 50%;
    background: #b6b8bc;
    width: 2px;
    height: 2px;
  }
  
  /*只需要3个div*/
  .ipAdress li:last-child div {
    display: none;
  }
  
  /*取消掉默认的input focus状态*/
  .ipAdress input:focus {
    outline: none;
  }
  /* 添加到 <style> 部分 */
.ip-input-default {
  .ipAdress {
    height: 40px;
    line-height: 40px;
    
    .ipInputClass {
      height: 23px;
    }
  }
}

.ip-input-small {
  .ipAdress {
    height: 32px;
    line-height: 32px;
    
    .ipInputClass {
      height: 20px;
      font-size: 12px;
    }
  }
}

.ip-input-large {
  .ipAdress {
    height: 48px;
    line-height: 48px;
    
    .ipInputClass {
      height: 28px;
      font-size: 16px;
    }
  }
}

.ip-input-mini {
  .ipAdress {
    height: 28px;
    line-height: 28px;
    
    .ipInputClass {
      height: 18px;
      font-size: 11px;
    }
  }
}
  </style>
  
  