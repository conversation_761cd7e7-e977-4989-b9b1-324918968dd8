package com.znhb.core.config;

import org.springframework.boot.autoconfigure.web.servlet.WebMvcRegistrations;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.MediaType;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.config.annotation.ContentNegotiationConfigurer;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.ViewControllerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import org.springframework.web.servlet.mvc.method.RequestMappingInfo;
import org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.core.Ordered;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;

@Configuration
public class WebMvcConfig implements WebMvcConfigurer {

    // MIME类型映射
    private final Map<String, String> mimeMapping = new HashMap<>();
    
    public WebMvcConfig() {
        // 初始化MIME类型映射
        mimeMapping.put("js", "application/javascript");
        mimeMapping.put("mjs", "application/javascript");
        mimeMapping.put("css", "text/css");
        mimeMapping.put("html", "text/html");
        mimeMapping.put("htm", "text/html");
        mimeMapping.put("json", "application/json");
        mimeMapping.put("png", "image/png");
        mimeMapping.put("jpg", "image/jpeg");
        mimeMapping.put("jpeg", "image/jpeg");
        mimeMapping.put("svg", "image/svg+xml");
        mimeMapping.put("ico", "image/x-icon");
    }

    @Override
    public void configureContentNegotiation(ContentNegotiationConfigurer configurer) {
        // 配置正确的MIME类型
        configurer.mediaType("js", MediaType.valueOf("application/javascript"));
        // 添加ES模块支持
        configurer.mediaType("mjs", MediaType.valueOf("application/javascript"));
        configurer.mediaType("css", MediaType.valueOf("text/css"));
        configurer.mediaType("html", MediaType.TEXT_HTML);
        configurer.mediaType("json", MediaType.APPLICATION_JSON);
        configurer.mediaType("png", MediaType.valueOf("image/png"));
        configurer.mediaType("jpg", MediaType.valueOf("image/jpeg"));
        configurer.mediaType("svg", MediaType.valueOf("image/svg+xml"));
        // 确保响应头设置正确的Content-Type
        configurer.useRegisteredExtensionsOnly(false);
        // 强制使用路径扩展名来决定内容类型
        configurer.favorPathExtension(true);
        // 忽略请求中的Accept头
        configurer.ignoreAcceptHeader(true);
    }

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // 添加静态资源处理，并设置高优先级
        registry.addResourceHandler("/assets/**")
                .addResourceLocations("classpath:/static/assets/")
                .setCachePeriod(3600)
                .resourceChain(true);

        // 处理根目录下的静态文件
        registry.addResourceHandler("/*.js", "/*.css", "/*.html", "/*.ico", "/*.json")
                .addResourceLocations("classpath:/static/")
                .setCachePeriod(3600)
                .resourceChain(true);

        registry.addResourceHandler("/img/**")
                .addResourceLocations("classpath:/static/img/")
                .setCachePeriod(3600)
                .resourceChain(true);
                
        // 专门处理海康SDK资源
        registry.addResourceHandler("/hik-sdk/**")
                .addResourceLocations("classpath:/static/hik-sdk/")
                .setCachePeriod(0) // 禁用缓存以便于调试
                .resourceChain(true);

        // 专门处理Druid监控页面的资源
        registry.addResourceHandler("/druid/**")
                .addResourceLocations("classpath:/META-INF/resources/druid/")
                .setCachePeriod(0)
                .resourceChain(false);

        // 通用资源处理
        registry.addResourceHandler("/**")
                .addResourceLocations("classpath:/static/")
                .setCachePeriod(3600)
                .resourceChain(true);
    }

    @Override
    public void addViewControllers(ViewControllerRegistry registry) {
        // 处理前端路由，确保所有非静态资源的请求都重定向到index.html
        // 排除druid监控页面的路径
        registry.addViewController("/{spring:[^.]*}")
                .setViewName("forward:/index.html");

        registry.addViewController("/{spring:[^.]*}/{spring2:[^.]*}")
                .setViewName("forward:/index.html");

        registry.addViewController("/{spring:[^.]*}/{spring2:[^.]*}/{spring3:[^.]*}")
                .setViewName("forward:/index.html");

        registry.addViewController("/{spring:[^.]*}/{spring2:[^.]*}/{spring3:[^.]*}/{spring4:[^.]*}")
                .setViewName("forward:/index.html");
    }

    /**
     * 注册自定义的MIME类型过滤器
     */
    @Bean
    public FilterRegistrationBean<ContentTypeFilter> contentTypeFilter() {
        FilterRegistrationBean<ContentTypeFilter> registration = new FilterRegistrationBean<>();
        registration.setFilter(new ContentTypeFilter(mimeMapping));
        registration.addUrlPatterns("/*");
        registration.setName("contentTypeFilter");
        registration.setOrder(Ordered.HIGHEST_PRECEDENCE);
        return registration;
    }

    /**
     * 内部类，用于处理静态资源的Content-Type
     */
    public static class ContentTypeFilter implements Filter {
        private final Map<String, String> mimeMapping;

        public ContentTypeFilter(Map<String, String> mimeMapping) {
            this.mimeMapping = mimeMapping;
        }

        @Override
        public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
                throws IOException, ServletException {
            HttpServletRequest httpRequest = (HttpServletRequest) request;
            HttpServletResponse httpResponse = (HttpServletResponse) response;
            
            String requestURI = httpRequest.getRequestURI();
            int dotIndex = requestURI.lastIndexOf('.');
            
            if (dotIndex > 0 && dotIndex < requestURI.length() - 1) {
                String extension = requestURI.substring(dotIndex + 1).toLowerCase();
                String mimeType = mimeMapping.get(extension);
                
                if (mimeType != null) {
                    httpResponse.setContentType(mimeType + ";charset=UTF-8");
                }
            }
            
            chain.doFilter(request, response);
        }

        @Override
        public void init(FilterConfig filterConfig) throws ServletException {
            // 无需初始化
        }

        @Override
        public void destroy() {
            // 无需销毁操作
        }
    }

    /**
     * 添加自定义资源处理器，专门处理Druid监控页面
     */
    @Bean
    public WebMvcRegistrations webMvcRegistrations() {
        return new WebMvcRegistrations() {
            @Override
            public RequestMappingHandlerMapping getRequestMappingHandlerMapping() {
                return new RequestMappingHandlerMapping() {
                    @Override
                    protected boolean isHandler(Class<?> beanType) {
                        return super.isHandler(beanType);
                    }

                    @Override
                    protected void registerHandlerMethod(Object handler, Method method, RequestMappingInfo mapping) {
                        // 让原始的处理器注册方法先执行
                        super.registerHandlerMethod(handler, method, mapping);
                    }
                };
            }
        };
    }
}