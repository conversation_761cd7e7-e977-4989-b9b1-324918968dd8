spring:
  redis:
    database: 1
    host: 127.0.0.1
    port: 6379
    password:
    timeout: 10s
    lettuce:
      pool:
        max-active: 200
        max-wait: -1ms
        max-idle: 10
        min-idle: 0
  datasource:
    dynamic:
      strict: true
      datasource:
        master:
          driver-class-name: com.mysql.cj.jdbc.Driver
          url: *****************************************************************************************************************************************************************************************************************************************************************='TRADITIONAL'&autoReconnect=true
          username: root
          password: hizp!bx1@elc@cpx1swqh77urzbvc@ynhau$!$Otc2024

      druid:
        stat-view-servlet:
          enabled: true
          login-username: admin
          login-password: 88888888
          url-pattern: /druid/*
        public-key: MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAMWiTVtdXFVrgFHDDKELZM0SywkWY3KjugN90eY5Sogon1j8Y0ClPF7nx3FuE7pAeBKiv7ChIS0vvx/59WUpKmUCAwEAAQ==
        initial-size: 5
        max-active: 20
        min-idle: 5
        max-wait: 60000
        pool-prepared-statements: true
        max-pool-prepared-statement-per-connection-size: 20
        validation-query-timeout: 2000
        test-on-borrow: false
        test-on-return: false
        test-while-idle: true
        time-between-eviction-runs-millis: 60000
        min-evictable-idle-time-millis: 300000
        filters: stat
        break-after-acquire-failure: false

snowy:
  config:
    common:
      backend-url: http://10.244.17.25:82

hiksdk:
  enabled: true
  linux-lib-path: /hik/linux
  windows-lib-path: \hik\windows
  connect-timeout: 10
  reconnect-time: 100
  check-online-timeout: 30
  check-online-net-fail-max: 3

ocr:
  hwy:
    enabled: true

plate:
  handler-name: yueDongAlarmHandler
  api-name: yueDongCommonApi