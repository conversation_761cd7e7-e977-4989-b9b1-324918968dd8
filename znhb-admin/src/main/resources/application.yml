server:
  port: 82
  servlet:
    encoding:
      charset: UTF-8
      force: true
  # 优雅关闭配置
  shutdown: graceful

spring:
  profiles:
    active: ${spring.active}

  # 确保Druid监控页面可以正常访问
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher
    # 静态资源MIME类型映射配置
    contentnegotiation:
      favor-parameter: false
      favor-path-extension: true
      media-types:
        js: application/javascript
        json: application/json
        html: text/html
        css: text/css
    # 添加静态资源处理配置
    static-path-pattern: /**

  # 配置Druid监控
  datasource:
    druid:
      stat-view-servlet:
        enabled: true
        url-pattern: /druid/*
        reset-enable: true
        login-username: admin
        login-password: 88888888
        allow:
        deny:

  # 优雅关闭超时时间
  lifecycle:
    timeout-per-shutdown-phase: 30s

  # 添加静态资源配置
  web:
    resources:
      static-locations: classpath:/static/
      cache:
        period: 3600
      chain:
        strategy:
          content:
            enabled: true
      # 启用资源映射
      add-mappings: true

  servlet:
    multipart:
      max-request-size: 100MB
      max-file-size: 100MB



  jackson:
    time-zone: GMT+8
    date-format: yyyy-MM-dd HH:mm:ss
    locale: zh_CN

mybatis-plus:
  configuration:
    ##设置不打印sql语句 org.apache.ibatis.logging.nologging.NoLoggingImpl      org.apache.ibatis.logging.stdout.StdOutImpl
    log-impl: org.apache.ibatis.logging.nologging.NoLoggingImpl
    jdbc-type-for-null: null
    # 开启驼峰命名
    map-underscore-to-camel-case: true
  global-config:
    banner: false
    enable-sql-runner: true
    db-config:
      id-type: ASSIGN_ID
      logic-delete-field: DELETE_FLAG
      logic-delete-value: DELETED
      logic-not-delete-value: NOT_DELETE
  mapper-locations: classpath*:com/znhb/**/mapping/*.xml
  type-handlers-package: com.znhb.common.handler


sa-token:
  token-name: token
  timeout: 2592000
  activity-timeout: -1
  is-concurrent: true
  is-share: false
  max-login-count: -1
  token-style: random-32
  is-log: false
  is-print: false
  alone-redis:
    database: 2
    host: ${spring.redis.host}
    port: ${spring.redis.port}
    password: ${spring.redis.password}
    timeout: ${spring.redis.timeout}
    lettuce:
      pool:
        max-active: ${spring.redis.lettuce.pool.max-active}
        max-wait: ${spring.redis.lettuce.pool.max-wait}
        max-idle: ${spring.redis.lettuce.pool.max-idle}
        min-idle: ${spring.redis.lettuce.pool.min-idle}

knife4j:
  enable: true
  production: false
  basic:
    enable: true
    username: admin
    password: 123456
  setting:
    enableOpenApi: false
    enableSwaggerModels: false
    enableFooter: false
    enableFooterCustom: true
    footerCustomContent: "Apache License 2.0 | Copyright 2022-[SNOWY](https://www.xiaonuo.vip)"


# Actuator配置
management:
  endpoints:
    web:
      exposure:
        # 暴露shutdown端点
        include: shutdown
  endpoint:
    # 启用shutdown端点
    shutdown:
      enabled: true