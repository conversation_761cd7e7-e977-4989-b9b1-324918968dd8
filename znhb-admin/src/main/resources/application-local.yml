spring:
  redis:
    database: 1
    host: 127.0.0.1
    port: 6379
    password:
    timeout: 10s
    lettuce:
      pool:
        max-active: 200
        max-wait: -1ms
        max-idle: 10
        min-idle: 0
  datasource:
    dynamic:
      aop:
        packages: com.znhb.biz.modular
      strict: true
      datasource:
        master:
          driver-class-name: com.mysql.cj.jdbc.Driver
          url: *******************************************************************************************************************************************************************************
          username: root
          password: 123456
        slave:
          driver-class-name: com.mysql.cj.jdbc.Driver
          url: *****************************************************************************************************************************************************************************
          username: root
          password: 123456
      #          driver-class-name: com.mysql.cj.jdbc.Driver
      #          url: jdbc:mysql://***********:3306/znhb-cyft?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=false&serverTimezone=GMT%2B8
      #          username: root
      #          password: jjhb2024


      # postgres配置
      #master:
      #  driver-class-name: org.postgresql.Driver
      #  url: **************************************
      #  username: postgres
      #  password: 123456
      #strict: true

      # oracle配置
      #master:
      #  driver-class-name: oracle.jdbc.driver.OracleDriver
      #  url: ***********************************************************
      #  username: SNOWY
      #  password: 12345678
      #strict: true

      # mssql配置
      #master:
      #  driver-class-name: com.microsoft.sqlserver.jdbc.SQLServerDriver
      #  url: **************************************************
      #  username: sa
      #  password: 123456
      #strict: true

      # dm数据库配置
      #master:
      #  driver-class-name: dm.jdbc.driver.DmDriver
      #  url: jdbc:dm://localhost:5236/SYSDBA
      #  username: SYSDBA
      #  password: SYSDBA
      #strict: true

      # kingbase数据库配置
      #master:
      #  driver-class-name: com.kingbase8.Driver
      #  url: **************************************
      #  username: SYSTEM
      #  password: 123456
      #strict: true

      druid:
        stat-view-servlet:
          enabled: true
          login-username: admin
          login-password: 88888888
          url-pattern: /druid/*
          allow:
          deny:
        public-key: MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAMWiTVtdXFVrgFHDDKELZM0SywkWY3KjugN90eY5Sogon1j8Y0ClPF7nx3FuE7pAeBKiv7ChIS0vvx/59WUpKmUCAwEAAQ==
        initial-size: 5
        max-active: 20
        min-idle: 5
        max-wait: 60000
        pool-prepared-statements: true
        max-pool-prepared-statement-per-connection-size: 20
        validation-query-timeout: 2000
        test-on-borrow: false
        test-on-return: false
        test-while-idle: true
        time-between-eviction-runs-millis: 60000
        min-evictable-idle-time-millis: 300000
        filters: stat
        break-after-acquire-failure: false


snowy:
  config:
    common:
      backend-url: http://localhost:82

hiksdk:
  enabled: false
  linux-lib-path: /hik/linux
  windows-lib-path: \hik\windows
  connect-timeout: 10
  reconnect-time: 100
  check-online-timeout: 30
  check-online-net-fail-max: 3

ocr:
  hwy:
    enabled: false

plate:
  handler-name: ruiGangAlarmHandler
  api-name: ruiGangCommonApi