<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.znhb</groupId>
        <artifactId>znhb-snowy</artifactId>
        <version>2.0.0</version>
    </parent>

    <artifactId>znhb-admin</artifactId>
    <packaging>jar</packaging>
    <description>主启动模块</description>

    <dependencies>

        <!-- test -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- junit -->
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>${junit.version}</version>
            <scope>test</scope>
        </dependency>

        <!-- dynamic-datasource -->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
        </dependency>

        <!-- mysql -->
         <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>

        <!-- postgresql -->
        <!--<dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
        </dependency>-->

        <!-- oracle -->
        <!--<dependency>
            <groupId>com.oracle.database.jdbc</groupId>
            <artifactId>ojdbc8</artifactId>
        </dependency>-->
        <!--<dependency>
            <groupId>com.oracle.database.nls</groupId>
            <artifactId>orai18n</artifactId>
        </dependency>-->

        <!-- mssql -->
        <!--<dependency>
            <groupId>com.microsoft.sqlserver</groupId>
            <artifactId>mssql-jdbc</artifactId>
        </dependency>-->

        <!-- 达梦数据库 -->
        <!--<dependency>
            <groupId>com.dameng</groupId>
            <artifactId>DmJdbcDriver18</artifactId>
        </dependency>-->

        <!-- 人大金仓数据库 -->
        <!--<dependency>
            <groupId>cn.com.kingbase</groupId>
            <artifactId>kingbase8</artifactId>
        </dependency>-->

        <!-- 登录鉴权插件 -->
        <dependency>
            <groupId>com.znhb</groupId>
            <artifactId>znhb-plugin-auth</artifactId>
        </dependency>

        <!-- 业务功能插件 -->
        <dependency>
            <groupId>com.znhb</groupId>
            <artifactId>znhb-plugin-biz</artifactId>
        </dependency>


        <!-- C端功能插件 -->
        <dependency>
            <groupId>com.znhb</groupId>
            <artifactId>znhb-plugin-client</artifactId>
        </dependency>

        <!-- 开发工具插件 -->
        <dependency>
            <groupId>com.znhb</groupId>
            <artifactId>znhb-plugin-dev</artifactId>
        </dependency>

        <!-- 代码生成插件 -->
        <dependency>
            <groupId>com.znhb</groupId>
            <artifactId>znhb-plugin-gen</artifactId>
        </dependency>

        <!-- 移动端管理插件 -->
        <dependency>
            <groupId>com.znhb</groupId>
            <artifactId>znhb-plugin-mobile</artifactId>
        </dependency>

        <!-- 系统功能插件 -->
        <dependency>
            <groupId>com.znhb</groupId>
            <artifactId>znhb-plugin-sys</artifactId>
        </dependency>

        <!-- 微信公众号插件 -->
        <dependency>
            <groupId>com.znhb</groupId>
            <artifactId>znhb-plugin-wechat</artifactId>
        </dependency>

        <!-- SnakeYAML 依赖 -->
        <dependency>
            <groupId>org.yaml</groupId>
            <artifactId>snakeyaml</artifactId>
            <version>${snakeyaml.version}</version>
        </dependency>

        <!-- Spring Boot Actuator 依赖 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <!-- 打包JAR文件，排除依赖和资源文件 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <version>3.2.0</version>
                <configuration>
                    <!-- 排除资源文件 -->
                    <excludes>
                        <exclude>application*.yml</exclude>
                        <exclude>logback-spring.xml</exclude>
                        <exclude>static/**</exclude>
                        <exclude>templates/**</exclude>
                        <exclude>*.properties</exclude>
                    </excludes>
                    <archive>
                        <manifest>
                            <addClasspath>true</addClasspath>
                            <!-- 依赖包路径前缀 -->
                            <classpathPrefix>lib/</classpathPrefix>
                            <mainClass>com.znhb.Application</mainClass>
                        </manifest>
                        <manifestEntries>
                            <!-- 资源文件路径前缀 -->
                            <Class-Path>resources/</Class-Path>
                        </manifestEntries>
                    </archive>
                    <outputDirectory>${project.basedir}/package</outputDirectory>
                </configuration>
            </plugin>

            <!-- 复制依赖到lib目录 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
                <version>3.2.0</version>
                <executions>
                    <execution>
                        <id>copy-dependencies</id>
                        <phase>package</phase>
                        <goals>
                            <goal>copy-dependencies</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>${project.basedir}/package/lib</outputDirectory>
                            <excludeTransitive>false</excludeTransitive>
                            <stripVersion>false</stripVersion>
                            <includeScope>runtime</includeScope>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

            <!-- 资源过滤插件 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <version>3.2.0</version>
                <configuration>
                    <encoding>UTF-8</encoding>
                    <useDefaultDelimiters>true</useDefaultDelimiters>
                    <nonFilteredFileExtensions>
                        <nonFilteredFileExtension>ttf</nonFilteredFileExtension>
                        <nonFilteredFileExtension>woff</nonFilteredFileExtension>
                        <nonFilteredFileExtension>woff2</nonFilteredFileExtension>
                        <nonFilteredFileExtension>eot</nonFilteredFileExtension>
                        <nonFilteredFileExtension>svg</nonFilteredFileExtension>
                        <nonFilteredFileExtension>jpg</nonFilteredFileExtension>
                        <nonFilteredFileExtension>png</nonFilteredFileExtension>
                        <nonFilteredFileExtension>gif</nonFilteredFileExtension>
                        <nonFilteredFileExtension>ico</nonFilteredFileExtension>
                        <nonFilteredFileExtension>jar</nonFilteredFileExtension>
                    </nonFilteredFileExtensions>
                </configuration>
            </plugin>

            <!-- 使用 maven-antrun-plugin 复制资源文件和创建启动脚本 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-antrun-plugin</artifactId>
                <version>3.0.0</version>
                <executions>
                    <!-- 清理项目根目录下的输出文件 -->
                    <execution>
                        <id>clean-root-dir</id>
                        <phase>clean</phase>
                        <goals>
                            <goal>run</goal>
                        </goals>
                        <configuration>
                            <target>
                                <!-- 删除JAR文件 -->
                                <delete file="${project.basedir}/package/${project.build.finalName}.jar"/>
                                <!-- 删除lib目录 -->
                                <delete dir="${project.basedir}/package/lib"/>
                                <!-- 删除resources目录 -->
                                <delete dir="${project.basedir}/package/resources"/>
                                <!-- 删除启动脚本 -->
                                <delete file="${project.basedir}/package/start.bat"/>
                                <delete file="${project.basedir}/package/start.sh"/>
                            </target>
                        </configuration>
                    </execution>
                    <execution>
                        <id>copy-resources-and-create-scripts</id>
                        <phase>package</phase>
                        <goals>
                            <goal>run</goal>
                        </goals>
                        <configuration>
                            <target>
                                <!-- 复制资源文件到项目根目录的resources目录 -->
                                <mkdir dir="${project.basedir}/package/resources"/>
                                <!-- 复制已经过滤的资源文件 -->
                                <copy todir="${project.basedir}/package/resources" overwrite="true">
                                    <fileset dir="${project.build.directory}/classes">
                                        <include name="**/*.yml"/>
                                        <include name="**/*.yaml"/>
                                        <include name="**/*.properties"/>
                                        <include name="**/*.xml"/>
                                    </fileset>
                                </copy>
                                <!-- 复制其他资源文件 -->
                                <copy todir="${project.basedir}/package/resources" overwrite="true">
                                    <fileset dir="${project.basedir}/src/main/resources">
                                        <exclude name="**/*.yml"/>
                                        <exclude name="**/*.yaml"/>
                                        <exclude name="**/*.properties"/>
                                        <exclude name="**/*.xml"/>
                                    </fileset>
                                </copy>
                            </target>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
