# 微信公众号集成方案 - ZNHB项目

## 📋 方案概述

本方案完全适配ZNHB项目的技术架构，基于现有的插件化设计模式，集成微信公众号功能。

## 🛠️ 技术版本适配

### 核心依赖版本
- **Spring Boot**: 2.5.12 (项目现有版本)
- **Spring Framework**: 5.3.26 (项目现有版本)
- **WxJava**: 4.4.0 (兼容Spring Boot 2.5.x)
- **Sa-Token**: 1.31.0 (项目现有版本)
- **MyBatis-Plus**: 项目现有版本

### 版本兼容性说明
- WxJava 4.4.0 完全兼容Spring Boot 2.5.12
- 支持JDK 1.8 (项目要求)
- 与现有Sa-Token权限框架无冲突
- 遵循项目现有的Swagger2配置模式

## 🏗️ 架构设计

### 模块结构
```
znhb-plugin-api/
└── znhb-plugin-wechat-api/          # 微信插件API接口
    ├── WechatConfigApi.java         # 配置API
    ├── WechatUserApi.java           # 用户API
    └── WechatMessageApi.java        # 消息API

znhb-plugin/
└── znhb-plugin-wechat/              # 微信插件实现
    ├── core/config/                 # 配置类
    ├── modular/config/              # 配置管理
    ├── modular/user/                # 用户管理
    ├── modular/message/             # 消息处理
    ├── modular/menu/                # 菜单管理
    └── modular/template/            # 模板消息
```

### 数据库设计
- `wx_user` - 微信用户表
- `wx_message` - 微信消息记录表
- `wx_menu` - 微信菜单表
- `wx_reply_rule` - 自动回复规则表

## 🚀 部署步骤

### 1. 依赖配置
已在根pom.xml中添加：
```xml
<weixin.java.version>4.4.0</weixin.java.version>
```

### 2. 数据库初始化
执行SQL脚本：
```sql
-- 位置：znhb-plugin/znhb-plugin-wechat/src/main/resources/db/migration/wechat_init.sql
```

### 3. 微信公众号配置
在dev_config表中配置：
- `WECHAT_MP_APP_ID`: 微信公众号AppId
- `WECHAT_MP_APP_SECRET`: 微信公众号AppSecret  
- `WECHAT_MP_TOKEN`: 微信公众号Token
- `WECHAT_MP_ENCODING_AES_KEY`: 微信公众号EncodingAESKey

### 4. 服务器配置
在微信公众号后台配置服务器地址：
```
URL: https://your-domain.com/wechat/message/receive
Token: znhb_wechat_token (或自定义)
```

## 🔧 功能特性

### 基础功能
- ✅ 微信消息接收与回复
- ✅ 用户关注/取消关注事件处理
- ✅ 自定义菜单管理
- ✅ 自动回复规则配置
- ✅ 用户信息同步与管理

### 高级功能
- ✅ 模板消息推送
- ✅ 用户账号绑定
- ✅ 消息记录存储
- ✅ 多媒体消息处理

### 业务集成
- 🔄 与现有用户系统绑定
- 🔄 车辆通行记录推送
- 🔄 系统通知推送
- 🔄 门禁状态查询

## 📱 API接口

### 消息处理接口
```
POST /wechat/message/receive  # 微信消息接收
GET  /wechat/message/receive  # 微信公众号验证
```

### 管理接口 (需要登录权限)
```
GET    /wechat/user/list      # 微信用户列表
POST   /wechat/user/bind      # 绑定系统用户
POST   /wechat/menu/create    # 创建菜单
POST   /wechat/message/send   # 发送消息
```

## 🔐 安全配置

### 权限控制
- 消息接收接口：无需登录 (微信回调)
- 管理接口：需要B端登录权限
- API接口：遵循Sa-Token权限控制

### 数据安全
- 微信用户信息加密存储
- 消息内容安全过滤
- 接口签名验证

## 🎯 使用示例

### 发送模板消息
```java
@Resource
private WechatMessageApi wechatMessageApi;

// 发送通行记录通知
WxMpTemplateMessage templateMessage = WxMpTemplateMessage.builder()
    .toUser(openId)
    .templateId("template_id")
    .data(templateData)
    .build();
    
wechatMessageApi.sendTemplateMessage(templateMessage);
```

### 用户绑定
```java
@Resource
private WechatUserApi wechatUserApi;

// 绑定微信用户与系统用户
wechatUserApi.bindUser(openId, userId);
```

## 📊 监控与日志

### 日志记录
- 微信消息接收日志
- 用户操作日志
- 错误异常日志
- 性能监控日志

### 数据统计
- 用户关注数量
- 消息处理量
- 功能使用统计
- 错误率监控

## 🔄 扩展功能

### 计划功能
- 微信支付集成
- 小程序授权登录
- 企业微信集成
- 多公众号管理

### 自定义开发
- 业务消息模板
- 自定义菜单功能
- 第三方系统集成
- 数据同步机制

## 📝 注意事项

1. **版本兼容性**: 严格按照项目现有版本选择依赖
2. **配置管理**: 使用项目现有的DevConfigApi进行配置管理
3. **权限控制**: 遵循项目Sa-Token权限体系
4. **代码规范**: 遵循项目现有代码规范和注释风格
5. **数据库**: 使用项目现有的MyBatis-Plus和动态数据源

## 🚀 快速启动

1. 执行数据库初始化脚本
2. 配置微信公众号参数
3. 重启应用服务
4. 配置微信公众号服务器地址
5. 测试消息收发功能

## 📞 技术支持

如有问题，请参考：
- WxJava官方文档: https://github.com/Wechat-Group/WxJava
- 项目现有插件开发模式
- Sa-Token权限框架文档
